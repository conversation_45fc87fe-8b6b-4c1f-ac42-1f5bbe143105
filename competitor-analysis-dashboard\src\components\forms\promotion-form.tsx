/**
 * 促销活动表单组件
 * 
 * 功能说明：
 * 1. 支持创建、编辑、查看三种模式
 * 2. 表单验证和错误处理
 * 3. 响应式设计
 * 4. 关联竞品和票种选择
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DialogTitle, DialogDescription, ScrollableDialogHeader, ScrollableDialogBody, ScrollableDialogFooter } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle, CheckCircle2, Calendar, DollarSign } from 'lucide-react';
import { Promotion, CreatePromotionInput, UpdatePromotionInput, Competitor, TicketType } from '@/types';

// ============================================================================
// 表单验证模式
// ============================================================================

const promotionSchema = z.object({
  competitor_id: z.number().min(1, '请选择竞品'),
  ticket_type_id: z.number().min(1, '请选择票种'),
  activity_name: z.string()
    .min(1, '活动名称不能为空')
    .max(200, '活动名称不能超过200个字符'),
  rack_rate: z.number()
    .min(0, '原价不能为负数')
    .optional()
    .nullable(),
  promo_price: z.number()
    .min(0, '促销价不能为负数')
    .optional()
    .nullable(),
  sale_start_date: z.string().optional().nullable(),
  sale_end_date: z.string().optional().nullable(),
  use_start_date: z.string().optional().nullable(),
  use_end_date: z.string().optional().nullable(),
  sales_channel: z.string()
    .max(100, '销售渠道不能超过100个字符')
    .optional()
    .nullable(),
  usage_rules: z.string()
    .max(500, '使用规则不能超过500个字符')
    .optional()
    .nullable(),
  data_source_url: z.string()
    .refine((val) => !val || /^https?:\/\/.+/.test(val), '请输入有效的URL')
    .refine((val) => !val || val.length <= 500, 'URL不能超过500个字符')
    .optional()
    .or(z.literal('')),
  remarks: z.string()
    .max(1000, '备注不能超过1000个字符')
    .optional()
    .nullable()
});

type PromotionFormData = z.infer<typeof promotionSchema>;

// ============================================================================
// 组件属性接口
// ============================================================================

interface PromotionFormProps {
  mode: 'create' | 'edit' | 'view';
  initialData?: Promotion;
  onSubmit: (data: CreatePromotionInput | UpdatePromotionInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  className?: string;
}

// ============================================================================
// 辅助函数
// ============================================================================

// 将日期转换为表单可用的字符串格式
const formatDateForInput = (date: string | Date | null | undefined): string => {
  if (!date) return '';
  if (typeof date === 'string') {
    return date.split('T')[0];
  }
  if (date instanceof Date) {
    return date.toISOString().split('T')[0];
  }
  return '';
};

// ============================================================================
// 促销活动表单组件实现
// ============================================================================

export const PromotionForm: React.FC<PromotionFormProps> = ({
  mode,
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  className
}) => {
  const [submitError, setSubmitError] = useState<string>('');
  const [submitSuccess, setSubmitSuccess] = useState<string>('');
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [ticketTypes, setTicketTypes] = useState<TicketType[]>([]);
  const [loadingOptions, setLoadingOptions] = useState(true);

  const isReadonly = mode === 'view';
  const isEdit = mode === 'edit';
  const isCreate = mode === 'create';

  // 表单配置
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch
  } = useForm<PromotionFormData>({
    resolver: zodResolver(promotionSchema),
    defaultValues: {
      competitor_id: initialData?.competitor_id || 0,
      ticket_type_id: initialData?.ticket_type_id || 0,
      activity_name: initialData?.activity_name || '',
      rack_rate: initialData?.rack_rate || null,
      promo_price: initialData?.promo_price || null,
      sale_start_date: formatDateForInput(initialData?.sale_start_date),
      sale_end_date: formatDateForInput(initialData?.sale_end_date),
      use_start_date: formatDateForInput(initialData?.use_start_date),
      use_end_date: formatDateForInput(initialData?.use_end_date),
      sales_channel: initialData?.sales_channel || '',
      usage_rules: initialData?.usage_rules || '',
      data_source_url: initialData?.data_source_url || '',
      remarks: initialData?.remarks || ''
    }
  });

  // 监听价格变化计算折扣
  const rackRate = watch('rack_rate');
  const promoPrice = watch('promo_price');

  // 加载竞品和票种选项
  useEffect(() => {
    const loadOptions = async () => {
      try {
        setLoadingOptions(true);
        
        // 并行加载竞品和票种
        const [competitorsRes, ticketTypesRes] = await Promise.all([
          fetch('/api/competitors?pageSize=1000'),
          fetch('/api/ticket-types?pageSize=1000')
        ]);

        const [competitorsResult, ticketTypesResult] = await Promise.all([
          competitorsRes.json(),
          ticketTypesRes.json()
        ]);

        if (competitorsResult.success) {
          setCompetitors(competitorsResult.data.data || []);
        }

        if (ticketTypesResult.success) {
          setTicketTypes(ticketTypesResult.data.data || []);
        }
      } catch (error) {
        console.error('加载选项失败:', error);
      } finally {
        setLoadingOptions(false);
      }
    };

    loadOptions();
  }, []);

  // 重置表单数据
  useEffect(() => {
    if (initialData) {
      reset({
        competitor_id: initialData.competitor_id,
        ticket_type_id: initialData.ticket_type_id,
        activity_name: initialData.activity_name,
        rack_rate: initialData.rack_rate,
        promo_price: initialData.promo_price,
        sale_start_date: formatDateForInput(initialData.sale_start_date),
        sale_end_date: formatDateForInput(initialData.sale_end_date),
        use_start_date: formatDateForInput(initialData.use_start_date),
        use_end_date: formatDateForInput(initialData.use_end_date),
        sales_channel: initialData.sales_channel || '',
        usage_rules: initialData.usage_rules || '',
        data_source_url: initialData.data_source_url || '',
        remarks: initialData.remarks || ''
      });
    }
  }, [initialData, reset]);

  // 表单提交处理
  const handleFormSubmit = async (data: PromotionFormData) => {
    try {
      setSubmitError('');
      setSubmitSuccess('');

      // 清理和转换数据
      const cleanedData = {
        ...data,
        rack_rate: data.rack_rate || null,
        promo_price: data.promo_price || null,
        sale_start_date: data.sale_start_date || null,
        sale_end_date: data.sale_end_date || null,
        use_start_date: data.use_start_date || null,
        use_end_date: data.use_end_date || null,
        sales_channel: data.sales_channel?.trim() || null,
        usage_rules: data.usage_rules?.trim() || null,
        data_source_url: data.data_source_url?.trim() || null,
        remarks: data.remarks?.trim() || null
      };

      await onSubmit(cleanedData);

      if (isCreate) {
        setSubmitSuccess('促销活动创建成功！');
        reset(); // 重置表单
      } else if (isEdit) {
        setSubmitSuccess('促销活动更新成功！');
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : '操作失败，请重试');
    }
  };

  // 计算折扣率
  const calculateDiscount = () => {
    if (rackRate && promoPrice && rackRate > 0) {
      const discount = ((rackRate - promoPrice) / rackRate * 100);
      return discount.toFixed(1);
    }
    return null;
  };

  // 获取表单标题
  const getTitle = () => {
    switch (mode) {
      case 'create':
        return '创建促销活动';
      case 'edit':
        return '编辑促销活动';
      case 'view':
        return '查看促销活动';
      default:
        return '促销活动信息';
    }
  };

  // 获取表单描述
  const getDescription = () => {
    switch (mode) {
      case 'create':
        return '填写促销活动详细信息，创建新的促销记录';
      case 'edit':
        return '修改促销活动信息，更新现有记录';
      case 'view':
        return '查看促销活动的详细信息';
      default:
        return '';
    }
  };

  return (
    <>
      <ScrollableDialogHeader>
        <DialogTitle>{getTitle()}</DialogTitle>
        <DialogDescription>{getDescription()}</DialogDescription>
      </ScrollableDialogHeader>

      <ScrollableDialogBody>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6" id="promotion-form">
        {/* 错误提示 */}
        {submitError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        {/* 成功提示 */}
        {submitSuccess && (
          <Alert className="border-green-200 bg-green-50 text-green-800">
            <CheckCircle2 className="h-4 w-4" />
            <AlertDescription>{submitSuccess}</AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 竞品选择 */}
            <div className="space-y-2">
              <Label htmlFor="competitor_id" className="text-sm font-medium">
                竞品 <span className="text-red-500">*</span>
              </Label>
              <Select
                disabled={isReadonly || loading || loadingOptions}
                onValueChange={(value) => setValue('competitor_id', parseInt(value))}
                defaultValue={initialData?.competitor_id?.toString()}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择竞品" />
                </SelectTrigger>
                <SelectContent>
                  {competitors.map((competitor) => (
                    <SelectItem key={competitor.competitor_id} value={competitor.competitor_id.toString()}>
                      {competitor.competitor_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.competitor_id && (
                <p className="text-sm text-red-500">{errors.competitor_id.message}</p>
              )}
            </div>

            {/* 票种选择 */}
            <div className="space-y-2">
              <Label htmlFor="ticket_type_id" className="text-sm font-medium">
                票种 <span className="text-red-500">*</span>
              </Label>
              <Select
                disabled={isReadonly || loading || loadingOptions}
                onValueChange={(value) => setValue('ticket_type_id', parseInt(value))}
                defaultValue={initialData?.ticket_type_id?.toString()}
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择票种" />
                </SelectTrigger>
                <SelectContent>
                  {ticketTypes.map((ticketType) => (
                    <SelectItem key={ticketType.ticket_type_id} value={ticketType.ticket_type_id.toString()}>
                      {ticketType.ticket_type_name}
                      {ticketType.category && ` (${ticketType.category})`}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.ticket_type_id && (
                <p className="text-sm text-red-500">{errors.ticket_type_id.message}</p>
              )}
            </div>
          </div>

          {/* 活动名称 */}
          <div className="space-y-2">
            <Label htmlFor="activity_name" className="text-sm font-medium">
              活动名称 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="activity_name"
              {...register('activity_name')}
              placeholder="请输入活动名称"
              disabled={isReadonly || loading}
              className={errors.activity_name ? 'border-red-500' : ''}
            />
            {errors.activity_name && (
              <p className="text-sm text-red-500">{errors.activity_name.message}</p>
            )}
          </div>

          {/* 价格信息 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="rack_rate" className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                原价 (¥)
              </Label>
              <Input
                id="rack_rate"
                type="number"
                step="0.01"
                min="0"
                {...register('rack_rate', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={isReadonly || loading}
                className={errors.rack_rate ? 'border-red-500' : ''}
              />
              {errors.rack_rate && (
                <p className="text-sm text-red-500">{errors.rack_rate.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="promo_price" className="text-sm font-medium flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                促销价 (¥)
              </Label>
              <Input
                id="promo_price"
                type="number"
                step="0.01"
                min="0"
                {...register('promo_price', { valueAsNumber: true })}
                placeholder="0.00"
                disabled={isReadonly || loading}
                className={errors.promo_price ? 'border-red-500' : ''}
              />
              {errors.promo_price && (
                <p className="text-sm text-red-500">{errors.promo_price.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium">折扣率</Label>
              <div className="h-10 px-3 py-2 border rounded-md bg-gray-50 flex items-center">
                <span className="text-sm text-gray-600">
                  {calculateDiscount() ? `${calculateDiscount()}%` : '-'}
                </span>
              </div>
            </div>
          </div>

          {/* 销售时间 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sale_start_date" className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                销售开始日期
              </Label>
              <Input
                id="sale_start_date"
                type="date"
                {...register('sale_start_date')}
                disabled={isReadonly || loading}
                className={errors.sale_start_date ? 'border-red-500' : ''}
              />
              {errors.sale_start_date && (
                <p className="text-sm text-red-500">{errors.sale_start_date.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="sale_end_date" className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                销售结束日期
              </Label>
              <Input
                id="sale_end_date"
                type="date"
                {...register('sale_end_date')}
                disabled={isReadonly || loading}
                className={errors.sale_end_date ? 'border-red-500' : ''}
              />
              {errors.sale_end_date && (
                <p className="text-sm text-red-500">{errors.sale_end_date.message}</p>
              )}
            </div>
          </div>

          {/* 使用时间 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="use_start_date" className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                使用开始日期
              </Label>
              <Input
                id="use_start_date"
                type="date"
                {...register('use_start_date')}
                disabled={isReadonly || loading}
                className={errors.use_start_date ? 'border-red-500' : ''}
              />
              {errors.use_start_date && (
                <p className="text-sm text-red-500">{errors.use_start_date.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="use_end_date" className="text-sm font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                使用结束日期
              </Label>
              <Input
                id="use_end_date"
                type="date"
                {...register('use_end_date')}
                disabled={isReadonly || loading}
                className={errors.use_end_date ? 'border-red-500' : ''}
              />
              {errors.use_end_date && (
                <p className="text-sm text-red-500">{errors.use_end_date.message}</p>
              )}
            </div>
          </div>

          {/* 销售渠道 */}
          <div className="space-y-2">
            <Label htmlFor="sales_channel" className="text-sm font-medium">
              销售渠道
            </Label>
            <Input
              id="sales_channel"
              {...register('sales_channel')}
              placeholder="例如：官网、APP、第三方平台等"
              disabled={isReadonly || loading}
              className={errors.sales_channel ? 'border-red-500' : ''}
            />
            {errors.sales_channel && (
              <p className="text-sm text-red-500">{errors.sales_channel.message}</p>
            )}
          </div>

          {/* 使用规则 */}
          <div className="space-y-2">
            <Label htmlFor="usage_rules" className="text-sm font-medium">
              使用规则
            </Label>
            <Textarea
              id="usage_rules"
              {...register('usage_rules')}
              placeholder="请输入使用规则和限制条件"
              disabled={isReadonly || loading}
              className={errors.usage_rules ? 'border-red-500' : ''}
              rows={3}
            />
            {errors.usage_rules && (
              <p className="text-sm text-red-500">{errors.usage_rules.message}</p>
            )}
          </div>

          {/* 数据来源URL */}
          <div className="space-y-2">
            <Label htmlFor="data_source_url" className="text-sm font-medium">
              数据来源URL
            </Label>
            <Input
              id="data_source_url"
              type="url"
              {...register('data_source_url')}
              placeholder="https://example.com/promotion"
              disabled={isReadonly || loading}
              className={errors.data_source_url ? 'border-red-500' : ''}
            />
            {errors.data_source_url && (
              <p className="text-sm text-red-500">{errors.data_source_url.message}</p>
            )}
          </div>

          {/* 备注 */}
          <div className="space-y-2">
            <Label htmlFor="remarks" className="text-sm font-medium">
              备注
            </Label>
            <Textarea
              id="remarks"
              {...register('remarks')}
              placeholder="请输入备注信息"
              disabled={isReadonly || loading}
              className={errors.remarks ? 'border-red-500' : ''}
              rows={3}
            />
            {errors.remarks && (
              <p className="text-sm text-red-500">{errors.remarks.message}</p>
            )}
          </div>

          {/* 只读模式显示额外信息 */}
          {isReadonly && initialData && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-600">活动ID</Label>
                <div className="text-sm text-gray-900">{initialData.promotion_id}</div>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-600">录入时间</Label>
                <div className="text-sm text-gray-900">
                  {initialData.entry_date ? new Date(initialData.entry_date).toLocaleString() : '-'}
                </div>
              </div>
            </div>
          )}
        </div>

        </form>
      </ScrollableDialogBody>

      <ScrollableDialogFooter>
        {!isReadonly && (
          <Button
            type="submit"
            form="promotion-form"
            disabled={loading || isSubmitting || loadingOptions}
            className="min-w-[100px]"
          >
            {(loading || isSubmitting) && (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            {isCreate ? '创建活动' : '保存更改'}
          </Button>
        )}
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading || isSubmitting}
        >
          {isReadonly ? '关闭' : '取消'}
        </Button>
      </ScrollableDialogFooter>
    </>
  );
};

// ============================================================================
// 导出组件
// ============================================================================

export default PromotionForm;
