{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/layout/mobile-nav.tsx"], "sourcesContent": ["/**\n * 移动端导航组件\n * \n * 功能说明：\n * 1. 专为移动端设计的导航菜单\n * 2. 底部标签栏导航\n * 3. 手势友好的交互设计\n * 4. 适配不同屏幕尺寸\n */\n\n'use client';\n\nimport React from 'react';\nimport { Home, Users, Ticket, Database, BarChart3, Settings } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\n// ============================================================================\n// 导航项配置\n// ============================================================================\n\ninterface NavItem {\n  key: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  href: string;\n  badge?: number;\n}\n\nconst navItems: NavItem[] = [\n  {\n    key: 'dashboard',\n    label: '首页',\n    icon: Home,\n    href: '/'\n  },\n  {\n    key: 'competitors',\n    label: '竞品',\n    icon: Users,\n    href: '/competitors'\n  },\n  {\n    key: 'ticket-types',\n    label: '票种',\n    icon: Ticket,\n    href: '/ticket-types'\n  },\n  {\n    key: 'promotions',\n    label: '促销',\n    icon: Database,\n    href: '/promotions'\n  },\n  {\n    key: 'analytics',\n    label: '分析',\n    icon: BarChart3,\n    href: '/analytics'\n  }\n];\n\n// ============================================================================\n// 移动端底部导航组件\n// ============================================================================\n\nexport interface MobileBottomNavProps {\n  currentPath?: string;\n  className?: string;\n}\n\nexport const MobileBottomNav: React.FC<MobileBottomNavProps> = ({\n  currentPath = '/',\n  className\n}) => {\n  return (\n    <nav className={cn(\n      'fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 md:hidden',\n      'safe-area-pb', // 适配iPhone底部安全区域\n      className\n    )}>\n      <div className=\"flex items-center justify-around px-2 py-1\">\n        {navItems.map((item) => {\n          const isActive = currentPath === item.href || \n            (item.href !== '/' && currentPath.startsWith(item.href));\n          const Icon = item.icon;\n\n          return (\n            <a\n              key={item.key}\n              href={item.href}\n              className={cn(\n                'flex flex-col items-center justify-center min-w-0 flex-1 px-1 py-2 text-xs transition-colors',\n                'active:bg-gray-100 rounded-lg',\n                isActive\n                  ? 'text-blue-600'\n                  : 'text-gray-600 hover:text-gray-900'\n              )}\n            >\n              <div className=\"relative\">\n                <Icon className={cn(\n                  'h-5 w-5 mb-1',\n                  isActive ? 'text-blue-600' : 'text-gray-400'\n                )} />\n                {item.badge && item.badge > 0 && (\n                  <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 99 ? '99+' : item.badge}\n                  </span>\n                )}\n              </div>\n              <span className={cn(\n                'truncate max-w-full',\n                isActive ? 'font-medium' : 'font-normal'\n              )}>\n                {item.label}\n              </span>\n            </a>\n          );\n        })}\n      </div>\n    </nav>\n  );\n};\n\n// ============================================================================\n// 移动端侧边抽屉导航组件\n// ============================================================================\n\nexport interface MobileDrawerNavProps {\n  open: boolean;\n  onClose: () => void;\n  currentPath?: string;\n  className?: string;\n}\n\nexport const MobileDrawerNav: React.FC<MobileDrawerNavProps> = ({\n  open,\n  onClose,\n  currentPath = '/',\n  className\n}) => {\n  return (\n    <>\n      {/* 遮罩层 */}\n      {open && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* 抽屉内容 */}\n      <div\n        className={cn(\n          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:hidden',\n          open ? 'translate-x-0' : '-translate-x-full',\n          className\n        )}\n      >\n        {/* 抽屉头部 */}\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">\n            竞品分析系统\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n          >\n            <span className=\"sr-only\">关闭菜单</span>\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n          {navItems.map((item) => {\n            const isActive = currentPath === item.href || \n              (item.href !== '/' && currentPath.startsWith(item.href));\n            const Icon = item.icon;\n\n            return (\n              <a\n                key={item.key}\n                href={item.href}\n                onClick={onClose}\n                className={cn(\n                  'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',\n                  isActive\n                    ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                )}\n              >\n                <Icon className=\"mr-3 h-5 w-5\" />\n                <span className=\"flex-1\">{item.label}</span>\n                {item.badge && item.badge > 0 && (\n                  <span className=\"ml-2 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 99 ? '99+' : item.badge}\n                  </span>\n                )}\n              </a>\n            );\n          })}\n        </nav>\n\n        {/* 抽屉底部 */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"text-xs text-gray-500 text-center\">\n            版本 1.0.0\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\n// ============================================================================\n// 移动端顶部导航栏组件\n// ============================================================================\n\nexport interface MobileTopNavProps {\n  title?: string;\n  onMenuClick?: () => void;\n  actions?: React.ReactNode;\n  className?: string;\n}\n\nexport const MobileTopNav: React.FC<MobileTopNavProps> = ({\n  title = '竞品分析系统',\n  onMenuClick,\n  actions,\n  className\n}) => {\n  return (\n    <header className={cn(\n      'sticky top-0 z-30 bg-white border-b border-gray-200 md:hidden',\n      className\n    )}>\n      <div className=\"flex items-center justify-between h-14 px-4\">\n        {/* 左侧菜单按钮 */}\n        <button\n          onClick={onMenuClick}\n          className=\"p-2 -ml-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n        >\n          <span className=\"sr-only\">打开菜单</span>\n          <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n          </svg>\n        </button>\n\n        {/* 中间标题 */}\n        <h1 className=\"text-lg font-semibold text-gray-900 truncate flex-1 mx-4 text-center\">\n          {title}\n        </h1>\n\n        {/* 右侧操作按钮 */}\n        <div className=\"flex items-center space-x-2\">\n          {actions}\n        </div>\n      </div>\n    </header>\n  );\n};\n\n// ============================================================================\n// 移动端浮动操作按钮组件\n// ============================================================================\n\nexport interface MobileFABProps {\n  onClick?: () => void;\n  icon?: React.ComponentType<{ className?: string }>;\n  label?: string;\n  className?: string;\n}\n\nexport const MobileFAB: React.FC<MobileFABProps> = ({\n  onClick,\n  icon: Icon = Database,\n  label = '添加',\n  className\n}) => {\n  return (\n    <button\n      onClick={onClick}\n      className={cn(\n        'fixed bottom-20 right-4 z-40 md:hidden',\n        'h-14 w-14 bg-blue-600 text-white rounded-full shadow-lg',\n        'flex items-center justify-center',\n        'hover:bg-blue-700 active:bg-blue-800',\n        'transition-colors duration-200',\n        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n        className\n      )}\n    >\n      <Icon className=\"h-6 w-6\" />\n      <span className=\"sr-only\">{label}</span>\n    </button>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  MobileBottomNav as default,\n  MobileDrawerNav,\n  MobileTopNav,\n  MobileFAB\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAkBA,MAAM,WAAsB;IAC1B;QACE,KAAK;QACL,OAAO;QACP,MAAM,mMAAA,CAAA,OAAI;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,sMAAA,CAAA,SAAM;QACZ,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,0MAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,kNAAA,CAAA,YAAS;QACf,MAAM;IACR;CACD;AAWM,MAAM,kBAAkD,CAAC,EAC9D,cAAc,GAAG,EACjB,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kFACA,gBACA;kBAEA,cAAA,8OAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,WAAW,gBAAgB,KAAK,IAAI,IACvC,KAAK,IAAI,KAAK,OAAO,YAAY,UAAU,CAAC,KAAK,IAAI;gBACxD,MAAM,OAAO,KAAK,IAAI;gBAEtB,qBACE,8OAAC;oBAEC,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gGACA,iCACA,WACI,kBACA;;sCAGN,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,gBACA,WAAW,kBAAkB;;;;;;gCAE9B,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,8OAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;;;;;;sCAI3C,8OAAC;4BAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,WAAW,gBAAgB;sCAE1B,KAAK,KAAK;;;;;;;mBAzBR,KAAK,GAAG;;;;;YA6BnB;;;;;;;;;;;AAIR;AAaO,MAAM,kBAAkD,CAAC,EAC9D,IAAI,EACJ,OAAO,EACP,cAAc,GAAG,EACjB,SAAS,EACV;IACC,qBACE;;YAEG,sBACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yHACA,OAAO,kBAAkB,qBACzB;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,gBAAgB,KAAK,IAAI,IACvC,KAAK,IAAI,KAAK,OAAO,YAAY,UAAU,CAAC,KAAK,IAAI;4BACxD,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,8OAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,SAAS;gCACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,yDACA;;kDAGN,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAU,KAAK,KAAK;;;;;;oCACnC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;+BAdpC,KAAK,GAAG;;;;;wBAmBnB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;AAO7D;AAaO,MAAM,eAA4C,CAAC,EACxD,QAAQ,QAAQ,EAChB,WAAW,EACX,OAAO,EACP,SAAS,EACV;IACC,qBACE,8OAAC;QAAO,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAClB,iEACA;kBAEA,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,8OAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,8OAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCAC9D,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;8BAKzE,8OAAC;oBAAG,WAAU;8BACX;;;;;;8BAIH,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;AAaO,MAAM,YAAsC,CAAC,EAClD,OAAO,EACP,MAAM,OAAO,0MAAA,CAAA,WAAQ,EACrB,QAAQ,IAAI,EACZ,SAAS,EACV;IACC,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,2DACA,oCACA,wCACA,kCACA,2EACA;;0BAGF,8OAAC;gBAAK,WAAU;;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BAAW;;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 444, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/layout/main-layout.tsx"], "sourcesContent": ["/**\n * 主布局组件\n * \n * 功能说明：\n * 1. 提供应用程序的主要布局结构\n * 2. 包含导航栏、侧边栏、主内容区域\n * 3. 响应式设计，支持移动端\n * 4. 可配置的布局选项\n */\n\nimport React, { useState } from 'react';\nimport { Menu, X, Home, Database, BarChart3, Settings, Users, Ticket } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { MobileBottomNav, MobileDrawerNav, MobileTopNav } from './mobile-nav';\n\n// ============================================================================\n// 导航菜单配置\n// ============================================================================\n\nexport interface MenuItem {\n  key: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  href: string;\n  children?: MenuItem[];\n}\n\nconst defaultMenuItems: MenuItem[] = [\n  {\n    key: 'dashboard',\n    label: '仪表板',\n    icon: Home,\n    href: '/'\n  },\n  {\n    key: 'competitors',\n    label: '竞品管理',\n    icon: Users,\n    href: '/competitors'\n  },\n  {\n    key: 'ticket-types',\n    label: '票种管理',\n    icon: Ticket,\n    href: '/ticket-types'\n  },\n  {\n    key: 'promotions',\n    label: '促销活动',\n    icon: Database,\n    href: '/promotions'\n  },\n  {\n    key: 'analytics',\n    label: '数据分析',\n    icon: BarChart3,\n    href: '/analytics'\n  },\n  {\n    key: 'settings',\n    label: '系统设置',\n    icon: Settings,\n    href: '/settings'\n  }\n];\n\n// ============================================================================\n// 布局组件接口\n// ============================================================================\n\nexport interface MainLayoutProps {\n  children: React.ReactNode;\n  menuItems?: MenuItem[];\n  currentPath?: string;\n  title?: string;\n  className?: string;\n}\n\n// ============================================================================\n// 主布局组件实现\n// ============================================================================\n\nexport const MainLayout: React.FC<MainLayoutProps> = ({\n  children,\n  menuItems = defaultMenuItems,\n  currentPath = '/',\n  title = '竞品分析管理系统',\n  className\n}) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // 渲染菜单项\n  const renderMenuItem = (item: MenuItem) => {\n    const isActive = currentPath === item.href || currentPath.startsWith(item.href + '/');\n    const Icon = item.icon;\n\n    return (\n      <a\n        key={item.key}\n        href={item.href}\n        className={cn(\n          'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',\n          isActive\n            ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n        )}\n        onClick={() => setSidebarOpen(false)}\n      >\n        <Icon className=\"mr-3 h-5 w-5\" />\n        {item.label}\n      </a>\n    );\n  };\n\n  return (\n    <div className={cn('min-h-screen bg-gray-50', className)}>\n      {/* 移动端导航组件 */}\n      <MobileDrawerNav\n        open={sidebarOpen}\n        onClose={() => setSidebarOpen(false)}\n        currentPath={currentPath}\n      />\n\n      {/* 桌面端侧边栏 */}\n      <div\n        className={cn(\n          'hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:w-64 lg:bg-white lg:shadow-lg lg:block'\n        )}\n      >\n        {/* 侧边栏头部 */}\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n          <h1 className=\"text-lg font-semibold text-gray-900 truncate\">\n            {title}\n          </h1>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          >\n            <X className=\"h-5 w-5\" />\n          </Button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n          {menuItems.map(renderMenuItem)}\n        </nav>\n\n        {/* 侧边栏底部 */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"text-xs text-gray-500 text-center\">\n            版本 1.0.0\n          </div>\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div className=\"lg:pl-64\">\n        {/* 移动端顶部导航 */}\n        <MobileTopNav\n          title={menuItems.find(item => item.href === currentPath)?.label || '首页'}\n          onMenuClick={() => setSidebarOpen(true)}\n        />\n\n        {/* 桌面端顶部导航栏 */}\n        <header className=\"hidden lg:block bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\n\n            {/* 页面标题 */}\n            <div className=\"flex-1 lg:flex-none\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">\n                {/* 这里可以根据当前路径动态显示页面标题 */}\n                {menuItems.find(item => item.href === currentPath)?.label || '首页'}\n              </h2>\n            </div>\n\n            {/* 用户操作区域 */}\n            <div className=\"flex items-center space-x-4\">\n              {/* 通知按钮 */}\n              <Button variant=\"ghost\" size=\"sm\">\n                <span className=\"sr-only\">通知</span>\n                <div className=\"h-5 w-5 rounded-full bg-gray-300\" />\n              </Button>\n\n              {/* 用户头像 */}\n              <Button variant=\"ghost\" size=\"sm\">\n                <span className=\"sr-only\">用户菜单</span>\n                <div className=\"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium\">\n                  U\n                </div>\n              </Button>\n            </div>\n          </div>\n        </header>\n\n        {/* 主内容 */}\n        <main className=\"flex-1 pb-16 lg:pb-0\">\n          <div className=\"p-4 sm:p-6 lg:p-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n\n      {/* 移动端底部导航 */}\n      <MobileBottomNav currentPath={currentPath} />\n    </div>\n  );\n};\n\n// ============================================================================\n// 页面容器组件\n// ============================================================================\n\nexport interface PageContainerProps {\n  title?: string;\n  description?: string;\n  actions?: React.ReactNode;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const PageContainer: React.FC<PageContainerProps> = ({\n  title,\n  description,\n  actions,\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('space-y-6', className)}>\n      {/* 页面头部 */}\n      {(title || description || actions) && (\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div>\n            {title && (\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                {title}\n              </h1>\n            )}\n            {description && (\n              <p className=\"mt-1 text-sm text-gray-600\">\n                {description}\n              </p>\n            )}\n          </div>\n          {actions && (\n            <div className=\"flex gap-2\">\n              {actions}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 页面内容 */}\n      <div>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 卡片容器组件\n// ============================================================================\n\nexport interface CardContainerProps {\n  title?: string;\n  description?: string;\n  actions?: React.ReactNode;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContainer: React.FC<CardContainerProps> = ({\n  title,\n  description,\n  actions,\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('bg-white rounded-lg shadow border border-gray-200', className)}>\n      {/* 卡片头部 */}\n      {(title || description || actions) && (\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              {title && (\n                <h3 className=\"text-lg font-medium text-gray-900\">\n                  {title}\n                </h3>\n              )}\n              {description && (\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  {description}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex gap-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* 卡片内容 */}\n      <div className=\"p-6\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  MainLayout as default,\n  PageContainer,\n  CardContainer\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAcA,MAAM,mBAA+B;IACnC;QACE,KAAK;QACL,OAAO;QACP,MAAM,mMAAA,CAAA,OAAI;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,sMAAA,CAAA,SAAM;QACZ,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,0MAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,kNAAA,CAAA,YAAS;QACf,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,0MAAA,CAAA,WAAQ;QACd,MAAM;IACR;CACD;AAkBM,MAAM,aAAwC,CAAC,EACpD,QAAQ,EACR,YAAY,gBAAgB,EAC5B,cAAc,GAAG,EACjB,QAAQ,UAAU,EAClB,SAAS,EACV;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,QAAQ;IACR,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,gBAAgB,KAAK,IAAI,IAAI,YAAY,UAAU,CAAC,KAAK,IAAI,GAAG;QACjF,MAAM,OAAO,KAAK,IAAI;QAEtB,qBACE,8OAAC;YAEC,MAAM,KAAK,IAAI;YACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,yDACA;YAEN,SAAS,IAAM,eAAe;;8BAE9B,8OAAC;oBAAK,WAAU;;;;;;gBACf,KAAK,KAAK;;WAXN,KAAK,GAAG;;;;;IAcnB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAE5C,8OAAC,6IAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,SAAS,IAAM,eAAe;gBAC9B,aAAa;;;;;;0BAIf,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;kCAIF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;0BAOvD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,6IAAA,CAAA,eAAY;wBACX,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,cAAc,SAAS;wBACnE,aAAa,IAAM,eAAe;;;;;;kCAIpC,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CAGb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAEX,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,cAAc,SAAS;;;;;;;;;;;8CAKjE,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;8DAAmG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1H,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAMP,8OAAC,6IAAA,CAAA,kBAAe;gBAAC,aAAa;;;;;;;;;;;;AAGpC;AAcO,MAAM,gBAA8C,CAAC,EAC1D,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAE7B,CAAC,SAAS,eAAe,OAAO,mBAC/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;4BACE,uBACC,8OAAC;gCAAG,WAAU;0CACX;;;;;;4BAGJ,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;oBAIN,yBACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAOT,8OAAC;0BACE;;;;;;;;;;;;AAIT;AAcO,MAAM,gBAA8C,CAAC,EAC1D,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;;YAErE,CAAC,SAAS,eAAe,OAAO,mBAC/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;gCACE,uBACC,8OAAC;oCAAG,WAAU;8CACX;;;;;;gCAGJ,6BACC,8OAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;wBAIN,yBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAQX,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 886, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 910, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/loading.tsx"], "sourcesContent": ["/**\n * 加载组件\n * \n * 功能说明：\n * 1. 提供多种加载状态的显示\n * 2. 支持不同尺寸和样式\n * 3. 可配置加载文本和动画\n */\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\n// ============================================================================\n// 加载动画组件\n// ============================================================================\n\n/**\n * 旋转加载器组件\n */\nexport interface SpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const Spinner: React.FC<SpinnerProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n};\n\n/**\n * 脉冲加载器组件\n */\nexport interface PulseProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const Pulse: React.FC<PulseProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-2 h-2',\n    md: 'w-3 h-3',\n    lg: 'w-4 h-4'\n  };\n\n  return (\n    <div className={cn('flex space-x-1', className)}>\n      {[0, 1, 2].map((i) => (\n        <div\n          key={i}\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n          style={{\n            animationDelay: `${i * 0.2}s`,\n            animationDuration: '1s'\n          }}\n        />\n      ))}\n    </div>\n  );\n};\n\n// ============================================================================\n// 加载状态组件\n// ============================================================================\n\n/**\n * 内联加载组件\n */\nexport interface LoadingInlineProps {\n  text?: string;\n  size?: 'sm' | 'md' | 'lg';\n  type?: 'spinner' | 'pulse';\n  className?: string;\n}\n\nexport const LoadingInline: React.FC<LoadingInlineProps> = ({\n  text = '加载中...',\n  size = 'md',\n  type = 'spinner',\n  className\n}) => {\n  const LoaderComponent = type === 'spinner' ? Spinner : Pulse;\n\n  return (\n    <div className={cn('flex items-center space-x-2', className)}>\n      <LoaderComponent size={size} />\n      {text && (\n        <span className=\"text-sm text-gray-600 animate-pulse\">\n          {text}\n        </span>\n      )}\n    </div>\n  );\n};\n\n/**\n * 页面加载组件\n */\nexport interface LoadingPageProps {\n  text?: string;\n  description?: string;\n  className?: string;\n}\n\nexport const LoadingPage: React.FC<LoadingPageProps> = ({\n  text = '加载中...',\n  description,\n  className\n}) => {\n  return (\n    <div className={cn(\n      'flex flex-col items-center justify-center min-h-[400px] space-y-4',\n      className\n    )}>\n      <Spinner size=\"lg\" />\n      <div className=\"text-center space-y-2\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          {text}\n        </h3>\n        {description && (\n          <p className=\"text-sm text-gray-500 max-w-sm\">\n            {description}\n          </p>\n        )}\n      </div>\n    </div>\n  );\n};\n\n/**\n * 覆盖层加载组件\n */\nexport interface LoadingOverlayProps {\n  visible: boolean;\n  text?: string;\n  className?: string;\n  children?: React.ReactNode;\n}\n\nexport const LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  visible,\n  text = '处理中...',\n  className,\n  children\n}) => {\n  if (!visible) {\n    return <>{children}</>;\n  }\n\n  return (\n    <div className={cn('relative', className)}>\n      {children}\n      <div className=\"absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6 flex flex-col items-center space-y-4\">\n          <Spinner size=\"lg\" />\n          <p className=\"text-sm font-medium text-gray-900\">\n            {text}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n/**\n * 表格行加载组件\n */\nexport interface LoadingTableRowProps {\n  columns: number;\n  rows?: number;\n  className?: string;\n}\n\nexport const LoadingTableRow: React.FC<LoadingTableRowProps> = ({\n  columns,\n  rows = 5,\n  className\n}) => {\n  return (\n    <>\n      {Array.from({ length: rows }).map((_, rowIndex) => (\n        <tr key={rowIndex} className={className}>\n          {Array.from({ length: columns }).map((_, colIndex) => (\n            <td key={colIndex} className=\"px-4 py-3\">\n              <div className=\"h-4 bg-gray-200 rounded animate-pulse\" />\n            </td>\n          ))}\n        </tr>\n      ))}\n    </>\n  );\n};\n\n/**\n * 卡片加载组件\n */\nexport interface LoadingCardProps {\n  className?: string;\n}\n\nexport const LoadingCard: React.FC<LoadingCardProps> = ({ className }) => {\n  return (\n    <div className={cn(\n      'border rounded-lg p-6 space-y-4 animate-pulse',\n      className\n    )}>\n      <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n      <div className=\"space-y-2\">\n        <div className=\"h-3 bg-gray-200 rounded\" />\n        <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n      </div>\n      <div className=\"flex space-x-2\">\n        <div className=\"h-6 bg-gray-200 rounded w-16\" />\n        <div className=\"h-6 bg-gray-200 rounded w-20\" />\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 按钮加载状态组件\n// ============================================================================\n\n/**\n * 带加载状态的按钮组件\n */\nexport interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  loading?: boolean;\n  loadingText?: string;\n  children: React.ReactNode;\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nexport const LoadingButton: React.FC<LoadingButtonProps> = ({\n  loading = false,\n  loadingText,\n  children,\n  disabled,\n  className,\n  ...props\n}) => {\n  return (\n    <button\n      {...props}\n      disabled={disabled || loading}\n      className={cn(\n        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',\n        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n        'disabled:pointer-events-none disabled:opacity-50',\n        'bg-primary text-primary-foreground hover:bg-primary/90',\n        'h-10 px-4 py-2',\n        className\n      )}\n    >\n      {loading && (\n        <Spinner size=\"sm\" className=\"mr-2\" />\n      )}\n      {loading && loadingText ? loadingText : children}\n    </button>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  Spinner,\n  Pulse,\n  LoadingInline,\n  LoadingPage,\n  LoadingOverlay,\n  LoadingTableRow,\n  LoadingCard,\n  LoadingButton\n};\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;AAGD;;;AAcO,MAAM,UAAkC,CAAC,EAC9C,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;AAUO,MAAM,QAA8B,CAAC,EAC1C,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;gBAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;gBAEnB,OAAO;oBACL,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;oBAC7B,mBAAmB;gBACrB;eARK;;;;;;;;;;AAaf;AAgBO,MAAM,gBAA8C,CAAC,EAC1D,OAAO,QAAQ,EACf,OAAO,IAAI,EACX,OAAO,SAAS,EAChB,SAAS,EACV;IACC,MAAM,kBAAkB,SAAS,YAAY,UAAU;IAEvD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;0BAChD,8OAAC;gBAAgB,MAAM;;;;;;YACtB,sBACC,8OAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAKX;AAWO,MAAM,cAA0C,CAAC,EACtD,OAAO,QAAQ,EACf,WAAW,EACX,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,qEACA;;0BAEA,8OAAC;gBAAQ,MAAK;;;;;;0BACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX;;;;;;oBAEF,6BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;AAYO,MAAM,iBAAgD,CAAC,EAC5D,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,QAAQ,EACT;IACC,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B;0BACD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAQ,MAAK;;;;;;sCACd,8OAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAMb;AAWO,MAAM,kBAAkD,CAAC,EAC9D,OAAO,EACP,OAAO,CAAC,EACR,SAAS,EACV;IACC,qBACE;kBACG,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,8OAAC;gBAAkB,WAAW;0BAC3B,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,8OAAC;wBAAkB,WAAU;kCAC3B,cAAA,8OAAC;4BAAI,WAAU;;;;;;uBADR;;;;;eAFJ;;;;;;AAUjB;AASO,MAAM,cAA0C,CAAC,EAAE,SAAS,EAAE;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,iDACA;;0BAEA,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;AAiBO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACE,GAAG,KAAK;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA,uGACA,oDACA,0DACA,kBACA;;YAGD,yBACC,8OAAC;gBAAQ,MAAK;gBAAK,WAAU;;;;;;YAE9B,WAAW,cAAc,cAAc;;;;;;;AAG9C", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1479, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/pagination.tsx"], "sourcesContent": ["/**\n * 分页组件\n * \n * 功能说明：\n * 1. 提供完整的分页功能\n * 2. 支持页码跳转和页面大小选择\n * 3. 响应式设计，适配移动端\n * 4. 可配置显示样式和行为\n */\n\nimport React from 'react';\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';\n\n// ============================================================================\n// 分页组件接口定义\n// ============================================================================\n\nexport interface PaginationProps {\n  currentPage: number;          // 当前页码\n  totalPages: number;           // 总页数\n  totalItems: number;           // 总记录数\n  pageSize: number;             // 每页记录数\n  onPageChange: (page: number) => void;        // 页码变更回调\n  onPageSizeChange?: (pageSize: number) => void; // 页面大小变更回调\n  showPageSizeSelector?: boolean;               // 是否显示页面大小选择器\n  pageSizeOptions?: number[];                   // 页面大小选项\n  showTotal?: boolean;                          // 是否显示总数信息\n  showQuickJumper?: boolean;                    // 是否显示快速跳转\n  className?: string;\n}\n\n// ============================================================================\n// 分页组件实现\n// ============================================================================\n\nexport const Pagination: React.FC<PaginationProps> = ({\n  currentPage,\n  totalPages,\n  totalItems,\n  pageSize,\n  onPageChange,\n  onPageSizeChange,\n  showPageSizeSelector = true,\n  pageSizeOptions = [10, 20, 50, 100],\n  showTotal = true,\n  showQuickJumper = false,\n  className\n}) => {\n  // 计算显示的页码范围\n  const getVisiblePages = (): (number | 'ellipsis')[] => {\n    const delta = 2; // 当前页前后显示的页数\n    const range: (number | 'ellipsis')[] = [];\n    const rangeWithDots: (number | 'ellipsis')[] = [];\n\n    // 总是显示第一页\n    range.push(1);\n\n    // 计算当前页周围的页码\n    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {\n      range.push(i);\n    }\n\n    // 总是显示最后一页（如果总页数大于1）\n    if (totalPages > 1) {\n      range.push(totalPages);\n    }\n\n    // 添加省略号\n    let prev = 0;\n    for (const page of range) {\n      if (typeof page === 'number') {\n        if (page - prev === 2) {\n          rangeWithDots.push(prev + 1);\n        } else if (page - prev !== 1) {\n          rangeWithDots.push('ellipsis');\n        }\n        rangeWithDots.push(page);\n        prev = page;\n      }\n    }\n\n    return rangeWithDots;\n  };\n\n  const visiblePages = getVisiblePages();\n\n  // 计算当前页显示的记录范围\n  const startItem = (currentPage - 1) * pageSize + 1;\n  const endItem = Math.min(currentPage * pageSize, totalItems);\n\n  // 快速跳转处理\n  const handleQuickJump = (event: React.KeyboardEvent<HTMLInputElement>) => {\n    if (event.key === 'Enter') {\n      const target = event.target as HTMLInputElement;\n      const page = parseInt(target.value);\n      if (page >= 1 && page <= totalPages) {\n        onPageChange(page);\n        target.value = '';\n      }\n    }\n  };\n\n  return (\n    <div className={cn('flex flex-col sm:flex-row items-center justify-between gap-4', className)}>\n      {/* 总数信息 */}\n      {showTotal && (\n        <div className=\"text-sm text-gray-700 order-2 sm:order-1\">\n          显示第 <span className=\"font-medium\">{startItem}</span> 到{' '}\n          <span className=\"font-medium\">{endItem}</span> 条，共{' '}\n          <span className=\"font-medium\">{totalItems}</span> 条记录\n        </div>\n      )}\n\n      {/* 分页控件 */}\n      <div className=\"flex items-center gap-2 order-1 sm:order-2\">\n        {/* 页面大小选择器 */}\n        {showPageSizeSelector && onPageSizeChange && (\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">每页</span>\n            <Select\n              value={pageSize.toString()}\n              onValueChange={(value) => onPageSizeChange(parseInt(value))}\n            >\n              <SelectTrigger className=\"w-20\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {pageSizeOptions.map((size) => (\n                  <SelectItem key={size} value={size.toString()}>\n                    {size}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">条</span>\n          </div>\n        )}\n\n        {/* 页码导航 */}\n        <div className=\"flex items-center gap-1\">\n          {/* 上一页按钮 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(currentPage - 1)}\n            disabled={currentPage <= 1}\n            className=\"h-8 w-8 p-0\"\n          >\n            <ChevronLeft className=\"h-4 w-4\" />\n          </Button>\n\n          {/* 页码按钮 */}\n          {visiblePages.map((page, index) => {\n            if (page === 'ellipsis') {\n              return (\n                <div key={`ellipsis-${index}`} className=\"flex h-8 w-8 items-center justify-center\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </div>\n              );\n            }\n\n            return (\n              <Button\n                key={page}\n                variant={page === currentPage ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => onPageChange(page)}\n                className=\"h-8 w-8 p-0\"\n              >\n                {page}\n              </Button>\n            );\n          })}\n\n          {/* 下一页按钮 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(currentPage + 1)}\n            disabled={currentPage >= totalPages}\n            className=\"h-8 w-8 p-0\"\n          >\n            <ChevronRight className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        {/* 快速跳转 */}\n        {showQuickJumper && (\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">跳转到</span>\n            <input\n              type=\"number\"\n              min={1}\n              max={totalPages}\n              placeholder=\"页码\"\n              onKeyDown={handleQuickJump}\n              className=\"w-16 h-8 px-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">页</span>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 简化版分页组件\n// ============================================================================\n\nexport interface SimplePaginationProps {\n  currentPage: number;\n  totalPages: number;\n  onPageChange: (page: number) => void;\n  className?: string;\n}\n\nexport const SimplePagination: React.FC<SimplePaginationProps> = ({\n  currentPage,\n  totalPages,\n  onPageChange,\n  className\n}) => {\n  return (\n    <div className={cn('flex items-center justify-center gap-2', className)}>\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => onPageChange(currentPage - 1)}\n        disabled={currentPage <= 1}\n      >\n        <ChevronLeft className=\"h-4 w-4 mr-1\" />\n        上一页\n      </Button>\n\n      <span className=\"text-sm text-gray-700 px-4\">\n        第 {currentPage} 页，共 {totalPages} 页\n      </span>\n\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => onPageChange(currentPage + 1)}\n        disabled={currentPage >= totalPages}\n      >\n        下一页\n        <ChevronRight className=\"h-4 w-4 ml-1\" />\n      </Button>\n    </div>\n  );\n};\n\n// ============================================================================\n// 移动端分页组件\n// ============================================================================\n\nexport interface MobilePaginationProps {\n  currentPage: number;\n  totalPages: number;\n  onPageChange: (page: number) => void;\n  className?: string;\n}\n\nexport const MobilePagination: React.FC<MobilePaginationProps> = ({\n  currentPage,\n  totalPages,\n  onPageChange,\n  className\n}) => {\n  return (\n    <div className={cn('flex flex-col gap-3', className)}>\n      {/* 页码信息 */}\n      <div className=\"text-center text-sm text-gray-700\">\n        第 {currentPage} 页，共 {totalPages} 页\n      </div>\n\n      {/* 导航按钮 */}\n      <div className=\"flex gap-2\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => onPageChange(currentPage - 1)}\n          disabled={currentPage <= 1}\n          className=\"flex-1\"\n        >\n          <ChevronLeft className=\"h-4 w-4 mr-1\" />\n          上一页\n        </Button>\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => onPageChange(currentPage + 1)}\n          disabled={currentPage >= totalPages}\n          className=\"flex-1\"\n        >\n          下一页\n          <ChevronRight className=\"h-4 w-4 ml-1\" />\n        </Button>\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  Pagination as default,\n  SimplePagination,\n  MobilePagination\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAGD;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAwBO,MAAM,aAAwC,CAAC,EACpD,WAAW,EACX,UAAU,EACV,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,gBAAgB,EAChB,uBAAuB,IAAI,EAC3B,kBAAkB;IAAC;IAAI;IAAI;IAAI;CAAI,EACnC,YAAY,IAAI,EAChB,kBAAkB,KAAK,EACvB,SAAS,EACV;IACC,YAAY;IACZ,MAAM,kBAAkB;QACtB,MAAM,QAAQ,GAAG,aAAa;QAC9B,MAAM,QAAiC,EAAE;QACzC,MAAM,gBAAyC,EAAE;QAEjD,UAAU;QACV,MAAM,IAAI,CAAC;QAEX,aAAa;QACb,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,QAAQ,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,QAAQ,IAAK;YACtG,MAAM,IAAI,CAAC;QACb;QAEA,qBAAqB;QACrB,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,QAAQ;QACR,IAAI,OAAO;QACX,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,OAAO,SAAS,GAAG;oBACrB,cAAc,IAAI,CAAC,OAAO;gBAC5B,OAAO,IAAI,OAAO,SAAS,GAAG;oBAC5B,cAAc,IAAI,CAAC;gBACrB;gBACA,cAAc,IAAI,CAAC;gBACnB,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,eAAe;IACf,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,WAAW;IACjD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,UAAU;IAEjD,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,SAAS;YACzB,MAAM,SAAS,MAAM,MAAM;YAC3B,MAAM,OAAO,SAAS,OAAO,KAAK;YAClC,IAAI,QAAQ,KAAK,QAAQ,YAAY;gBACnC,aAAa;gBACb,OAAO,KAAK,GAAG;YACjB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gEAAgE;;YAEhF,2BACC,8OAAC;gBAAI,WAAU;;oBAA2C;kCACpD,8OAAC;wBAAK,WAAU;kCAAe;;;;;;oBAAiB;oBAAG;kCACvD,8OAAC;wBAAK,WAAU;kCAAe;;;;;;oBAAe;oBAAK;kCACnD,8OAAC;wBAAK,WAAU;kCAAe;;;;;;oBAAkB;;;;;;;0BAKrD,8OAAC;gBAAI,WAAU;;oBAEZ,wBAAwB,kCACvB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,8OAAC,kIAAA,CAAA,SAAM;gCACL,OAAO,SAAS,QAAQ;gCACxB,eAAe,CAAC,QAAU,iBAAiB,SAAS;;kDAEpD,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,8OAAC,kIAAA,CAAA,gBAAa;kDACX,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,kIAAA,CAAA,aAAU;gDAAY,OAAO,KAAK,QAAQ;0DACxC;+CADc;;;;;;;;;;;;;;;;0CAMvB,8OAAC;gCAAK,WAAU;0CAA0C;;;;;;;;;;;;kCAK9D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,cAAc;gCAC1C,UAAU,eAAe;gCACzB,WAAU;0CAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;4BAIxB,aAAa,GAAG,CAAC,CAAC,MAAM;gCACvB,IAAI,SAAS,YAAY;oCACvB,qBACE,8OAAC;wCAA8B,WAAU;kDACvC,cAAA,8OAAC,gNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;uCADlB,CAAC,SAAS,EAAE,OAAO;;;;;gCAIjC;gCAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;oCAEL,SAAS,SAAS,cAAc,YAAY;oCAC5C,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAET;mCANI;;;;;4BASX;0CAGA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,cAAc;gCAC1C,UAAU,eAAe;gCACzB,WAAU;0CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAK3B,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,8OAAC;gCACC,MAAK;gCACL,KAAK;gCACL,KAAK;gCACL,aAAY;gCACZ,WAAW;gCACX,WAAU;;;;;;0CAEZ,8OAAC;gCAAK,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;AAMtE;AAaO,MAAM,mBAAoD,CAAC,EAChE,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;0BAC3D,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;;kCAEzB,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAI1C,8OAAC;gBAAK,WAAU;;oBAA6B;oBACxC;oBAAY;oBAAM;oBAAW;;;;;;;0BAGlC,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;;oBAC1B;kCAEC,8OAAC,sNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIhC;AAaO,MAAM,mBAAoD,CAAC,EAChE,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACV;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;;0BAExC,8OAAC;gBAAI,WAAU;;oBAAoC;oBAC9C;oBAAY;oBAAM;oBAAW;;;;;;;0BAIlC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,eAAe;wBACzB,WAAU;;0CAEV,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAI1C,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,eAAe;wBACzB,WAAU;;4BACX;0CAEC,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC", "debugId": null}}, {"offset": {"line": 1932, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/responsive-utils.ts"], "sourcesContent": ["/**\n * 响应式设计工具函数\n * \n * 功能说明：\n * 1. 提供屏幕尺寸检测工具\n * 2. 响应式断点管理\n * 3. 移动端特性检测\n * 4. 触摸设备检测\n */\n\n'use client';\n\nimport { useState, useEffect } from 'react';\n\n// ============================================================================\n// 响应式断点定义\n// ============================================================================\n\nexport const breakpoints = {\n  xs: 0,      // 超小屏幕\n  sm: 640,    // 小屏幕\n  md: 768,    // 中等屏幕\n  lg: 1024,   // 大屏幕\n  xl: 1280,   // 超大屏幕\n  '2xl': 1536 // 2倍超大屏幕\n} as const;\n\nexport type Breakpoint = keyof typeof breakpoints;\n\n// ============================================================================\n// 屏幕尺寸检测Hook\n// ============================================================================\n\nexport interface ScreenSize {\n  width: number;\n  height: number;\n  isMobile: boolean;\n  isTablet: boolean;\n  isDesktop: boolean;\n  currentBreakpoint: Breakpoint;\n}\n\nexport const useScreenSize = (): ScreenSize => {\n  const [screenSize, setScreenSize] = useState<ScreenSize>({\n    width: 0,\n    height: 0,\n    isMobile: false,\n    isTablet: false,\n    isDesktop: false,\n    currentBreakpoint: 'xs'\n  });\n\n  useEffect(() => {\n    const updateScreenSize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n\n      // 确定当前断点\n      let currentBreakpoint: Breakpoint = 'xs';\n      if (width >= breakpoints['2xl']) currentBreakpoint = '2xl';\n      else if (width >= breakpoints.xl) currentBreakpoint = 'xl';\n      else if (width >= breakpoints.lg) currentBreakpoint = 'lg';\n      else if (width >= breakpoints.md) currentBreakpoint = 'md';\n      else if (width >= breakpoints.sm) currentBreakpoint = 'sm';\n\n      setScreenSize({\n        width,\n        height,\n        isMobile: width < breakpoints.md,\n        isTablet: width >= breakpoints.md && width < breakpoints.lg,\n        isDesktop: width >= breakpoints.lg,\n        currentBreakpoint\n      });\n    };\n\n    // 初始化\n    updateScreenSize();\n\n    // 监听窗口大小变化\n    window.addEventListener('resize', updateScreenSize);\n    \n    return () => {\n      window.removeEventListener('resize', updateScreenSize);\n    };\n  }, []);\n\n  return screenSize;\n};\n\n// ============================================================================\n// 断点匹配Hook\n// ============================================================================\n\nexport const useBreakpoint = (breakpoint: Breakpoint): boolean => {\n  const [matches, setMatches] = useState(false);\n\n  useEffect(() => {\n    const checkBreakpoint = () => {\n      setMatches(window.innerWidth >= breakpoints[breakpoint]);\n    };\n\n    // 初始检查\n    checkBreakpoint();\n\n    // 监听窗口大小变化\n    window.addEventListener('resize', checkBreakpoint);\n    \n    return () => {\n      window.removeEventListener('resize', checkBreakpoint);\n    };\n  }, [breakpoint]);\n\n  return matches;\n};\n\n// ============================================================================\n// 移动端检测Hook\n// ============================================================================\n\nexport const useIsMobile = (): boolean => {\n  return useBreakpoint('md') === false;\n};\n\nexport const useIsTablet = (): boolean => {\n  const isMd = useBreakpoint('md');\n  const isLg = useBreakpoint('lg');\n  return isMd && !isLg;\n};\n\nexport const useIsDesktop = (): boolean => {\n  return useBreakpoint('lg');\n};\n\n// ============================================================================\n// 触摸设备检测\n// ============================================================================\n\nexport const useIsTouchDevice = (): boolean => {\n  const [isTouchDevice, setIsTouchDevice] = useState(false);\n\n  useEffect(() => {\n    const checkTouchDevice = () => {\n      setIsTouchDevice(\n        'ontouchstart' in window ||\n        navigator.maxTouchPoints > 0 ||\n        // @ts-ignore\n        navigator.msMaxTouchPoints > 0\n      );\n    };\n\n    checkTouchDevice();\n  }, []);\n\n  return isTouchDevice;\n};\n\n// ============================================================================\n// 设备方向检测\n// ============================================================================\n\nexport type Orientation = 'portrait' | 'landscape';\n\nexport const useOrientation = (): Orientation => {\n  const [orientation, setOrientation] = useState<Orientation>('portrait');\n\n  useEffect(() => {\n    const updateOrientation = () => {\n      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');\n    };\n\n    // 初始化\n    updateOrientation();\n\n    // 监听方向变化\n    window.addEventListener('resize', updateOrientation);\n    window.addEventListener('orientationchange', updateOrientation);\n    \n    return () => {\n      window.removeEventListener('resize', updateOrientation);\n      window.removeEventListener('orientationchange', updateOrientation);\n    };\n  }, []);\n\n  return orientation;\n};\n\n// ============================================================================\n// 响应式值Hook\n// ============================================================================\n\nexport interface ResponsiveValue<T> {\n  xs?: T;\n  sm?: T;\n  md?: T;\n  lg?: T;\n  xl?: T;\n  '2xl'?: T;\n}\n\nexport const useResponsiveValue = <T>(values: ResponsiveValue<T>): T | undefined => {\n  const { currentBreakpoint } = useScreenSize();\n\n  // 按优先级查找值\n  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];\n  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);\n\n  for (let i = currentIndex; i < breakpointOrder.length; i++) {\n    const bp = breakpointOrder[i];\n    if (values[bp] !== undefined) {\n      return values[bp];\n    }\n  }\n\n  return undefined;\n};\n\n// ============================================================================\n// 工具函数\n// ============================================================================\n\n/**\n * 检查当前是否为移动端\n */\nexport const isMobileDevice = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < breakpoints.md;\n};\n\n/**\n * 检查当前是否为平板设备\n */\nexport const isTabletDevice = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  const width = window.innerWidth;\n  return width >= breakpoints.md && width < breakpoints.lg;\n};\n\n/**\n * 检查当前是否为桌面设备\n */\nexport const isDesktopDevice = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= breakpoints.lg;\n};\n\n/**\n * 获取当前断点\n */\nexport const getCurrentBreakpoint = (): Breakpoint => {\n  if (typeof window === 'undefined') return 'xs';\n  \n  const width = window.innerWidth;\n  if (width >= breakpoints['2xl']) return '2xl';\n  if (width >= breakpoints.xl) return 'xl';\n  if (width >= breakpoints.lg) return 'lg';\n  if (width >= breakpoints.md) return 'md';\n  if (width >= breakpoints.sm) return 'sm';\n  return 'xs';\n};\n\n/**\n * 生成响应式类名\n */\nexport const getResponsiveClasses = (\n  baseClass: string,\n  responsiveClasses: Partial<Record<Breakpoint, string>>\n): string => {\n  const classes = [baseClass];\n  \n  Object.entries(responsiveClasses).forEach(([breakpoint, className]) => {\n    if (className) {\n      const prefix = breakpoint === 'xs' ? '' : `${breakpoint}:`;\n      classes.push(`${prefix}${className}`);\n    }\n  });\n  \n  return classes.join(' ');\n};\n\n// ============================================================================\n// 导出所有工具\n// ============================================================================\n\nexport {\n  useScreenSize,\n  useBreakpoint,\n  useIsMobile,\n  useIsTablet,\n  useIsDesktop,\n  useIsTouchDevice,\n  useOrientation,\n  useResponsiveValue\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;AAID;AAFA;;AAQO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO,KAAK,SAAS;AACvB;AAiBO,MAAM,gBAAgB;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;QACX,mBAAmB;IACrB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,QAAQ,OAAO,UAAU;YAC/B,MAAM,SAAS,OAAO,WAAW;YAEjC,SAAS;YACT,IAAI,oBAAgC;YACpC,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,oBAAoB;iBAChD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;iBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;iBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;iBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;YAEtD,cAAc;gBACZ;gBACA;gBACA,UAAU,QAAQ,YAAY,EAAE;gBAChC,UAAU,SAAS,YAAY,EAAE,IAAI,QAAQ,YAAY,EAAE;gBAC3D,WAAW,SAAS,YAAY,EAAE;gBAClC;YACF;QACF;QAEA,MAAM;QACN;QAEA,WAAW;QACX,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AAMO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,WAAW,OAAO,UAAU,IAAI,WAAW,CAAC,WAAW;QACzD;QAEA,OAAO;QACP;QAEA,WAAW;QACX,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG;QAAC;KAAW;IAEf,OAAO;AACT;AAMO,MAAM,cAAc;IACzB,OAAO,cAAc,UAAU;AACjC;AAEO,MAAM,cAAc;IACzB,MAAM,OAAO,cAAc;IAC3B,MAAM,OAAO,cAAc;IAC3B,OAAO,QAAQ,CAAC;AAClB;AAEO,MAAM,eAAe;IAC1B,OAAO,cAAc;AACvB;AAMO,MAAM,mBAAmB;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,iBACE,kBAAkB,UAClB,UAAU,cAAc,GAAG,KAC3B,aAAa;YACb,UAAU,gBAAgB,GAAG;QAEjC;QAEA;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AAQO,MAAM,iBAAiB;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,eAAe,OAAO,WAAW,GAAG,OAAO,UAAU,GAAG,aAAa;QACvE;QAEA,MAAM;QACN;QAEA,SAAS;QACT,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,qBAAqB;QAE7C,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,qBAAqB;QAClD;IACF,GAAG,EAAE;IAEL,OAAO;AACT;AAeO,MAAM,qBAAqB,CAAI;IACpC,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,UAAU;IACV,MAAM,kBAAgC;QAAC;QAAO;QAAM;QAAM;QAAM;QAAM;KAAK;IAC3E,MAAM,eAAe,gBAAgB,OAAO,CAAC;IAE7C,IAAK,IAAI,IAAI,cAAc,IAAI,gBAAgB,MAAM,EAAE,IAAK;QAC1D,MAAM,KAAK,eAAe,CAAC,EAAE;QAC7B,IAAI,MAAM,CAAC,GAAG,KAAK,WAAW;YAC5B,OAAO,MAAM,CAAC,GAAG;QACnB;IACF;IAEA,OAAO;AACT;AASO,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;;AAE5C;AAKO,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;;IAC1C,MAAM;AAER;AAKO,MAAM,kBAAkB;IAC7B,wCAAmC,OAAO;;;AAE5C;AAKO,MAAM,uBAAuB;IAClC,wCAAmC,OAAO;;;IAE1C,MAAM;AAOR;AAKO,MAAM,uBAAuB,CAClC,WACA;IAEA,MAAM,UAAU;QAAC;KAAU;IAE3B,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC,CAAC,CAAC,YAAY,UAAU;QAChE,IAAI,WAAW;YACb,MAAM,SAAS,eAAe,OAAO,KAAK,GAAG,WAAW,CAAC,CAAC;YAC1D,QAAQ,IAAI,CAAC,GAAG,SAAS,WAAW;QACtC;IACF;IAEA,OAAO,QAAQ,IAAI,CAAC;AACtB", "debugId": null}}, {"offset": {"line": 2123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2239, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/data-table.tsx"], "sourcesContent": ["/**\n * 数据表格组件\n * \n * 功能说明：\n * 1. 提供完整的数据表格功能\n * 2. 支持排序、筛选、分页\n * 3. 响应式设计，移动端友好\n * 4. 可配置列定义和操作\n */\n\nimport React, { useState } from 'react';\nimport { ChevronUp, ChevronDown, Search, Filter, MoreHorizontal } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './button';\nimport { Input } from './input';\nimport { Badge } from './badge';\nimport { LoadingTableRow } from './loading';\nimport { Pagination } from './pagination';\nimport { useIsMobile } from '@/lib/responsive-utils';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from './table';\n\n// ============================================================================\n// 表格列定义接口\n// ============================================================================\n\nexport interface ColumnDef<T> {\n  key: string;                    // 列的唯一标识\n  title: string;                  // 列标题\n  dataIndex?: keyof T;            // 数据字段名\n  width?: string | number;        // 列宽度\n  sortable?: boolean;             // 是否可排序\n  filterable?: boolean;           // 是否可筛选\n  searchable?: boolean;           // 是否可搜索\n  align?: 'left' | 'center' | 'right'; // 对齐方式\n  className?: string;             // 自定义样式\n  render?: (value: any, record: T, index: number) => React.ReactNode; // 自定义渲染\n  sorter?: (a: T, b: T) => number; // 自定义排序函数\n  filters?: Array<{ text: string; value: any }>; // 筛选选项\n}\n\n// ============================================================================\n// 表格组件接口\n// ============================================================================\n\nexport interface DataTableProps<T> {\n  columns: ColumnDef<T>[];        // 列定义\n  data: T[];                      // 数据源\n  loading?: boolean;              // 加载状态\n  pagination?: {                  // 分页配置\n    current: number;\n    pageSize: number;\n    total: number;\n    showSizeChanger?: boolean;\n    showQuickJumper?: boolean;\n    onChange: (page: number, pageSize: number) => void;\n  };\n  rowKey?: keyof T | ((record: T) => string); // 行唯一标识\n  onRow?: (record: T, index: number) => {     // 行事件处理\n    onClick?: () => void;\n    onDoubleClick?: () => void;\n    className?: string;\n  };\n  emptyText?: string;             // 空数据提示\n  size?: 'small' | 'middle' | 'large'; // 表格尺寸\n  bordered?: boolean;             // 是否显示边框\n  striped?: boolean;              // 是否显示斑马纹\n  className?: string;\n}\n\n// ============================================================================\n// 数据表格组件实现\n// ============================================================================\n\nexport function DataTable<T extends Record<string, any>>({\n  columns,\n  data,\n  loading = false,\n  pagination,\n  rowKey = 'id',\n  onRow,\n  emptyText = '暂无数据',\n  className\n}: DataTableProps<T>) {\n  const [sortConfig, setSortConfig] = useState<{\n    key: string;\n    direction: 'asc' | 'desc';\n  } | null>(null);\n  const [searchText, setSearchText] = useState('');\n  const [filters, setFilters] = useState<Record<string, any>>({});\n  const isMobile = useIsMobile();\n\n  // 获取行的唯一标识\n  const getRowKey = (record: T, index: number): string => {\n    if (typeof rowKey === 'function') {\n      return rowKey(record);\n    }\n    return record[rowKey]?.toString() || index.toString();\n  };\n\n  // 处理排序\n  const handleSort = (column: ColumnDef<T>) => {\n    if (!column.sortable) return;\n\n    let direction: 'asc' | 'desc' = 'asc';\n    if (sortConfig?.key === column.key && sortConfig.direction === 'asc') {\n      direction = 'desc';\n    }\n\n    setSortConfig({ key: column.key, direction });\n  };\n\n  // 处理搜索\n  const handleSearch = (value: string) => {\n    setSearchText(value);\n  };\n\n  // 处理筛选\n  const handleFilter = (columnKey: string, value: any) => {\n    setFilters(prev => ({\n      ...prev,\n      [columnKey]: value\n    }));\n  };\n\n  // 渲染排序图标\n  const renderSortIcon = (column: ColumnDef<T>) => {\n    if (!column.sortable) return null;\n\n    const isActive = sortConfig?.key === column.key;\n    const direction = sortConfig?.direction;\n\n    return (\n      <span className=\"ml-1 inline-flex flex-col\">\n        <ChevronUp \n          className={cn(\n            'h-3 w-3 -mb-1',\n            isActive && direction === 'asc' ? 'text-blue-600' : 'text-gray-400'\n          )} \n        />\n        <ChevronDown \n          className={cn(\n            'h-3 w-3',\n            isActive && direction === 'desc' ? 'text-blue-600' : 'text-gray-400'\n          )} \n        />\n      </span>\n    );\n  };\n\n  // 渲染表头\n  const renderHeader = () => (\n    <TableHeader>\n      <TableRow>\n        {columns.map((column) => (\n          <TableHead\n            key={column.key}\n            className={cn(\n              column.className,\n              column.sortable && 'cursor-pointer hover:bg-gray-50',\n              column.align === 'center' && 'text-center',\n              column.align === 'right' && 'text-right'\n            )}\n            style={{ width: column.width }}\n            onClick={() => handleSort(column)}\n          >\n            <div className=\"flex items-center\">\n              {column.title}\n              {renderSortIcon(column)}\n            </div>\n          </TableHead>\n        ))}\n      </TableRow>\n    </TableHeader>\n  );\n\n  // 渲染单元格内容\n  const renderCell = (column: ColumnDef<T>, record: T, index: number) => {\n    const value = column.dataIndex ? record[column.dataIndex] : undefined;\n    \n    if (column.render) {\n      return column.render(value, record, index);\n    }\n\n    // 默认渲染逻辑\n    if (value === null || value === undefined) {\n      return <span className=\"text-gray-400\">-</span>;\n    }\n\n    if (typeof value === 'boolean') {\n      return (\n        <Badge variant={value ? 'default' : 'secondary'}>\n          {value ? '是' : '否'}\n        </Badge>\n      );\n    }\n\n    if (typeof value === 'number') {\n      return value.toLocaleString();\n    }\n\n    return value.toString();\n  };\n\n  // 渲染表格主体\n  const renderBody = () => {\n    if (loading) {\n      return (\n        <TableBody>\n          <LoadingTableRow columns={columns.length} rows={5} />\n        </TableBody>\n      );\n    }\n\n    if (data.length === 0) {\n      return (\n        <TableBody>\n          <TableRow>\n            <TableCell \n              colSpan={columns.length} \n              className=\"text-center py-8 text-gray-500\"\n            >\n              {emptyText}\n            </TableCell>\n          </TableRow>\n        </TableBody>\n      );\n    }\n\n    return (\n      <TableBody>\n        {data.map((record, index) => {\n          const rowProps = onRow?.(record, index) || {};\n          \n          return (\n            <TableRow\n              key={getRowKey(record, index)}\n              className={cn(\n                rowProps.className,\n                rowProps.onClick && 'cursor-pointer hover:bg-gray-50',\n                index % 2 === 1 && 'bg-gray-50/50'\n              )}\n              onClick={rowProps.onClick}\n              onDoubleClick={rowProps.onDoubleClick}\n            >\n              {columns.map((column) => (\n                <TableCell\n                  key={column.key}\n                  className={cn(\n                    column.align === 'center' && 'text-center',\n                    column.align === 'right' && 'text-right'\n                  )}\n                >\n                  {renderCell(column, record, index)}\n                </TableCell>\n              ))}\n            </TableRow>\n          );\n        })}\n      </TableBody>\n    );\n  };\n\n  return (\n    <div className={cn('space-y-4', className)}>\n      {/* 表格工具栏 */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n        <div className=\"flex gap-2\">\n          {/* 搜索框 */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"搜索...\"\n              value={searchText}\n              onChange={(e) => handleSearch(e.target.value)}\n              className=\"pl-9 w-64\"\n            />\n          </div>\n\n          {/* 筛选按钮 */}\n          <Button variant=\"outline\" size=\"sm\">\n            <Filter className=\"h-4 w-4 mr-2\" />\n            筛选\n          </Button>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" size=\"sm\">\n            <MoreHorizontal className=\"h-4 w-4 mr-2\" />\n            更多操作\n          </Button>\n        </div>\n      </div>\n\n      {/* 表格 */}\n      <div className=\"rounded-md border\">\n        <Table>\n          {renderHeader()}\n          {renderBody()}\n        </Table>\n      </div>\n\n      {/* 分页 */}\n      {pagination && (\n        <Pagination\n          currentPage={pagination.current}\n          totalPages={Math.ceil(pagination.total / pagination.pageSize)}\n          totalItems={pagination.total}\n          pageSize={pagination.pageSize}\n          onPageChange={(page) => pagination.onChange(page, pagination.pageSize)}\n          onPageSizeChange={(pageSize) => pagination.onChange(1, pageSize)}\n          showPageSizeSelector={pagination.showSizeChanger}\n          showQuickJumper={pagination.showQuickJumper}\n        />\n      )}\n    </div>\n  );\n}\n\n// ============================================================================\n// 导出组件\n// ============================================================================\n\nexport { DataTable as default };\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AA6DO,SAAS,UAAyC,EACvD,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,UAAU,EACV,SAAS,IAAI,EACb,KAAK,EACL,YAAY,MAAM,EAClB,SAAS,EACS;IAClB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAGjC;IACV,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD;IAE3B,WAAW;IACX,MAAM,YAAY,CAAC,QAAW;QAC5B,IAAI,OAAO,WAAW,YAAY;YAChC,OAAO,OAAO;QAChB;QACA,OAAO,MAAM,CAAC,OAAO,EAAE,cAAc,MAAM,QAAQ;IACrD;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,OAAO,QAAQ,EAAE;QAEtB,IAAI,YAA4B;QAChC,IAAI,YAAY,QAAQ,OAAO,GAAG,IAAI,WAAW,SAAS,KAAK,OAAO;YACpE,YAAY;QACd;QAEA,cAAc;YAAE,KAAK,OAAO,GAAG;YAAE;QAAU;IAC7C;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,cAAc;IAChB;IAEA,OAAO;IACP,MAAM,eAAe,CAAC,WAAmB;QACvC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;YACf,CAAC;IACH;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO;QAE7B,MAAM,WAAW,YAAY,QAAQ,OAAO,GAAG;QAC/C,MAAM,YAAY,YAAY;QAE9B,qBACE,8OAAC;YAAK,WAAU;;8BACd,8OAAC,gNAAA,CAAA,YAAS;oBACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iBACA,YAAY,cAAc,QAAQ,kBAAkB;;;;;;8BAGxD,8OAAC,oNAAA,CAAA,cAAW;oBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WACA,YAAY,cAAc,SAAS,kBAAkB;;;;;;;;;;;;IAK/D;IAEA,OAAO;IACP,MAAM,eAAe,kBACnB,8OAAC,iIAAA,CAAA,cAAW;sBACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;0BACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;wBAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OAAO,SAAS,EAChB,OAAO,QAAQ,IAAI,mCACnB,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;wBAE9B,OAAO;4BAAE,OAAO,OAAO,KAAK;wBAAC;wBAC7B,SAAS,IAAM,WAAW;kCAE1B,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,OAAO,KAAK;gCACZ,eAAe;;;;;;;uBAZb,OAAO,GAAG;;;;;;;;;;;;;;;IAoBzB,UAAU;IACV,MAAM,aAAa,CAAC,QAAsB,QAAW;QACnD,MAAM,QAAQ,OAAO,SAAS,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,GAAG;QAE5D,IAAI,OAAO,MAAM,EAAE;YACjB,OAAO,OAAO,MAAM,CAAC,OAAO,QAAQ;QACtC;QAEA,SAAS;QACT,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,qBAAO,8OAAC;gBAAK,WAAU;0BAAgB;;;;;;QACzC;QAEA,IAAI,OAAO,UAAU,WAAW;YAC9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAS,QAAQ,YAAY;0BACjC,QAAQ,MAAM;;;;;;QAGrB;QAEA,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO,MAAM,cAAc;QAC7B;QAEA,OAAO,MAAM,QAAQ;IACvB;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,qBACE,8OAAC,iIAAA,CAAA,YAAS;0BACR,cAAA,8OAAC,mIAAA,CAAA,kBAAe;oBAAC,SAAS,QAAQ,MAAM;oBAAE,MAAM;;;;;;;;;;;QAGtD;QAEA,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,qBACE,8OAAC,iIAAA,CAAA,YAAS;0BACR,cAAA,8OAAC,iIAAA,CAAA,WAAQ;8BACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wBACR,SAAS,QAAQ,MAAM;wBACvB,WAAU;kCAET;;;;;;;;;;;;;;;;QAKX;QAEA,qBACE,8OAAC,iIAAA,CAAA,YAAS;sBACP,KAAK,GAAG,CAAC,CAAC,QAAQ;gBACjB,MAAM,WAAW,QAAQ,QAAQ,UAAU,CAAC;gBAE5C,qBACE,8OAAC,iIAAA,CAAA,WAAQ;oBAEP,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,SAAS,SAAS,EAClB,SAAS,OAAO,IAAI,mCACpB,QAAQ,MAAM,KAAK;oBAErB,SAAS,SAAS,OAAO;oBACzB,eAAe,SAAS,aAAa;8BAEpC,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,iIAAA,CAAA,YAAS;4BAER,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;sCAG7B,WAAW,QAAQ,QAAQ;2BANvB,OAAO,GAAG;;;;;mBAXd,UAAU,QAAQ;;;;;YAsB7B;;;;;;IAGN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;;;;;;;;;;;;0CAKd,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMvC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,8OAAC,gNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAOjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;wBACH;wBACA;;;;;;;;;;;;YAKJ,4BACC,8OAAC,sIAAA,CAAA,aAAU;gBACT,aAAa,WAAW,OAAO;gBAC/B,YAAY,KAAK,IAAI,CAAC,WAAW,KAAK,GAAG,WAAW,QAAQ;gBAC5D,YAAY,WAAW,KAAK;gBAC5B,UAAU,WAAW,QAAQ;gBAC7B,cAAc,CAAC,OAAS,WAAW,QAAQ,CAAC,MAAM,WAAW,QAAQ;gBACrE,kBAAkB,CAAC,WAAa,WAAW,QAAQ,CAAC,GAAG;gBACvD,sBAAsB,WAAW,eAAe;gBAChD,iBAAiB,WAAW,eAAe;;;;;;;;;;;;AAKrD", "debugId": null}}, {"offset": {"line": 2609, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] rounded-lg border shadow-lg duration-200 sm:max-w-lg\",\n          // 添加高度限制和网格布局\n          \"max-h-[90vh] grid-rows-[auto_1fr_auto]\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\n// 可滚动表单对话框组件\nfunction ScrollableFormDialog({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean;\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 flex flex-col w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] rounded-lg border shadow-lg duration-200 sm:max-w-lg\",\n          // 固定高度和布局\n          \"h-[90vh] max-h-[90vh]\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 z-10\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\n// 固定的对话框头部\nfunction ScrollableDialogHeader({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\n        \"flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-4 border-b bg-background\",\n        \"flex-shrink-0\", // 防止收缩\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\n// 可滚动的对话框内容区域\nfunction ScrollableDialogBody({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\n        \"flex-1 overflow-y-auto px-6 py-4\",\n        // 隐藏滚动条但保持滚动功能\n        \"scrollbar-none\",\n        // 自定义滚动条样式\n        \"[&::-webkit-scrollbar]:w-0 [&::-webkit-scrollbar]:bg-transparent\",\n        \"[&::-webkit-scrollbar-track]:bg-transparent\",\n        \"[&::-webkit-scrollbar-thumb]:bg-transparent\",\n        className\n      )}\n      style={{\n        scrollbarWidth: 'none', // Firefox\n        msOverflowStyle: 'none', // IE/Edge\n      }}\n      {...props}\n    />\n  )\n}\n\n// 固定的对话框底部\nfunction ScrollableDialogFooter({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\n        \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-4 border-t bg-background\",\n        \"flex-shrink-0\", // 防止收缩\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n  ScrollableFormDialog,\n  ScrollableDialogHeader,\n  ScrollableDialogBody,\n  ScrollableDialogFooter,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,kKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qWACA,cAAc;gBACd,0CACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,aAAa;AACb,SAAS,qBAAqB,EAC5B,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,8OAAC;QAAa,aAAU;;0BACtB,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8WACA,UAAU;gBACV,yBACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,8OAAC,kKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,8OAAC,gMAAA,CAAA,QAAK;;;;;0CACN,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,WAAW;AACX,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sFACA,iBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,cAAc;AACd,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA,eAAe;QACf,kBACA,WAAW;QACX,oEACA,+CACA,+CACA;QAEF,OAAO;YACL,gBAAgB;YAChB,iBAAiB;QACnB;QACC,GAAG,KAAK;;;;;;AAGf;AAEA,WAAW;AACX,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iGACA,iBACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2878, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/confirm-dialog.tsx"], "sourcesContent": ["/**\n * 确认对话框组件\n * \n * 功能说明：\n * 1. 提供通用的确认对话框\n * 2. 支持不同类型的确认操作\n * 3. 可配置的标题、内容和按钮\n * 4. 支持异步操作和加载状态\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { AlertTriangle, Trash2, Edit, Eye, Plus } from 'lucide-react';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { LoadingButton } from '@/components/ui/loading';\nimport { Badge } from '@/components/ui/badge';\n\n// ============================================================================\n// 确认对话框类型定义\n// ============================================================================\n\nexport type ConfirmType = 'delete' | 'edit' | 'create' | 'view' | 'warning' | 'info';\n\nexport interface ConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  type?: ConfirmType;\n  title?: string;\n  description?: string;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n  loading?: boolean;\n  children?: React.ReactNode;\n  data?: any; // 可以传递额外的数据用于显示\n}\n\n// ============================================================================\n// 确认对话框组件实现\n// ============================================================================\n\nexport const ConfirmDialog: React.FC<ConfirmDialogProps> = ({\n  open,\n  onOpenChange,\n  type = 'warning',\n  title,\n  description,\n  confirmText,\n  cancelText = '取消',\n  onConfirm,\n  onCancel,\n  loading = false,\n  children,\n  data\n}) => {\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  // 根据类型获取默认配置\n  const getTypeConfig = () => {\n    switch (type) {\n      case 'delete':\n        return {\n          icon: Trash2,\n          iconColor: 'text-red-500',\n          title: title || '确认删除',\n          description: description || '此操作不可撤销，确定要删除吗？',\n          confirmText: confirmText || '删除',\n          confirmVariant: 'destructive' as const\n        };\n      case 'edit':\n        return {\n          icon: Edit,\n          iconColor: 'text-blue-500',\n          title: title || '确认编辑',\n          description: description || '确定要编辑此项吗？',\n          confirmText: confirmText || '编辑',\n          confirmVariant: 'default' as const\n        };\n      case 'create':\n        return {\n          icon: Plus,\n          iconColor: 'text-green-500',\n          title: title || '确认创建',\n          description: description || '确定要创建此项吗？',\n          confirmText: confirmText || '创建',\n          confirmVariant: 'default' as const\n        };\n      case 'view':\n        return {\n          icon: Eye,\n          iconColor: 'text-gray-500',\n          title: title || '查看详情',\n          description: description || '',\n          confirmText: confirmText || '确定',\n          confirmVariant: 'default' as const\n        };\n      case 'warning':\n        return {\n          icon: AlertTriangle,\n          iconColor: 'text-yellow-500',\n          title: title || '警告',\n          description: description || '请确认您的操作',\n          confirmText: confirmText || '确认',\n          confirmVariant: 'default' as const\n        };\n      case 'info':\n      default:\n        return {\n          icon: AlertTriangle,\n          iconColor: 'text-blue-500',\n          title: title || '提示',\n          description: description || '请确认您的操作',\n          confirmText: confirmText || '确认',\n          confirmVariant: 'default' as const\n        };\n    }\n  };\n\n  const config = getTypeConfig();\n  const Icon = config.icon;\n\n  // 处理确认操作\n  const handleConfirm = async () => {\n    if (!onConfirm) return;\n\n    try {\n      setIsProcessing(true);\n      await onConfirm();\n      onOpenChange(false);\n    } catch (error) {\n      console.error('确认操作失败:', error);\n      // 这里可以添加错误提示\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    }\n    onOpenChange(false);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <div className=\"flex items-center gap-3\">\n            <div className={`p-2 rounded-full bg-gray-100 ${config.iconColor}`}>\n              <Icon className=\"h-5 w-5\" />\n            </div>\n            <div>\n              <DialogTitle className=\"text-lg font-semibold\">\n                {config.title}\n              </DialogTitle>\n            </div>\n          </div>\n        </DialogHeader>\n\n        <div className=\"py-4\">\n          {config.description && (\n            <DialogDescription className=\"text-gray-600 mb-4\">\n              {config.description}\n            </DialogDescription>\n          )}\n\n          {/* 自定义内容 */}\n          {children}\n\n          {/* 数据展示 */}\n          {data && (\n            <div className=\"mt-4 p-4 bg-gray-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium mb-2\">相关信息：</h4>\n              <div className=\"space-y-2\">\n                {Object.entries(data).map(([key, value]) => (\n                  <div key={key} className=\"flex justify-between items-center text-sm\">\n                    <span className=\"text-gray-600\">{key}:</span>\n                    <span className=\"font-medium\">\n                      {typeof value === 'boolean' ? (\n                        <Badge variant={value ? 'default' : 'secondary'}>\n                          {value ? '是' : '否'}\n                        </Badge>\n                      ) : (\n                        value?.toString() || '-'\n                      )}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={handleCancel}\n            disabled={isProcessing || loading}\n          >\n            {cancelText}\n          </Button>\n          \n          {onConfirm && (\n            <LoadingButton\n              variant={config.confirmVariant}\n              onClick={handleConfirm}\n              loading={isProcessing || loading}\n              loadingText=\"处理中...\"\n            >\n              {config.confirmText}\n            </LoadingButton>\n          )}\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\n// ============================================================================\n// 删除确认对话框（特化组件）\n// ============================================================================\n\nexport interface DeleteConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  title?: string;\n  itemName?: string;\n  itemType?: string;\n  onConfirm: () => void | Promise<void>;\n  loading?: boolean;\n  additionalInfo?: Record<string, any>;\n}\n\nexport const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({\n  open,\n  onOpenChange,\n  title,\n  itemName,\n  itemType = '项目',\n  onConfirm,\n  loading = false,\n  additionalInfo\n}) => {\n  return (\n    <ConfirmDialog\n      open={open}\n      onOpenChange={onOpenChange}\n      type=\"delete\"\n      title={title || `删除${itemType}`}\n      description={\n        itemName \n          ? `确定要删除${itemType}\"${itemName}\"吗？此操作不可撤销。`\n          : `确定要删除此${itemType}吗？此操作不可撤销。`\n      }\n      confirmText=\"删除\"\n      onConfirm={onConfirm}\n      loading={loading}\n      data={additionalInfo}\n    />\n  );\n};\n\n// ============================================================================\n// 批量操作确认对话框\n// ============================================================================\n\nexport interface BatchConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  action: string;\n  itemCount: number;\n  itemType?: string;\n  onConfirm: () => void | Promise<void>;\n  loading?: boolean;\n}\n\nexport const BatchConfirmDialog: React.FC<BatchConfirmDialogProps> = ({\n  open,\n  onOpenChange,\n  action,\n  itemCount,\n  itemType = '项目',\n  onConfirm,\n  loading = false\n}) => {\n  return (\n    <ConfirmDialog\n      open={open}\n      onOpenChange={onOpenChange}\n      type={action === '删除' ? 'delete' : 'warning'}\n      title={`批量${action}`}\n      description={`确定要${action}选中的 ${itemCount} 个${itemType}吗？`}\n      confirmText={action}\n      onConfirm={onConfirm}\n      loading={loading}\n    />\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  ConfirmDialog as default,\n  DeleteConfirmDialog,\n  BatchConfirmDialog\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAQA;AACA;AACA;AAdA;;;;;;;;AAyCO,MAAM,gBAA8C,CAAC,EAC1D,IAAI,EACJ,YAAY,EACZ,OAAO,SAAS,EAChB,KAAK,EACL,WAAW,EACX,WAAW,EACX,aAAa,IAAI,EACjB,SAAS,EACT,QAAQ,EACR,UAAU,KAAK,EACf,QAAQ,EACR,IAAI,EACL;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,aAAa;IACb,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,MAAM,0MAAA,CAAA,SAAM;oBACZ,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,2MAAA,CAAA,OAAI;oBACV,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,kMAAA,CAAA,OAAI;oBACV,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,gMAAA,CAAA,MAAG;oBACT,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,wNAAA,CAAA,gBAAa;oBACnB,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;YACL;gBACE,OAAO;oBACL,MAAM,wNAAA,CAAA,gBAAa;oBACnB,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;QACJ;IACF;IAEA,MAAM,SAAS;IACf,MAAM,OAAO,OAAO,IAAI;IAExB,SAAS;IACT,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,gBAAgB;YAChB,MAAM;YACN,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QACzB,aAAa;QACf,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ;QACF;QACA,aAAa;IACf;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAW,CAAC,6BAA6B,EAAE,OAAO,SAAS,EAAE;0CAChE,cAAA,8OAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,8OAAC;0CACC,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;8BAMrB,8OAAC;oBAAI,WAAU;;wBACZ,OAAO,WAAW,kBACjB,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAC1B,OAAO,WAAW;;;;;;wBAKtB;wBAGA,sBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACrC,8OAAC;4CAAc,WAAU;;8DACvB,8OAAC;oDAAK,WAAU;;wDAAiB;wDAAI;;;;;;;8DACrC,8OAAC;oDAAK,WAAU;8DACb,OAAO,UAAU,0BAChB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAS,QAAQ,YAAY;kEACjC,QAAQ,MAAM;;;;;mGAGjB,OAAO,cAAc;;;;;;;2CARjB;;;;;;;;;;;;;;;;;;;;;;8BAkBpB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,gBAAgB;sCAEzB;;;;;;wBAGF,2BACC,8OAAC,mIAAA,CAAA,gBAAa;4BACZ,SAAS,OAAO,cAAc;4BAC9B,SAAS;4BACT,SAAS,gBAAgB;4BACzB,aAAY;sCAEX,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAOjC;AAiBO,MAAM,sBAA0D,CAAC,EACtE,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,IAAI,EACf,SAAS,EACT,UAAU,KAAK,EACf,cAAc,EACf;IACC,qBACE,8OAAC;QACC,MAAM;QACN,cAAc;QACd,MAAK;QACL,OAAO,SAAS,CAAC,EAAE,EAAE,UAAU;QAC/B,aACE,WACI,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,SAAS,WAAW,CAAC,GACzC,CAAC,MAAM,EAAE,SAAS,UAAU,CAAC;QAEnC,aAAY;QACZ,WAAW;QACX,SAAS;QACT,MAAM;;;;;;AAGZ;AAgBO,MAAM,qBAAwD,CAAC,EACpE,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,SAAS,EACT,WAAW,IAAI,EACf,SAAS,EACT,UAAU,KAAK,EAChB;IACC,qBACE,8OAAC;QACC,MAAM;QACN,cAAc;QACd,MAAM,WAAW,OAAO,WAAW;QACnC,OAAO,CAAC,EAAE,EAAE,QAAQ;QACpB,aAAa,CAAC,GAAG,EAAE,OAAO,IAAI,EAAE,UAAU,EAAE,EAAE,SAAS,EAAE,CAAC;QAC1D,aAAa;QACb,WAAW;QACX,SAAS;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3202, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3228, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3291, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3386, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/forms/ticket-type-form.tsx"], "sourcesContent": ["/**\n * 票种表单组件\n * \n * 功能说明：\n * 1. 支持创建、编辑、查看三种模式\n * 2. 表单验证和错误处理\n * 3. 响应式设计\n * 4. 加载状态管理\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';\nimport { TicketType, CreateTicketTypeInput, UpdateTicketTypeInput } from '@/types';\n\n// ============================================================================\n// 表单验证模式\n// ============================================================================\n\nconst ticketTypeSchema = z.object({\n  ticket_type_name: z.string()\n    .min(1, '票种名称不能为空')\n    .max(100, '票种名称不能超过100个字符'),\n  category: z.string()\n    .max(50, '分类不能超过50个字符')\n    .optional()\n    .or(z.literal(''))\n});\n\ntype TicketTypeFormData = z.infer<typeof ticketTypeSchema>;\n\n// ============================================================================\n// 组件属性接口\n// ============================================================================\n\ninterface TicketTypeFormProps {\n  mode: 'create' | 'edit' | 'view';\n  initialData?: TicketType;\n  onSubmit: (data: CreateTicketTypeInput | UpdateTicketTypeInput) => Promise<void>;\n  onCancel: () => void;\n  loading?: boolean;\n  className?: string;\n}\n\n// ============================================================================\n// 票种表单组件实现\n// ============================================================================\n\nexport const TicketTypeForm: React.FC<TicketTypeFormProps> = ({\n  mode,\n  initialData,\n  onSubmit,\n  onCancel,\n  loading = false,\n  className\n}) => {\n  const [submitError, setSubmitError] = useState<string>('');\n  const [submitSuccess, setSubmitSuccess] = useState<string>('');\n\n  const isReadonly = mode === 'view';\n  const isEdit = mode === 'edit';\n  const isCreate = mode === 'create';\n\n  // 表单配置\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset\n  } = useForm<TicketTypeFormData>({\n    resolver: zodResolver(ticketTypeSchema),\n    defaultValues: {\n      ticket_type_name: initialData?.ticket_type_name || '',\n      category: initialData?.category || ''\n    }\n  });\n\n  // 重置表单数据\n  useEffect(() => {\n    if (initialData) {\n      reset({\n        ticket_type_name: initialData.ticket_type_name,\n        category: initialData.category || ''\n      });\n    }\n  }, [initialData, reset]);\n\n  // 表单提交处理\n  const handleFormSubmit = async (data: TicketTypeFormData) => {\n    try {\n      setSubmitError('');\n      setSubmitSuccess('');\n\n      // 清理空字符串字段\n      const cleanedData = {\n        ...data,\n        category: data.category?.trim() || null\n      };\n\n      await onSubmit(cleanedData);\n\n      if (isCreate) {\n        setSubmitSuccess('票种创建成功！');\n        reset(); // 重置表单\n      } else if (isEdit) {\n        setSubmitSuccess('票种更新成功！');\n      }\n    } catch (error) {\n      setSubmitError(error instanceof Error ? error.message : '操作失败，请重试');\n    }\n  };\n\n  // 获取表单标题\n  const getTitle = () => {\n    switch (mode) {\n      case 'create':\n        return '创建票种';\n      case 'edit':\n        return '编辑票种';\n      case 'view':\n        return '查看票种';\n      default:\n        return '票种信息';\n    }\n  };\n\n  // 获取表单描述\n  const getDescription = () => {\n    switch (mode) {\n      case 'create':\n        return '填写票种基本信息，创建新的票种记录';\n      case 'edit':\n        return '修改票种信息，更新现有记录';\n      case 'view':\n        return '查看票种的详细信息';\n      default:\n        return '';\n    }\n  };\n\n  return (\n    <Card className={className}>\n      <CardHeader>\n        <CardTitle>{getTitle()}</CardTitle>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n          {/* 错误提示 */}\n          {submitError && (\n            <Alert variant=\"destructive\">\n              <AlertCircle className=\"h-4 w-4\" />\n              <AlertDescription>{submitError}</AlertDescription>\n            </Alert>\n          )}\n\n          {/* 成功提示 */}\n          {submitSuccess && (\n            <Alert className=\"border-green-200 bg-green-50 text-green-800\">\n              <CheckCircle2 className=\"h-4 w-4\" />\n              <AlertDescription>{submitSuccess}</AlertDescription>\n            </Alert>\n          )}\n\n          <div className=\"grid gap-6\">\n          {/* 票种名称 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"ticket_type_name\" className=\"text-sm font-medium\">\n              票种名称 <span className=\"text-red-500\">*</span>\n            </Label>\n            <Input\n              id=\"ticket_type_name\"\n              {...register('ticket_type_name')}\n              placeholder=\"请输入票种名称\"\n              disabled={isReadonly || loading}\n              className={errors.ticket_type_name ? 'border-red-500' : ''}\n            />\n            {errors.ticket_type_name && (\n              <p className=\"text-sm text-red-500\">{errors.ticket_type_name.message}</p>\n            )}\n          </div>\n\n          {/* 分类 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"category\" className=\"text-sm font-medium\">\n              分类\n            </Label>\n            <Input\n              id=\"category\"\n              {...register('category')}\n              placeholder=\"请输入票种分类（可选）\"\n              disabled={isReadonly || loading}\n              className={errors.category ? 'border-red-500' : ''}\n            />\n            {errors.category && (\n              <p className=\"text-sm text-red-500\">{errors.category.message}</p>\n            )}\n            <p className=\"text-xs text-gray-500\">\n              例如：成人票、儿童票、学生票、老人票等\n            </p>\n          </div>\n\n          {/* 只读模式显示额外信息 */}\n          {isReadonly && initialData && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t\">\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-gray-600\">票种ID</Label>\n                <div className=\"text-sm text-gray-900\">{initialData.ticket_type_id}</div>\n              </div>\n            </div>\n          )}\n\n          {/* 表单按钮 */}\n          <div className=\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 pt-6\">\n            {!isReadonly && (\n              <Button\n                type=\"submit\"\n                disabled={loading || isSubmitting}\n                className=\"min-w-[100px]\"\n              >\n                {(loading || isSubmitting) && (\n                  <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n                )}\n                {isCreate ? '创建票种' : '保存更改'}\n              </Button>\n            )}\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={onCancel}\n              disabled={loading || isSubmitting}\n              className=\"mb-2 sm:mb-0\"\n            >\n              {isReadonly ? '关闭' : '取消'}\n            </Button>\n          </div>\n        </div>\n        </form>\n      </CardContent>\n    </Card>\n  );\n};\n\n// ============================================================================\n// 导出组件\n// ============================================================================\n\nexport default TicketTypeForm;\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;AAID;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAXA;;;;;;;;;;;;AAcA,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E,MAAM,mBAAmB,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,kBAAkB,6KAAA,CAAA,IAAC,CAAC,MAAM,GACvB,GAAG,CAAC,GAAG,YACP,GAAG,CAAC,KAAK;IACZ,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GACf,GAAG,CAAC,IAAI,eACR,QAAQ,GACR,EAAE,CAAC,6KAAA,CAAA,IAAC,CAAC,OAAO,CAAC;AAClB;AAqBO,MAAM,iBAAgD,CAAC,EAC5D,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,SAAS,EACV;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,aAAa,SAAS;IAC5B,MAAM,SAAS,SAAS;IACxB,MAAM,WAAW,SAAS;IAE1B,OAAO;IACP,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,kBAAkB,aAAa,oBAAoB;YACnD,UAAU,aAAa,YAAY;QACrC;IACF;IAEA,SAAS;IACT,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,MAAM;gBACJ,kBAAkB,YAAY,gBAAgB;gBAC9C,UAAU,YAAY,QAAQ,IAAI;YACpC;QACF;IACF,GAAG;QAAC;QAAa;KAAM;IAEvB,SAAS;IACT,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,eAAe;YACf,iBAAiB;YAEjB,WAAW;YACX,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,UAAU,KAAK,QAAQ,EAAE,UAAU;YACrC;YAEA,MAAM,SAAS;YAEf,IAAI,UAAU;gBACZ,iBAAiB;gBACjB,SAAS,OAAO;YAClB,OAAO,IAAI,QAAQ;gBACjB,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC1D;IACF;IAEA,SAAS;IACT,MAAM,WAAW;QACf,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW;;0BACf,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8BAAE;;;;;;;;;;;0BAEd,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU,aAAa;oBAAmB,WAAU;;wBAEvD,6BACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;;8CACb,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;wBAKtB,+BACC,8OAAC,iIAAA,CAAA,QAAK;4BAAC,WAAU;;8CACf,8OAAC,qNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC,iIAAA,CAAA,mBAAgB;8CAAE;;;;;;;;;;;;sCAIvB,8OAAC;4BAAI,WAAU;;8CAEf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAmB,WAAU;;gDAAsB;8DAC3D,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEtC,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,mBAAmB;4CAChC,aAAY;4CACZ,UAAU,cAAc;4CACxB,WAAW,OAAO,gBAAgB,GAAG,mBAAmB;;;;;;wCAEzD,OAAO,gBAAgB,kBACtB,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;8CAKxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAW,WAAU;sDAAsB;;;;;;sDAG1D,8OAAC,iIAAA,CAAA,QAAK;4CACJ,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,aAAY;4CACZ,UAAU,cAAc;4CACxB,WAAW,OAAO,QAAQ,GAAG,mBAAmB;;;;;;wCAEjD,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,QAAQ,CAAC,OAAO;;;;;;sDAE9D,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;gCAMtC,cAAc,6BACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DAAyB,YAAY,cAAc;;;;;;;;;;;;;;;;;8CAMxE,8OAAC;oCAAI,WAAU;;wCACZ,CAAC,4BACA,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,WAAW;4CACrB,WAAU;;gDAET,CAAC,WAAW,YAAY,mBACvB,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAEpB,WAAW,SAAS;;;;;;;sDAGzB,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,WAAW;4CACrB,WAAU;sDAET,aAAa,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnC;uCAMe", "debugId": null}}, {"offset": {"line": 3766, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/app/ticket-types/page.tsx"], "sourcesContent": ["/**\n * 票种管理页面\n * \n * 功能说明：\n * 1. 展示票种列表，支持分页、搜索、筛选\n * 2. 提供票种的增删改查功能\n * 3. 响应式设计，适配移动端\n * 4. 批量操作支持\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Edit, Trash2, Eye } from 'lucide-react';\nimport { MainLayout, PageContainer } from '@/components/layout/main-layout';\nimport { DataTable, ColumnDef } from '@/components/ui/data-table';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { LoadingPage } from '@/components/ui/loading';\nimport { DeleteConfirmDialog } from '@/components/ui/confirm-dialog';\nimport { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';\nimport { TicketTypeForm } from '@/components/forms/ticket-type-form';\nimport { TicketType, PaginatedResult, CreateTicketTypeInput, UpdateTicketTypeInput } from '@/types';\n\n// ============================================================================\n// 页面状态接口定义\n// ============================================================================\n\ninterface TicketTypesPageState {\n  data: PaginatedResult<TicketType> | null;\n  loading: boolean;\n  error: string | null;\n  searchText: string;\n  currentPage: number;\n  pageSize: number;\n}\n\ninterface DialogState {\n  type: 'create' | 'edit' | 'view' | null;\n  open: boolean;\n  data?: TicketType;\n}\n\ninterface DeleteState {\n  open: boolean;\n  ticketType: TicketType | null;\n}\n\n// ============================================================================\n// 票种管理页面组件\n// ============================================================================\n\nexport default function TicketTypesPage() {\n  // 页面状态\n  const [state, setState] = useState<TicketTypesPageState>({\n    data: null,\n    loading: true,\n    error: null,\n    searchText: '',\n    currentPage: 1,\n    pageSize: 10\n  });\n\n  // 对话框状态\n  const [dialogState, setDialogState] = useState<DialogState>({\n    type: null,\n    open: false\n  });\n\n  // 删除确认状态\n  const [deleteState, setDeleteState] = useState<DeleteState>({\n    open: false,\n    ticketType: null\n  });\n\n  // 表单加载状态\n  const [formLoading, setFormLoading] = useState(false);\n\n  // 获取票种列表\n  const fetchTicketTypes = async (page = state.currentPage, pageSize = state.pageSize, search = state.searchText) => {\n    try {\n      setState(prev => ({ ...prev, loading: true, error: null }));\n\n      const params = new URLSearchParams({\n        page: page.toString(),\n        pageSize: pageSize.toString()\n      });\n\n      if (search.trim()) {\n        params.append('ticket_type_name', search.trim());\n      }\n\n      const response = await fetch(`/api/ticket-types?${params}`);\n      const result = await response.json();\n\n      if (result.success) {\n        setState(prev => ({\n          ...prev,\n          data: result.data,\n          loading: false,\n          currentPage: page,\n          pageSize: pageSize,\n          searchText: search\n        }));\n      } else {\n        throw new Error(result.error?.message || '获取票种列表失败');\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : '未知错误'\n      }));\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchTicketTypes();\n  }, []);\n\n  // 搜索处理\n  const handleSearch = () => {\n    fetchTicketTypes(1, state.pageSize, state.searchText);\n  };\n\n  // 分页处理\n  const handlePageChange = (page: number, pageSize?: number) => {\n    fetchTicketTypes(page, pageSize || state.pageSize, state.searchText);\n  };\n\n  // 打开对话框\n  const openDialog = (type: 'create' | 'edit' | 'view', data?: TicketType) => {\n    setDialogState({\n      type,\n      open: true,\n      data\n    });\n  };\n\n  // 关闭对话框\n  const closeDialog = () => {\n    setDialogState({\n      type: null,\n      open: false,\n      data: undefined\n    });\n  };\n\n  // 表单提交处理\n  const handleFormSubmit = async (data: CreateTicketTypeInput | UpdateTicketTypeInput) => {\n    try {\n      setFormLoading(true);\n\n      const isEdit = dialogState.type === 'edit';\n      const url = isEdit ? `/api/ticket-types/${dialogState.data?.ticket_type_id}` : '/api/ticket-types';\n      const method = isEdit ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(data)\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        closeDialog();\n        fetchTicketTypes(); // 刷新列表\n      } else {\n        throw new Error(result.error?.message || '操作失败');\n      }\n    } catch (error) {\n      throw error; // 让表单组件处理错误显示\n    } finally {\n      setFormLoading(false);\n    }\n  };\n\n  // 删除票种\n  const handleDelete = async (ticketType: TicketType) => {\n    try {\n      const response = await fetch(`/api/ticket-types/${ticketType.ticket_type_id}`, {\n        method: 'DELETE'\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setDeleteState({ open: false, ticketType: null });\n        fetchTicketTypes(); // 刷新列表\n      } else {\n        throw new Error(result.error?.message || '删除失败');\n      }\n    } catch (error) {\n      console.error('删除票种失败:', error);\n      // 这里可以添加错误提示\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnDef<TicketType>[] = [\n    {\n      key: 'ticket_type_name',\n      title: '票种名称',\n      dataIndex: 'ticket_type_name',\n      sorter: true,\n      render: (value: string) => (\n        <span className=\"font-medium text-gray-900\">{value}</span>\n      )\n    },\n    {\n      key: 'category',\n      title: '分类',\n      dataIndex: 'category',\n      render: (value: string | null) => (\n        value ? (\n          <Badge variant=\"secondary\">{value}</Badge>\n        ) : (\n          <span className=\"text-gray-400\">未分类</span>\n        )\n      )\n    },\n    {\n      key: 'actions',\n      title: '操作',\n      width: 120,\n      render: (_, record: TicketType) => (\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => openDialog('view', record)}\n          >\n            <Eye className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => openDialog('edit', record)}\n          >\n            <Edit className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setDeleteState({ open: true, ticketType: record })}\n          >\n            <Trash2 className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      )\n    }\n  ];\n\n  // 加载状态\n  if (state.loading && !state.data) {\n    return (\n      <MainLayout currentPath=\"/ticket-types\">\n        <LoadingPage text=\"加载票种数据...\" />\n      </MainLayout>\n    );\n  }\n\n  // 错误状态\n  if (state.error && !state.data) {\n    return (\n      <MainLayout currentPath=\"/ticket-types\">\n        <PageContainer>\n          <div className=\"text-center py-12\">\n            <div className=\"text-red-500 text-lg font-medium mb-4\">\n              {state.error}\n            </div>\n            <Button onClick={() => fetchTicketTypes()}>\n              重新加载\n            </Button>\n          </div>\n        </PageContainer>\n      </MainLayout>\n    );\n  }\n\n  return (\n    <MainLayout currentPath=\"/ticket-types\">\n      <PageContainer\n        title=\"票种管理\"\n        description=\"管理票种信息，包括票种的基本信息和分类\"\n        actions={\n          <Button onClick={() => openDialog('create')}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            添加票种\n          </Button>\n        }\n      >\n        {/* 搜索和筛选 */}\n        <div className=\"flex gap-4 mb-6\">\n          <div className=\"flex-1 max-w-md\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"搜索票种名称...\"\n                value={state.searchText}\n                onChange={(e) => setState(prev => ({ ...prev, searchText: e.target.value }))}\n                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\n                className=\"pl-9\"\n              />\n            </div>\n          </div>\n          <Button onClick={handleSearch} variant=\"outline\">\n            搜索\n          </Button>\n        </div>\n\n        {/* 数据表格 */}\n        <DataTable\n          columns={columns}\n          data={state.data?.data || []}\n          loading={state.loading}\n          pagination={state.data ? {\n            current: state.currentPage,\n            pageSize: state.pageSize,\n            total: state.data.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            onChange: handlePageChange\n          } : undefined}\n          emptyText=\"暂无票种数据\"\n        />\n\n        {/* 表单对话框 */}\n        <Dialog open={dialogState.open} onOpenChange={closeDialog}>\n          <DialogContent className=\"max-w-2xl\">\n            <DialogTitle className=\"sr-only\">\n              {dialogState.type === 'create' && '创建票种'}\n              {dialogState.type === 'edit' && '编辑票种'}\n              {dialogState.type === 'view' && '查看票种'}\n            </DialogTitle>\n            <DialogDescription className=\"sr-only\">\n              {dialogState.type === 'create' && '创建新的票种信息'}\n              {dialogState.type === 'edit' && '编辑现有票种信息'}\n              {dialogState.type === 'view' && '查看票种详细信息'}\n            </DialogDescription>\n            {dialogState.type && (\n              <TicketTypeForm\n                mode={dialogState.type}\n                initialData={dialogState.data}\n                onSubmit={handleFormSubmit}\n                onCancel={closeDialog}\n                loading={formLoading}\n              />\n            )}\n          </DialogContent>\n        </Dialog>\n\n        {/* 删除确认对话框 */}\n        <DeleteConfirmDialog\n          open={deleteState.open}\n          onOpenChange={(open) => setDeleteState(prev => ({ ...prev, open }))}\n          itemName={deleteState.ticketType?.ticket_type_name}\n          itemType=\"票种\"\n          onConfirm={() => deleteState.ticketType && handleDelete(deleteState.ticketType)}\n        />\n      </PageContainer>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AA2Ce,SAAS;IACtB,OAAO;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;QACvD,MAAM;QACN,SAAS;QACT,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;IACZ;IAEA,QAAQ;IACR,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM;QACN,MAAM;IACR;IAEA,SAAS;IACT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM;QACN,YAAY;IACd;IAEA,SAAS;IACT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,SAAS;IACT,MAAM,mBAAmB,OAAO,OAAO,MAAM,WAAW,EAAE,WAAW,MAAM,QAAQ,EAAE,SAAS,MAAM,UAAU;QAC5G,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,UAAU,SAAS,QAAQ;YAC7B;YAEA,IAAI,OAAO,IAAI,IAAI;gBACjB,OAAO,MAAM,CAAC,oBAAoB,OAAO,IAAI;YAC/C;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC1D,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,MAAM,OAAO,IAAI;wBACjB,SAAS;wBACT,aAAa;wBACb,UAAU;wBACV,YAAY;oBACd,CAAC;YACH,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF;IAEA,QAAQ;IACR,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,OAAO;IACP,MAAM,eAAe;QACnB,iBAAiB,GAAG,MAAM,QAAQ,EAAE,MAAM,UAAU;IACtD;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC,MAAc;QACtC,iBAAiB,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM,UAAU;IACrE;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC,MAAkC;QACpD,eAAe;YACb;YACA,MAAM;YACN;QACF;IACF;IAEA,QAAQ;IACR,MAAM,cAAc;QAClB,eAAe;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,eAAe;YAEf,MAAM,SAAS,YAAY,IAAI,KAAK;YACpC,MAAM,MAAM,SAAS,CAAC,kBAAkB,EAAE,YAAY,IAAI,EAAE,gBAAgB,GAAG;YAC/E,MAAM,SAAS,SAAS,QAAQ;YAEhC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB;gBACA,oBAAoB,OAAO;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,MAAM,OAAO,cAAc;QAC7B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,OAAO;IACP,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,WAAW,cAAc,EAAE,EAAE;gBAC7E,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,eAAe;oBAAE,MAAM;oBAAO,YAAY;gBAAK;gBAC/C,oBAAoB,OAAO;YAC7B,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QACzB,aAAa;QACf;IACF;IAEA,QAAQ;IACR,MAAM,UAAmC;QACvC;YACE,KAAK;YACL,OAAO;YACP,WAAW;YACX,QAAQ;YACR,QAAQ,CAAC,sBACP,8OAAC;oBAAK,WAAU;8BAA6B;;;;;;QAEjD;QACA;YACE,KAAK;YACL,OAAO;YACP,WAAW;YACX,QAAQ,CAAC,QACP,sBACE,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAa;;;;;yCAE5B,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;QAGtC;QACA;YACE,KAAK;YACL,OAAO;YACP,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,WAAW,QAAQ;sCAElC,cAAA,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,WAAW,QAAQ;sCAElC,cAAA,8OAAC,2MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,eAAe;oCAAE,MAAM;oCAAM,YAAY;gCAAO;sCAE/D,cAAA,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;QAI1B;KACD;IAED,OAAO;IACP,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE;QAChC,qBACE,8OAAC,8IAAA,CAAA,aAAU;YAAC,aAAY;sBACtB,cAAA,8OAAC,mIAAA,CAAA,cAAW;gBAAC,MAAK;;;;;;;;;;;IAGxB;IAEA,OAAO;IACP,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,EAAE;QAC9B,qBACE,8OAAC,8IAAA,CAAA,aAAU;YAAC,aAAY;sBACtB,cAAA,8OAAC,8IAAA,CAAA,gBAAa;0BACZ,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK;;;;;;sCAEd,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM;sCAAoB;;;;;;;;;;;;;;;;;;;;;;IAOrD;IAEA,qBACE,8OAAC,8IAAA,CAAA,aAAU;QAAC,aAAY;kBACtB,cAAA,8OAAC,8IAAA,CAAA,gBAAa;YACZ,OAAM;YACN,aAAY;YACZ,uBACE,8OAAC,kIAAA,CAAA,SAAM;gBAAC,SAAS,IAAM,WAAW;;kCAChC,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;8BAMrC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO,MAAM,UAAU;wCACvB,UAAU,CAAC,IAAM,SAAS,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;wCACvC,WAAU;;;;;;;;;;;;;;;;;sCAIhB,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAc,SAAQ;sCAAU;;;;;;;;;;;;8BAMnD,8OAAC,yIAAA,CAAA,YAAS;oBACR,SAAS;oBACT,MAAM,MAAM,IAAI,EAAE,QAAQ,EAAE;oBAC5B,SAAS,MAAM,OAAO;oBACtB,YAAY,MAAM,IAAI,GAAG;wBACvB,SAAS,MAAM,WAAW;wBAC1B,UAAU,MAAM,QAAQ;wBACxB,OAAO,MAAM,IAAI,CAAC,KAAK;wBACvB,iBAAiB;wBACjB,iBAAiB;wBACjB,UAAU;oBACZ,IAAI;oBACJ,WAAU;;;;;;8BAIZ,8OAAC,kIAAA,CAAA,SAAM;oBAAC,MAAM,YAAY,IAAI;oBAAE,cAAc;8BAC5C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,YAAY,IAAI,KAAK,YAAY;oCACjC,YAAY,IAAI,KAAK,UAAU;oCAC/B,YAAY,IAAI,KAAK,UAAU;;;;;;;0CAElC,8OAAC,kIAAA,CAAA,oBAAiB;gCAAC,WAAU;;oCAC1B,YAAY,IAAI,KAAK,YAAY;oCACjC,YAAY,IAAI,KAAK,UAAU;oCAC/B,YAAY,IAAI,KAAK,UAAU;;;;;;;4BAEjC,YAAY,IAAI,kBACf,8OAAC,qJAAA,CAAA,iBAAc;gCACb,MAAM,YAAY,IAAI;gCACtB,aAAa,YAAY,IAAI;gCAC7B,UAAU;gCACV,UAAU;gCACV,SAAS;;;;;;;;;;;;;;;;;8BAOjB,8OAAC,6IAAA,CAAA,sBAAmB;oBAClB,MAAM,YAAY,IAAI;oBACtB,cAAc,CAAC,OAAS,eAAe,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE;4BAAK,CAAC;oBACjE,UAAU,YAAY,UAAU,EAAE;oBAClC,UAAS;oBACT,WAAW,IAAM,YAAY,UAAU,IAAI,aAAa,YAAY,UAAU;;;;;;;;;;;;;;;;;AAKxF", "debugId": null}}]}