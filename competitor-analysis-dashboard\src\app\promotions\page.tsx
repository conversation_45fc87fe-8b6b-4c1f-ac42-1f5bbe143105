/**
 * 促销活动管理页面
 * 
 * 功能说明：
 * 1. 展示促销活动列表，支持分页、搜索、筛选
 * 2. 提供促销活动的增删改查功能
 * 3. 响应式设计，适配移动端
 * 4. 复杂的筛选条件支持
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Edit, Trash2, Eye, Calendar, DollarSign } from 'lucide-react';
import { MainLayout, PageContainer } from '@/components/layout/main-layout';
import { DataTable, ColumnDef } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { LoadingPage } from '@/components/ui/loading';
import { DeleteConfirmDialog } from '@/components/ui/confirm-dialog';
import { Dialog, DialogTitle, DialogDescription, ScrollableFormDialog } from '@/components/ui/dialog';
import { PromotionForm } from '@/components/forms/promotion-form';
import { Promotion, PaginatedResult, CreatePromotionInput, UpdatePromotionInput } from '@/types';

// ============================================================================
// 页面状态接口定义
// ============================================================================

interface PromotionsPageState {
  data: PaginatedResult<Promotion> | null;
  loading: boolean;
  error: string | null;
  searchText: string;
  currentPage: number;
  pageSize: number;
}

interface DialogState {
  type: 'create' | 'edit' | 'view' | null;
  open: boolean;
  data?: Promotion;
}

interface DeleteState {
  open: boolean;
  promotion: Promotion | null;
}

// ============================================================================
// 促销活动管理页面组件
// ============================================================================

export default function PromotionsPage() {
  // 页面状态
  const [state, setState] = useState<PromotionsPageState>({
    data: null,
    loading: true,
    error: null,
    searchText: '',
    currentPage: 1,
    pageSize: 10
  });

  // 对话框状态
  const [dialogState, setDialogState] = useState<DialogState>({
    type: null,
    open: false
  });

  // 删除确认状态
  const [deleteState, setDeleteState] = useState<DeleteState>({
    open: false,
    promotion: null
  });

  // 表单加载状态
  const [formLoading, setFormLoading] = useState(false);

  // 获取促销活动列表
  const fetchPromotions = async (page = state.currentPage, pageSize = state.pageSize, search = state.searchText) => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const params = new URLSearchParams({
        page: page.toString(),
        pageSize: pageSize.toString()
      });

      if (search.trim()) {
        params.append('activity_name', search.trim());
      }

      const response = await fetch(`/api/promotions?${params}`);
      const result = await response.json();

      if (result.success) {
        setState(prev => ({
          ...prev,
          data: result.data,
          loading: false,
          currentPage: page,
          pageSize: pageSize,
          searchText: search
        }));
      } else {
        throw new Error(result.error?.message || '获取促销活动列表失败');
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '未知错误'
      }));
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchPromotions();
  }, []);

  // 搜索处理
  const handleSearch = () => {
    fetchPromotions(1, state.pageSize, state.searchText);
  };

  // 分页处理
  const handlePageChange = (page: number, pageSize?: number) => {
    fetchPromotions(page, pageSize || state.pageSize, state.searchText);
  };

  // 打开对话框
  const openDialog = (type: 'create' | 'edit' | 'view', data?: Promotion) => {
    setDialogState({
      type,
      open: true,
      data
    });
  };

  // 关闭对话框
  const closeDialog = () => {
    setDialogState({
      type: null,
      open: false,
      data: undefined
    });
  };

  // 表单提交处理
  const handleFormSubmit = async (data: CreatePromotionInput | UpdatePromotionInput) => {
    try {
      setFormLoading(true);

      const isEdit = dialogState.type === 'edit';
      const url = isEdit ? `/api/promotions/${dialogState.data?.promotion_id}` : '/api/promotions';
      const method = isEdit ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      });

      const result = await response.json();

      if (result.success) {
        closeDialog();
        fetchPromotions(); // 刷新列表
      } else {
        throw new Error(result.error?.message || '操作失败');
      }
    } catch (error) {
      throw error; // 让表单组件处理错误显示
    } finally {
      setFormLoading(false);
    }
  };

  // 删除促销活动
  const handleDelete = async (promotion: Promotion) => {
    try {
      const response = await fetch(`/api/promotions/${promotion.promotion_id}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.success) {
        setDeleteState({ open: false, promotion: null });
        fetchPromotions(); // 刷新列表
      } else {
        throw new Error(result.error?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除促销活动失败:', error);
      // 这里可以添加错误提示
    }
  };

  // 格式化价格
  const formatPrice = (price: number | null | undefined) => {
    if (price === null || price === undefined || typeof price !== 'number') return '-';
    return `¥${price.toFixed(2)}`;
  };

  // 格式化日期
  const formatDate = (date: string | null) => {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('zh-CN');
  };

  // 计算折扣率
  const calculateDiscount = (rackRate: number | null | undefined, promoPrice: number | null | undefined) => {
    if (!rackRate || !promoPrice || typeof rackRate !== 'number' || typeof promoPrice !== 'number' || rackRate <= 0) return '-';
    const discount = ((rackRate - promoPrice) / rackRate * 100);
    return `${discount.toFixed(1)}%`;
  };

  // 获取活动状态
  const getActivityStatus = (promotion: Promotion) => {
    const now = new Date();
    const saleStart = promotion.sale_start_date ? new Date(promotion.sale_start_date) : null;
    const saleEnd = promotion.sale_end_date ? new Date(promotion.sale_end_date) : null;

    if (saleEnd && saleEnd < now) {
      return <Badge variant="secondary">已结束</Badge>;
    }
    if (saleStart && saleStart > now) {
      return <Badge variant="outline">未开始</Badge>;
    }
    return <Badge variant="default">进行中</Badge>;
  };

  // 表格列定义
  const columns: ColumnDef<Promotion>[] = [
    {
      key: 'activity_name',
      title: '活动名称',
      dataIndex: 'activity_name',
      sorter: true,
      render: (value: string) => (
        <span className="font-medium text-gray-900">{value}</span>
      )
    },
    {
      key: 'competitor',
      title: '竞品',
      render: (_, record: Promotion) => (
        <span className="text-sm text-gray-600">
          {record.competitor?.competitor_name || '-'}
        </span>
      )
    },
    {
      key: 'ticket_type',
      title: '票种',
      render: (_, record: Promotion) => (
        <span className="text-sm text-gray-600">
          {record.ticket_type?.ticket_type_name || '-'}
        </span>
      )
    },
    {
      key: 'price',
      title: '价格',
      render: (_, record: Promotion) => (
        <div className="text-sm">
          <div className="flex items-center gap-2">
            <span className="text-gray-500">原价:</span>
            <span>{formatPrice(record.rack_rate)}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-500">促销:</span>
            <span className="font-medium text-red-600">{formatPrice(record.promo_price)}</span>
          </div>
        </div>
      )
    },
    {
      key: 'discount',
      title: '折扣',
      render: (_, record: Promotion) => (
        <Badge variant="outline">
          {calculateDiscount(record.rack_rate, record.promo_price)}
        </Badge>
      )
    },
    {
      key: 'sale_period',
      title: '销售期',
      render: (_, record: Promotion) => (
        <div className="text-xs text-gray-600">
          <div>{formatDate(record.sale_start_date)}</div>
          <div>至</div>
          <div>{formatDate(record.sale_end_date)}</div>
        </div>
      )
    },
    {
      key: 'status',
      title: '状态',
      render: (_, record: Promotion) => getActivityStatus(record)
    },
    {
      key: 'actions',
      title: '操作',
      width: 120,
      render: (_, record: Promotion) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openDialog('view', record)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => openDialog('edit', record)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setDeleteState({ open: true, promotion: record })}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  // 加载状态
  if (state.loading && !state.data) {
    return (
      <MainLayout currentPath="/promotions">
        <LoadingPage text="加载促销活动数据..." />
      </MainLayout>
    );
  }

  // 错误状态
  if (state.error && !state.data) {
    return (
      <MainLayout currentPath="/promotions">
        <PageContainer>
          <div className="text-center py-12">
            <div className="text-red-500 text-lg font-medium mb-4">
              {state.error}
            </div>
            <Button onClick={() => fetchPromotions()}>
              重新加载
            </Button>
          </div>
        </PageContainer>
      </MainLayout>
    );
  }

  return (
    <MainLayout currentPath="/promotions">
      <PageContainer
        title="促销活动管理"
        description="管理促销活动信息，包括活动的价格、时间和规则"
        actions={
          <Button onClick={() => openDialog('create')}>
            <Plus className="h-4 w-4 mr-2" />
            添加促销活动
          </Button>
        }
      >
        {/* 搜索和筛选 */}
        <div className="flex gap-4 mb-6">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="搜索活动名称..."
                value={state.searchText}
                onChange={(e) => setState(prev => ({ ...prev, searchText: e.target.value }))}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-9"
              />
            </div>
          </div>
          <Button onClick={handleSearch} variant="outline">
            搜索
          </Button>
        </div>

        {/* 数据表格 */}
        <DataTable
          columns={columns}
          data={state.data?.data || []}
          loading={state.loading}
          pagination={state.data ? {
            current: state.currentPage,
            pageSize: state.pageSize,
            total: state.data.total,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: handlePageChange
          } : undefined}
          emptyText="暂无促销活动数据"
        />

        {/* 表单对话框 */}
        <Dialog open={dialogState.open} onOpenChange={closeDialog}>
          <ScrollableFormDialog className="max-w-4xl">
            <DialogTitle className="sr-only">
              {dialogState.type === 'create' && '添加促销活动'}
              {dialogState.type === 'edit' && '编辑促销活动'}
              {dialogState.type === 'view' && '查看促销活动'}
            </DialogTitle>
            <DialogDescription className="sr-only">
              {dialogState.type === 'create' && '创建新的促销活动信息'}
              {dialogState.type === 'edit' && '编辑现有促销活动信息'}
              {dialogState.type === 'view' && '查看促销活动详细信息'}
            </DialogDescription>
            {dialogState.type && (
              <PromotionForm
                mode={dialogState.type}
                initialData={dialogState.data}
                onSubmit={handleFormSubmit}
                onCancel={closeDialog}
                loading={formLoading}
              />
            )}
          </ScrollableFormDialog>
        </Dialog>

        {/* 删除确认对话框 */}
        <DeleteConfirmDialog
          open={deleteState.open}
          onOpenChange={(open) => setDeleteState(prev => ({ ...prev, open }))}
          itemName={deleteState.promotion?.activity_name}
          itemType="促销活动"
          onConfirm={() => deleteState.promotion && handleDelete(deleteState.promotion)}
        />
      </PageContainer>
    </MainLayout>
  );
}
