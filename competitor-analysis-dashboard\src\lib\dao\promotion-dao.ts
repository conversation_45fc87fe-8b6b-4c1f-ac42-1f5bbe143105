/**
 * 促销活动数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 实现促销活动表的CRUD操作
 * 2. 提供复杂的关联查询（竞品、票种）
 * 3. 支持多维度筛选和搜索
 * 4. 提供促销活动统计和分析功能
 */

import { BaseDAO } from './base-dao';
import { executeQuery } from '../database';
import { 
  Promotion, 
  PromotionDetail,
  CreatePromotionInput, 
  UpdatePromotionInput,
  PromotionFilters,
  PaginationParams,
  PaginatedResult
} from '@/types';

/**
 * 促销活动DAO类
 * 继承基础DAO类，实现促销活动特有的数据操作
 */
export class PromotionDAO extends BaseDAO<Promotion, CreatePromotionInput, UpdatePromotionInput> {
  constructor() {
    super('Promotions', 'promotion_id');
  }

  /**
   * 根据筛选条件查询促销活动列表（分页，包含关联数据）
   * @param params 分页参数
   * @param filters 筛选条件
   * @returns 分页查询结果
   */
  async findWithFilters(
    params: PaginationParams,
    filters: PromotionFilters = {}
  ): Promise<PaginatedResult<PromotionDetail>> {
    try {
      console.log('🔍 根据筛选条件查询促销活动列表');
      console.log('📝 筛选条件:', filters);

      const whereConditions: string[] = [];
      const whereParams: any[] = [];

      // 按竞品筛选
      if (filters.competitor_id) {
        whereConditions.push('p.competitor_id = ?');
        whereParams.push(filters.competitor_id);
      }

      // 按票种筛选
      if (filters.ticket_type_id) {
        whereConditions.push('p.ticket_type_id = ?');
        whereParams.push(filters.ticket_type_id);
      }

      // 按活动名称搜索
      if (filters.activity_name) {
        whereConditions.push('p.activity_name LIKE ?');
        whereParams.push(`%${filters.activity_name}%`);
      }

      // 按销售渠道筛选
      if (filters.sales_channel) {
        whereConditions.push('p.sales_channel LIKE ?');
        whereParams.push(`%${filters.sales_channel}%`);
      }

      // 按销售日期范围筛选
      if (filters.sale_date_start) {
        whereConditions.push('p.sale_start_date >= ?');
        whereParams.push(filters.sale_date_start);
      }
      if (filters.sale_date_end) {
        whereConditions.push('p.sale_end_date <= ?');
        whereParams.push(filters.sale_date_end);
      }

      // 按使用日期范围筛选
      if (filters.use_date_start) {
        whereConditions.push('p.use_start_date >= ?');
        whereParams.push(filters.use_date_start);
      }
      if (filters.use_date_end) {
        whereConditions.push('p.use_end_date <= ?');
        whereParams.push(filters.use_date_end);
      }

      // 按价格范围筛选
      if (filters.min_price) {
        whereConditions.push('p.promo_price >= ?');
        whereParams.push(filters.min_price);
      }
      if (filters.max_price) {
        whereConditions.push('p.promo_price <= ?');
        whereParams.push(filters.max_price);
      }

      const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';

      return await this.findWithPaginationAndJoins(params, whereClause, whereParams);
    } catch (error) {
      console.error('❌ 根据筛选条件查询促销活动失败:', error);
      throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 分页查询促销活动（包含关联的竞品和票种信息）
   * @param params 分页参数
   * @param whereClause WHERE子句
   * @param whereParams WHERE参数
   * @returns 分页查询结果
   */
  private async findWithPaginationAndJoins(
    params: PaginationParams,
    whereClause: string = '',
    whereParams: any[] = []
  ): Promise<PaginatedResult<PromotionDetail>> {
    try {
      const { page, pageSize, sortBy = 'p.promotion_id', sortOrder = 'DESC' } = params;
      const offset = (page - 1) * pageSize;

      // 构建WHERE子句
      const whereSQL = whereClause ? `WHERE ${whereClause}` : '';

      // 查询总数
      const countQuery = `
        SELECT COUNT(*) as total 
        FROM Promotions p
        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id
        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id
        ${whereSQL}
      `;
      const countResults = await executeQuery<{ total: number }>(countQuery, whereParams);
      const total = countResults[0].total;

      // 查询数据（包含关联信息）
      const dataQuery = `
        SELECT
          p.*,
          c.competitor_name,
          c.city,
          c.park_type,
          c.is_active as competitor_is_active,
          tt.ticket_type_name,
          tt.category as ticket_category,
          CASE
            WHEN p.rack_rate > 0 AND p.promo_price > 0
            THEN ROUND((p.rack_rate - p.promo_price) / p.rack_rate, 4)
            ELSE NULL
          END as discount_rate
        FROM Promotions p
        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id
        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id
        ${whereSQL}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ${pageSize} OFFSET ${offset}
      `;

      const dataResults = await executeQuery<any>(dataQuery, whereParams);

      // 转换数据格式
      const promotions: PromotionDetail[] = dataResults.map(row => ({
        promotion_id: row.promotion_id,
        competitor_id: row.competitor_id,
        ticket_type_id: row.ticket_type_id,
        activity_name: row.activity_name,
        rack_rate: row.rack_rate,
        promo_price: row.promo_price,
        sale_start_date: row.sale_start_date,
        sale_end_date: row.sale_end_date,
        use_start_date: row.use_start_date,
        use_end_date: row.use_end_date,
        sales_channel: row.sales_channel,
        usage_rules: row.usage_rules,
        data_source_url: row.data_source_url,
        entry_date: row.entry_date,
        remarks: row.remarks,
        competitor: row.competitor_name ? {
          competitor_id: row.competitor_id,
          competitor_name: row.competitor_name,
          city: row.city,
          park_type: row.park_type,
          is_active: row.competitor_is_active
        } : undefined,
        ticket_type: row.ticket_type_name ? {
          ticket_type_id: row.ticket_type_id,
          ticket_type_name: row.ticket_type_name,
          category: row.ticket_category
        } : undefined,
        discount_rate: row.discount_rate
      }));

      const totalPages = Math.ceil(total / pageSize);

      console.log(`✅ 分页查询成功 - 总计${total}条记录，当前页${promotions.length}条`);

      return {
        data: promotions,
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      console.error('❌ 分页查询促销活动失败:', error);
      throw new Error(`分页查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据ID查询促销活动详情（包含关联数据）
   * @param promotionId 促销活动ID
   * @returns 促销活动详情
   */
  async findDetailById(promotionId: number): Promise<PromotionDetail | null> {
    try {
      console.log(`🔍 查询促销活动详情，ID: ${promotionId}`);

      const query = `
        SELECT 
          p.*,
          c.competitor_name,
          c.city,
          c.park_type,
          c.is_active as competitor_is_active,
          tt.ticket_type_name,
          tt.category as ticket_category,
          CASE 
            WHEN p.rack_rate > 0 AND p.promo_price > 0 
            THEN ROUND((p.rack_rate - p.promo_price) / p.rack_rate, 4)
            ELSE NULL 
          END as discount_rate
        FROM Promotions p
        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id
        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id
        WHERE p.promotion_id = ?
      `;

      const results = await executeQuery<any>(query, [promotionId]);

      if (results.length === 0) {
        console.log(`⚠️ 未找到ID为${promotionId}的促销活动`);
        return null;
      }

      const row = results[0];
      const promotion: PromotionDetail = {
        promotion_id: row.promotion_id,
        competitor_id: row.competitor_id,
        ticket_type_id: row.ticket_type_id,
        activity_name: row.activity_name,
        rack_rate: row.rack_rate,
        promo_price: row.promo_price,
        sale_start_date: row.sale_start_date,
        sale_end_date: row.sale_end_date,
        use_start_date: row.use_start_date,
        use_end_date: row.use_end_date,
        sales_channel: row.sales_channel,
        usage_rules: row.usage_rules,
        data_source_url: row.data_source_url,
        entry_date: row.entry_date,
        remarks: row.remarks,
        competitor: row.competitor_name ? {
          competitor_id: row.competitor_id,
          competitor_name: row.competitor_name,
          city: row.city,
          park_type: row.park_type,
          is_active: row.competitor_is_active
        } : undefined,
        ticket_type: row.ticket_type_name ? {
          ticket_type_id: row.ticket_type_id,
          ticket_type_name: row.ticket_type_name,
          category: row.ticket_category
        } : undefined,
        discount_rate: row.discount_rate
      };

      console.log('✅ 成功查询到促销活动详情');
      return promotion;
    } catch (error) {
      console.error('❌ 查询促销活动详情失败:', error);
      throw new Error(`查询详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据竞品ID查询促销活动列表
   * @param competitorId 竞品ID
   * @param limit 返回数量限制
   * @returns 促销活动列表
   */
  async findByCompetitorId(competitorId: number, limit: number = 50): Promise<PromotionDetail[]> {
    try {
      console.log(`🔍 查询竞品${competitorId}的促销活动列表`);

      const params: PaginationParams = {
        page: 1,
        pageSize: limit,
        sortBy: 'p.entry_date',
        sortOrder: 'DESC'
      };

      const result = await this.findWithPaginationAndJoins(
        params,
        'p.competitor_id = ?',
        [competitorId]
      );

      console.log(`✅ 成功查询到${result.data.length}个促销活动`);
      return result.data;
    } catch (error) {
      console.error('❌ 根据竞品ID查询促销活动失败:', error);
      throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 根据票种ID查询促销活动列表
   * @param ticketTypeId 票种ID
   * @param limit 返回数量限制
   * @returns 促销活动列表
   */
  async findByTicketTypeId(ticketTypeId: number, limit: number = 50): Promise<PromotionDetail[]> {
    try {
      console.log(`🔍 查询票种${ticketTypeId}的促销活动列表`);

      const params: PaginationParams = {
        page: 1,
        pageSize: limit,
        sortBy: 'p.entry_date',
        sortOrder: 'DESC'
      };

      const result = await this.findWithPaginationAndJoins(
        params,
        'p.ticket_type_id = ?',
        [ticketTypeId]
      );

      console.log(`✅ 成功查询到${result.data.length}个促销活动`);
      return result.data;
    } catch (error) {
      console.error('❌ 根据票种ID查询促销活动失败:', error);
      throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取当前有效的促销活动（销售期内）
   * @param limit 返回数量限制
   * @returns 有效促销活动列表
   */
  async findCurrentActivePromotions(limit: number = 50): Promise<PromotionDetail[]> {
    try {
      console.log('🔍 查询当前有效的促销活动');

      const currentDate = new Date().toISOString().split('T')[0];
      const params: PaginationParams = {
        page: 1,
        pageSize: limit,
        sortBy: 'p.entry_date',
        sortOrder: 'DESC'
      };

      const whereClause = `
        (p.sale_start_date IS NULL OR p.sale_start_date <= ?) AND
        (p.sale_end_date IS NULL OR p.sale_end_date >= ?)
      `;

      const result = await this.findWithPaginationAndJoins(
        params,
        whereClause,
        [currentDate, currentDate]
      );

      console.log(`✅ 成功查询到${result.data.length}个当前有效的促销活动`);
      return result.data;
    } catch (error) {
      console.error('❌ 查询当前有效促销活动失败:', error);
      throw new Error(`查询有效促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取即将到期的促销活动
   * @param days 提前天数
   * @param limit 返回数量限制
   * @returns 即将到期的促销活动列表
   */
  async findExpiringPromotions(days: number = 7, limit: number = 50): Promise<PromotionDetail[]> {
    try {
      console.log(`🔍 查询${days}天内即将到期的促销活动`);

      const currentDate = new Date();
      const futureDate = new Date(currentDate.getTime() + days * 24 * 60 * 60 * 1000);

      const currentDateStr = currentDate.toISOString().split('T')[0];
      const futureDateStr = futureDate.toISOString().split('T')[0];

      const params: PaginationParams = {
        page: 1,
        pageSize: limit,
        sortBy: 'p.sale_end_date',
        sortOrder: 'ASC'
      };

      const whereClause = `
        p.sale_end_date IS NOT NULL AND
        p.sale_end_date >= ? AND
        p.sale_end_date <= ?
      `;

      const result = await this.findWithPaginationAndJoins(
        params,
        whereClause,
        [currentDateStr, futureDateStr]
      );

      console.log(`✅ 成功查询到${result.data.length}个即将到期的促销活动`);
      return result.data;
    } catch (error) {
      console.error('❌ 查询即将到期促销活动失败:', error);
      throw new Error(`查询即将到期促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取所有销售渠道列表
   * @returns 销售渠道列表
   */
  async getAllSalesChannels(): Promise<string[]> {
    try {
      console.log('🔍 获取所有销售渠道列表');

      const query = `
        SELECT DISTINCT sales_channel
        FROM Promotions
        WHERE sales_channel IS NOT NULL AND sales_channel != ''
        ORDER BY sales_channel ASC
      `;
      const results = await executeQuery<{ sales_channel: string }>(query);

      const channels = results.map(row => row.sales_channel);
      console.log(`✅ 成功获取${channels.length}个销售渠道`);
      return channels;
    } catch (error) {
      console.error('❌ 获取销售渠道列表失败:', error);
      throw new Error(`获取销售渠道列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取促销活动统计信息
   * @returns 统计信息
   */
  async getStatistics(): Promise<{
    total: number;
    activePromotions: number;
    expiredPromotions: number;
    avgDiscountRate: number;
    avgPrice: number;
    totalCompetitors: number;
    totalTicketTypes: number;
  }> {
    try {
      console.log('📊 获取促销活动统计信息');

      const currentDate = new Date().toISOString().split('T')[0];

      const queries = [
        'SELECT COUNT(*) as total FROM Promotions',
        `SELECT COUNT(*) as activePromotions FROM Promotions WHERE (sale_start_date IS NULL OR sale_start_date <= '${currentDate}') AND (sale_end_date IS NULL OR sale_end_date >= '${currentDate}')`,
        `SELECT COUNT(*) as expiredPromotions FROM Promotions WHERE sale_end_date IS NOT NULL AND sale_end_date < '${currentDate}'`,
        'SELECT AVG(CASE WHEN rack_rate > 0 AND promo_price > 0 THEN (rack_rate - promo_price) / rack_rate ELSE NULL END) as avgDiscountRate FROM Promotions',
        'SELECT AVG(promo_price) as avgPrice FROM Promotions WHERE promo_price IS NOT NULL',
        'SELECT COUNT(DISTINCT competitor_id) as totalCompetitors FROM Promotions',
        'SELECT COUNT(DISTINCT ticket_type_id) as totalTicketTypes FROM Promotions'
      ];

      const [
        totalResult,
        activeResult,
        expiredResult,
        discountResult,
        priceResult,
        competitorResult,
        ticketTypeResult
      ] = await Promise.all(
        queries.map(query => executeQuery<{ [key: string]: number }>(query))
      );

      const statistics = {
        total: totalResult[0].total,
        activePromotions: activeResult[0].activePromotions,
        expiredPromotions: expiredResult[0].expiredPromotions,
        avgDiscountRate: discountResult[0].avgDiscountRate ? parseFloat(discountResult[0].avgDiscountRate.toString()) : 0,
        avgPrice: priceResult[0].avgPrice ? parseFloat(priceResult[0].avgPrice.toString()) : 0,
        totalCompetitors: competitorResult[0].totalCompetitors,
        totalTicketTypes: ticketTypeResult[0].totalTicketTypes
      };

      console.log('✅ 成功获取促销活动统计信息:', statistics);
      return statistics;
    } catch (error) {
      console.error('❌ 获取促销活动统计信息失败:', error);
      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 获取热门竞品排行榜（按促销活动数量）
   * @param limit 返回数量限制
   * @returns 热门竞品列表
   */
  async getTopCompetitorsByPromotions(limit: number = 10): Promise<Array<{
    competitor_id: number;
    competitor_name: string;
    promotion_count: number;
    avg_price: number;
    avg_discount_rate: number;
  }>> {
    try {
      console.log(`📊 获取热门竞品排行榜，前${limit}名`);

      const query = `
        SELECT
          c.competitor_id,
          c.competitor_name,
          COUNT(p.promotion_id) as promotion_count,
          AVG(p.promo_price) as avg_price,
          AVG(CASE WHEN p.rack_rate > 0 AND p.promo_price > 0 THEN (p.rack_rate - p.promo_price) / p.rack_rate ELSE NULL END) as avg_discount_rate
        FROM Competitors c
        LEFT JOIN Promotions p ON c.competitor_id = p.competitor_id
        GROUP BY c.competitor_id, c.competitor_name
        ORDER BY promotion_count DESC, avg_price DESC
        LIMIT ?
      `;

      const results = await executeQuery<{
        competitor_id: number;
        competitor_name: string;
        promotion_count: number;
        avg_price: number;
        avg_discount_rate: number;
      }>(query, [limit]);

      console.log(`✅ 成功获取${results.length}个热门竞品`);
      return results;
    } catch (error) {
      console.error('❌ 获取热门竞品排行榜失败:', error);
      throw new Error(`获取排行榜失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }
}

// 导出单例实例
export const promotionDAO = new PromotionDAO();
