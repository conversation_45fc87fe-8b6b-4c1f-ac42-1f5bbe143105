{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/database.ts"], "sourcesContent": ["/**\n * 数据库连接配置和连接池管理\n * \n * 功能说明：\n * 1. 创建MySQL连接池，支持高并发访问\n * 2. 提供数据库连接的获取和释放机制\n * 3. 实现连接错误处理和重连机制\n * 4. 提供数据库健康检查功能\n */\n\nimport mysql from 'mysql2/promise';\n\n// 数据库连接配置接口定义\ninterface DatabaseConfig {\n  host: string;\n  port: number;\n  user: string;\n  password: string;\n  database: string;\n  connectionLimit: number;\n  queueLimit: number;\n  timeout: number;\n  acquireTimeout: number;\n}\n\n// 从环境变量获取数据库配置\nconst getDatabaseConfig = (): DatabaseConfig => {\n  const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];\n  \n  // 检查必需的环境变量\n  for (const envVar of requiredEnvVars) {\n    if (!process.env[envVar]) {\n      throw new Error(`缺少必需的环境变量: ${envVar}`);\n    }\n  }\n\n  return {\n    host: process.env.DB_HOST!,\n    port: parseInt(process.env.DB_PORT || '3306'),\n    user: process.env.DB_USER!,\n    password: process.env.DB_PASSWORD!,\n    database: process.env.DB_NAME!,\n    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10'),\n    queueLimit: parseInt(process.env.DB_QUEUE_LIMIT || '0'),\n    timeout: parseInt(process.env.DB_TIMEOUT || '60000'),\n    acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),\n  };\n};\n\n// 全局连接池变量\nlet connectionPool: mysql.Pool | null = null;\n\n/**\n * 创建数据库连接池\n * 使用单例模式确保整个应用只有一个连接池实例\n */\nexport const createConnectionPool = (): mysql.Pool => {\n  if (connectionPool) {\n    return connectionPool;\n  }\n\n  try {\n    const config = getDatabaseConfig();\n    \n    console.log('🔄 正在创建数据库连接池...');\n    console.log(`📍 数据库地址: ${config.host}:${config.port}`);\n    console.log(`🗄️  数据库名称: ${config.database}`);\n    console.log(`👤 用户名: ${config.user}`);\n    console.log(`🔗 连接池大小: ${config.connectionLimit}`);\n\n    connectionPool = mysql.createPool({\n      host: config.host,\n      port: config.port,\n      user: config.user,\n      password: config.password,\n      database: config.database,\n      waitForConnections: true,\n      connectionLimit: config.connectionLimit,\n      queueLimit: config.queueLimit,\n      timeout: config.timeout,\n      acquireTimeout: config.acquireTimeout,\n      // 启用多语句查询\n      multipleStatements: false,\n      // 字符集设置\n      charset: 'utf8mb4',\n      // 时区设置\n      timezone: '+08:00',\n      // 连接保活设置\n      keepAliveInitialDelay: 0,\n      enableKeepAlive: true,\n    });\n\n    console.log('✅ 数据库连接池创建成功');\n    return connectionPool;\n  } catch (error) {\n    console.error('❌ 创建数据库连接池失败:', error);\n    throw new Error(`数据库连接池创建失败: ${error instanceof Error ? error.message : '未知错误'}`);\n  }\n};\n\n/**\n * 获取数据库连接池实例\n */\nexport const getConnectionPool = (): mysql.Pool => {\n  if (!connectionPool) {\n    return createConnectionPool();\n  }\n  return connectionPool;\n};\n\n/**\n * 执行数据库查询\n * @param query SQL查询语句\n * @param params 查询参数\n * @returns 查询结果\n */\nexport const executeQuery = async <T = any>(\n  query: string,\n  params: any[] = []\n): Promise<T[]> => {\n  const pool = getConnectionPool();\n  \n  try {\n    console.log('🔍 执行SQL查询:', query);\n    console.log('📝 查询参数:', params);\n    \n    const [rows] = await pool.execute(query, params);\n    \n    console.log('✅ 查询执行成功');\n    return rows as T[];\n  } catch (error) {\n    console.error('❌ 数据库查询失败:', error);\n    console.error('🔍 失败的SQL:', query);\n    console.error('📝 查询参数:', params);\n    \n    throw new Error(`数据库查询失败: ${error instanceof Error ? error.message : '未知错误'}`);\n  }\n};\n\n/**\n * 执行数据库事务\n * @param callback 事务回调函数\n * @returns 事务执行结果\n */\nexport const executeTransaction = async <T>(\n  callback: (connection: mysql.PoolConnection) => Promise<T>\n): Promise<T> => {\n  const pool = getConnectionPool();\n  const connection = await pool.getConnection();\n  \n  try {\n    console.log('🔄 开始数据库事务');\n    await connection.beginTransaction();\n    \n    const result = await callback(connection);\n    \n    await connection.commit();\n    console.log('✅ 事务提交成功');\n    \n    return result;\n  } catch (error) {\n    console.error('❌ 事务执行失败，正在回滚:', error);\n    await connection.rollback();\n    throw error;\n  } finally {\n    connection.release();\n    console.log('🔄 数据库连接已释放');\n  }\n};\n\n/**\n * 检查数据库连接健康状态\n */\nexport const checkDatabaseHealth = async (): Promise<boolean> => {\n  try {\n    console.log('🏥 检查数据库连接健康状态...');\n    \n    const result = await executeQuery('SELECT 1 as health_check');\n    \n    if (result && result.length > 0) {\n      console.log('✅ 数据库连接健康');\n      return true;\n    } else {\n      console.log('⚠️ 数据库连接异常');\n      return false;\n    }\n  } catch (error) {\n    console.error('❌ 数据库健康检查失败:', error);\n    return false;\n  }\n};\n\n/**\n * 关闭数据库连接池\n * 通常在应用程序关闭时调用\n */\nexport const closeDatabasePool = async (): Promise<void> => {\n  if (connectionPool) {\n    try {\n      console.log('🔄 正在关闭数据库连接池...');\n      await connectionPool.end();\n      connectionPool = null;\n      console.log('✅ 数据库连接池已关闭');\n    } catch (error) {\n      console.error('❌ 关闭数据库连接池失败:', error);\n      throw error;\n    }\n  }\n};\n\n// 导出默认连接池实例\nexport default getConnectionPool;\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;AAED;;AAeA,eAAe;AACf,MAAM,oBAAoB;IACxB,MAAM,kBAAkB;QAAC;QAAW;QAAW;QAAe;KAAU;IAExE,YAAY;IACZ,KAAK,MAAM,UAAU,gBAAiB;QACpC,IAAI,CAAC,QAAQ,GAAG,CAAC,OAAO,EAAE;YACxB,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,QAAQ;QACxC;IACF;IAEA,OAAO;QACL,MAAM,QAAQ,GAAG,CAAC,OAAO;QACzB,MAAM,SAAS,QAAQ,GAAG,CAAC,OAAO,IAAI;QACtC,MAAM,QAAQ,GAAG,CAAC,OAAO;QACzB,UAAU,QAAQ,GAAG,CAAC,WAAW;QACjC,UAAU,QAAQ,GAAG,CAAC,OAAO;QAC7B,iBAAiB,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;QAC7D,YAAY,SAAS,QAAQ,GAAG,CAAC,cAAc,IAAI;QACnD,SAAS,SAAS,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC5C,gBAAgB,SAAS,QAAQ,GAAG,CAAC,kBAAkB,IAAI;IAC7D;AACF;AAEA,UAAU;AACV,IAAI,iBAAoC;AAMjC,MAAM,uBAAuB;IAClC,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,IAAI;QACF,MAAM,SAAS;QAEf,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE;QACrD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,EAAE;QAC5C,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;QACpC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,eAAe,EAAE;QAEjD,iBAAiB,mIAAA,CAAA,UAAK,CAAC,UAAU,CAAC;YAChC,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,oBAAoB;YACpB,iBAAiB,OAAO,eAAe;YACvC,YAAY,OAAO,UAAU;YAC7B,SAAS,OAAO,OAAO;YACvB,gBAAgB,OAAO,cAAc;YACrC,UAAU;YACV,oBAAoB;YACpB,QAAQ;YACR,SAAS;YACT,OAAO;YACP,UAAU;YACV,SAAS;YACT,uBAAuB;YACvB,iBAAiB;QACnB;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;IAClF;AACF;AAKO,MAAM,oBAAoB;IAC/B,IAAI,CAAC,gBAAgB;QACnB,OAAO;IACT;IACA,OAAO;AACT;AAQO,MAAM,eAAe,OAC1B,OACA,SAAgB,EAAE;IAElB,MAAM,OAAO;IAEb,IAAI;QACF,QAAQ,GAAG,CAAC,eAAe;QAC3B,QAAQ,GAAG,CAAC,YAAY;QAExB,MAAM,CAAC,KAAK,GAAG,MAAM,KAAK,OAAO,CAAC,OAAO;QAEzC,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,cAAc;QAC5B,QAAQ,KAAK,CAAC,cAAc;QAC5B,QAAQ,KAAK,CAAC,YAAY;QAE1B,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;IAC/E;AACF;AAOO,MAAM,qBAAqB,OAChC;IAEA,MAAM,OAAO;IACb,MAAM,aAAa,MAAM,KAAK,aAAa;IAE3C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,gBAAgB;QAEjC,MAAM,SAAS,MAAM,SAAS;QAE9B,MAAM,WAAW,MAAM;QACvB,QAAQ,GAAG,CAAC;QAEZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kBAAkB;QAChC,MAAM,WAAW,QAAQ;QACzB,MAAM;IACR,SAAU;QACR,WAAW,OAAO;QAClB,QAAQ,GAAG,CAAC;IACd;AACF;AAKO,MAAM,sBAAsB;IACjC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,SAAS,MAAM,aAAa;QAElC,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;YAC/B,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO;IACT;AACF;AAMO,MAAM,oBAAoB;IAC/B,IAAI,gBAAgB;QAClB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,eAAe,GAAG;YACxB,iBAAiB;YACjB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR;IACF;AACF;uCAGe", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/dao/base-dao.ts"], "sourcesContent": ["/**\n * 基础数据访问对象(DAO)类\n * \n * 功能说明：\n * 1. 提供通用的CRUD操作方法\n * 2. 实现分页查询和排序功能\n * 3. 提供事务支持和错误处理\n * 4. 作为其他DAO类的基类\n */\n\nimport { executeQuery, executeTransaction } from '../database';\nimport { PaginationParams, PaginatedResult } from '@/types';\nimport mysql from 'mysql2/promise';\n\n/**\n * 基础DAO抽象类\n * 提供通用的数据库操作方法\n */\nexport abstract class BaseDAO<T, CreateInput, UpdateInput> {\n  protected tableName: string;\n  protected primaryKey: string;\n\n  constructor(tableName: string, primaryKey: string = 'id') {\n    this.tableName = tableName;\n    this.primaryKey = primaryKey;\n  }\n\n  /**\n   * 根据ID查询单条记录\n   * @param id 记录ID\n   * @returns 查询结果\n   */\n  async findById(id: string | number): Promise<T | null> {\n    try {\n      console.log(`🔍 查询${this.tableName}表中ID为${id}的记录`);\n      \n      const query = `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;\n      const results = await executeQuery<T>(query, [id]);\n      \n      if (results.length === 0) {\n        console.log(`⚠️ 未找到ID为${id}的记录`);\n        return null;\n      }\n      \n      console.log(`✅ 成功查询到记录`);\n      return results[0];\n    } catch (error) {\n      console.error(`❌ 查询${this.tableName}记录失败:`, error);\n      throw new Error(`查询记录失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 查询所有记录\n   * @param orderBy 排序字段\n   * @param orderDirection 排序方向\n   * @returns 查询结果列表\n   */\n  async findAll(\n    orderBy: string = this.primaryKey,\n    orderDirection: 'ASC' | 'DESC' = 'ASC'\n  ): Promise<T[]> {\n    try {\n      console.log(`🔍 查询${this.tableName}表的所有记录`);\n      \n      const query = `SELECT * FROM ${this.tableName} ORDER BY ${orderBy} ${orderDirection}`;\n      const results = await executeQuery<T>(query);\n      \n      console.log(`✅ 成功查询到${results.length}条记录`);\n      return results;\n    } catch (error) {\n      console.error(`❌ 查询${this.tableName}所有记录失败:`, error);\n      throw new Error(`查询记录失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 分页查询记录\n   * @param params 分页参数\n   * @param whereClause WHERE子句\n   * @param whereParams WHERE参数\n   * @returns 分页查询结果\n   */\n  async findWithPagination(\n    params: PaginationParams,\n    whereClause: string = '',\n    whereParams: any[] = []\n  ): Promise<PaginatedResult<T>> {\n    try {\n      const { page, pageSize, sortBy = this.primaryKey, sortOrder = 'ASC' } = params;\n      const offset = (page - 1) * pageSize;\n      \n      console.log(`🔍 分页查询${this.tableName}表 - 第${page}页，每页${pageSize}条`);\n      \n      // 构建WHERE子句\n      const whereSQL = whereClause ? `WHERE ${whereClause}` : '';\n      \n      // 查询总数\n      const countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereSQL}`;\n      const countResults = await executeQuery<{ total: number }>(countQuery, whereParams);\n      const total = countResults[0].total;\n      \n      // 查询数据 - 使用字符串拼接避免参数类型问题\n      const dataQuery = `\n        SELECT * FROM ${this.tableName}\n        ${whereSQL}\n        ORDER BY ${sortBy} ${sortOrder}\n        LIMIT ${Number(pageSize)} OFFSET ${Number(offset)}\n      `;\n      const dataResults = await executeQuery<T>(dataQuery, whereParams);\n      \n      const totalPages = Math.ceil(total / pageSize);\n      \n      console.log(`✅ 分页查询成功 - 总计${total}条记录，当前页${dataResults.length}条`);\n      \n      return {\n        data: dataResults,\n        total,\n        page,\n        pageSize,\n        totalPages\n      };\n    } catch (error) {\n      console.error(`❌ 分页查询${this.tableName}失败:`, error);\n      throw new Error(`分页查询失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 创建新记录\n   * @param data 创建数据\n   * @returns 创建的记录ID\n   */\n  async create(data: CreateInput): Promise<number> {\n    try {\n      console.log(`➕ 创建${this.tableName}新记录`);\n      console.log('📝 创建数据:', data);\n      \n      const fields = Object.keys(data as any);\n      const values = Object.values(data as any);\n      const placeholders = fields.map(() => '?').join(', ');\n      \n      const query = `\n        INSERT INTO ${this.tableName} (${fields.join(', ')}) \n        VALUES (${placeholders})\n      `;\n      \n      const results = await executeQuery(query, values);\n      const insertId = (results as any).insertId;\n      \n      console.log(`✅ 成功创建记录，ID: ${insertId}`);\n      return insertId;\n    } catch (error) {\n      console.error(`❌ 创建${this.tableName}记录失败:`, error);\n      throw new Error(`创建记录失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 更新记录\n   * @param id 记录ID\n   * @param data 更新数据\n   * @returns 是否更新成功\n   */\n  async update(id: string | number, data: UpdateInput): Promise<boolean> {\n    try {\n      console.log(`✏️ 更新${this.tableName}记录，ID: ${id}`);\n      console.log('📝 更新数据:', data);\n      \n      const fields = Object.keys(data as any);\n      const values = Object.values(data as any);\n      \n      if (fields.length === 0) {\n        console.log('⚠️ 没有需要更新的字段');\n        return false;\n      }\n      \n      const setClause = fields.map(field => `${field} = ?`).join(', ');\n      const query = `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.primaryKey} = ?`;\n      \n      const results = await executeQuery(query, [...values, id]);\n      const affectedRows = (results as any).affectedRows;\n      \n      if (affectedRows === 0) {\n        console.log(`⚠️ 未找到ID为${id}的记录或数据未发生变化`);\n        return false;\n      }\n      \n      console.log(`✅ 成功更新记录，影响行数: ${affectedRows}`);\n      return true;\n    } catch (error) {\n      console.error(`❌ 更新${this.tableName}记录失败:`, error);\n      throw new Error(`更新记录失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 删除记录\n   * @param id 记录ID\n   * @returns 是否删除成功\n   */\n  async delete(id: string | number): Promise<boolean> {\n    try {\n      console.log(`🗑️ 删除${this.tableName}记录，ID: ${id}`);\n      \n      const query = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;\n      const results = await executeQuery(query, [id]);\n      const affectedRows = (results as any).affectedRows;\n      \n      if (affectedRows === 0) {\n        console.log(`⚠️ 未找到ID为${id}的记录`);\n        return false;\n      }\n      \n      console.log(`✅ 成功删除记录，影响行数: ${affectedRows}`);\n      return true;\n    } catch (error) {\n      console.error(`❌ 删除${this.tableName}记录失败:`, error);\n      throw new Error(`删除记录失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 批量删除记录\n   * @param ids 记录ID列表\n   * @returns 删除的记录数量\n   */\n  async batchDelete(ids: (string | number)[]): Promise<number> {\n    try {\n      if (ids.length === 0) {\n        console.log('⚠️ 没有需要删除的记录');\n        return 0;\n      }\n      \n      console.log(`🗑️ 批量删除${this.tableName}记录，数量: ${ids.length}`);\n      \n      const placeholders = ids.map(() => '?').join(', ');\n      const query = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} IN (${placeholders})`;\n      \n      const results = await executeQuery(query, ids);\n      const affectedRows = (results as any).affectedRows;\n      \n      console.log(`✅ 成功删除${affectedRows}条记录`);\n      return affectedRows;\n    } catch (error) {\n      console.error(`❌ 批量删除${this.tableName}记录失败:`, error);\n      throw new Error(`批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 检查记录是否存在\n   * @param id 记录ID\n   * @returns 是否存在\n   */\n  async exists(id: string | number): Promise<boolean> {\n    try {\n      const query = `SELECT 1 FROM ${this.tableName} WHERE ${this.primaryKey} = ? LIMIT 1`;\n      const results = await executeQuery(query, [id]);\n      return results.length > 0;\n    } catch (error) {\n      console.error(`❌ 检查${this.tableName}记录存在性失败:`, error);\n      throw new Error(`检查记录存在性失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取记录总数\n   * @param whereClause WHERE子句\n   * @param whereParams WHERE参数\n   * @returns 记录总数\n   */\n  async count(whereClause: string = '', whereParams: any[] = []): Promise<number> {\n    try {\n      const whereSQL = whereClause ? `WHERE ${whereClause}` : '';\n      const query = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereSQL}`;\n      \n      const results = await executeQuery<{ total: number }>(query, whereParams);\n      return results[0].total;\n    } catch (error) {\n      console.error(`❌ 统计${this.tableName}记录数量失败:`, error);\n      throw new Error(`统计记录数量失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 执行事务操作\n   * @param callback 事务回调函数\n   * @returns 事务执行结果\n   */\n  async executeInTransaction<R>(\n    callback: (connection: mysql.PoolConnection) => Promise<R>\n  ): Promise<R> {\n    return executeTransaction(callback);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;AAED;;AAQO,MAAe;IACV,UAAkB;IAClB,WAAmB;IAE7B,YAAY,SAAiB,EAAE,aAAqB,IAAI,CAAE;QACxD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA;;;;GAIC,GACD,MAAM,SAAS,EAAmB,EAAqB;QACrD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC;YAEjD,MAAM,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAC5E,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAK,OAAO;gBAAC;aAAG;YAEjD,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,GAAG,GAAG,CAAC;gBAC/B,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAC;YACvB,OAAO,OAAO,CAAC,EAAE;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5C,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;;GAKC,GACD,MAAM,QACJ,UAAkB,IAAI,CAAC,UAAU,EACjC,iBAAiC,KAAK,EACxB;QACd,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAE1C,MAAM,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,gBAAgB;YACrF,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAK;YAEtC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,GAAG,CAAC;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YAC9C,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;;;GAMC,GACD,MAAM,mBACJ,MAAwB,EACxB,cAAsB,EAAE,EACxB,cAAqB,EAAE,EACM;QAC7B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,IAAI,CAAC,UAAU,EAAE,YAAY,KAAK,EAAE,GAAG;YACxE,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAE5B,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,IAAI,EAAE,SAAS,CAAC,CAAC;YAElE,YAAY;YACZ,MAAM,WAAW,cAAc,CAAC,MAAM,EAAE,aAAa,GAAG;YAExD,OAAO;YACP,MAAM,aAAa,CAAC,8BAA8B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU;YAChF,MAAM,eAAe,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAqB,YAAY;YACvE,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,KAAK;YAEnC,yBAAyB;YACzB,MAAM,YAAY,CAAC;sBACH,EAAE,IAAI,CAAC,SAAS,CAAC;QAC/B,EAAE,SAAS;iBACF,EAAE,OAAO,CAAC,EAAE,UAAU;cACzB,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,QAAQ;MACpD,CAAC;YACD,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAK,WAAW;YAErD,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;YAErC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE,YAAY,MAAM,CAAC,CAAC,CAAC;YAEhE,OAAO;gBACL,MAAM;gBACN;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;YAC5C,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,OAAO,IAAiB,EAAmB;QAC/C,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACtC,QAAQ,GAAG,CAAC,YAAY;YAExB,MAAM,SAAS,OAAO,IAAI,CAAC;YAC3B,MAAM,SAAS,OAAO,MAAM,CAAC;YAC7B,MAAM,eAAe,OAAO,GAAG,CAAC,IAAM,KAAK,IAAI,CAAC;YAEhD,MAAM,QAAQ,CAAC;oBACD,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,MAAM;gBAC3C,EAAE,aAAa;MACzB,CAAC;YAED,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YAC1C,MAAM,WAAW,AAAC,QAAgB,QAAQ;YAE1C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,UAAU;YACtC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5C,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;;GAKC,GACD,MAAM,OAAO,EAAmB,EAAE,IAAiB,EAAoB;QACrE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI;YAChD,QAAQ,GAAG,CAAC,YAAY;YAExB,MAAM,SAAS,OAAO,IAAI,CAAC;YAC3B,MAAM,SAAS,OAAO,MAAM,CAAC;YAE7B,IAAI,OAAO,MAAM,KAAK,GAAG;gBACvB,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,MAAM,YAAY,OAAO,GAAG,CAAC,CAAA,QAAS,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC;YAC3D,MAAM,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAEtF,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;mBAAI;gBAAQ;aAAG;YACzD,MAAM,eAAe,AAAC,QAAgB,YAAY;YAElD,IAAI,iBAAiB,GAAG;gBACtB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,GAAG,WAAW,CAAC;gBACvC,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,cAAc;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5C,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,OAAO,EAAmB,EAAoB;QAClD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI;YAEjD,MAAM,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1E,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAAC;aAAG;YAC9C,MAAM,eAAe,AAAC,QAAgB,YAAY;YAElD,IAAI,iBAAiB,GAAG;gBACtB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,GAAG,GAAG,CAAC;gBAC/B,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,cAAc;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC5C,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,YAAY,GAAwB,EAAmB;QAC3D,IAAI;YACF,IAAI,IAAI,MAAM,KAAK,GAAG;gBACpB,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,MAAM,EAAE;YAE3D,MAAM,eAAe,IAAI,GAAG,CAAC,IAAM,KAAK,IAAI,CAAC;YAC7C,MAAM,QAAQ,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YAE3F,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YAC1C,MAAM,eAAe,AAAC,QAAgB,YAAY;YAElD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,GAAG,CAAC;YACtC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC9C,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,OAAO,EAAmB,EAAoB;QAClD,IAAI;YACF,MAAM,QAAQ,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;YACpF,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAAC;aAAG;YAC9C,OAAO,QAAQ,MAAM,GAAG;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE;YAC/C,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACjF;IACF;IAEA;;;;;GAKC,GACD,MAAM,MAAM,cAAsB,EAAE,EAAE,cAAqB,EAAE,EAAmB;QAC9E,IAAI;YACF,MAAM,WAAW,cAAc,CAAC,MAAM,EAAE,aAAa,GAAG;YACxD,MAAM,QAAQ,CAAC,8BAA8B,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,UAAU;YAE3E,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAqB,OAAO;YAC7D,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;YAC9C,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;GAIC,GACD,MAAM,qBACJ,QAA0D,EAC9C;QACZ,OAAO,CAAA,GAAA,wHAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/dao/competitor-dao.ts"], "sourcesContent": ["/**\n * 竞品数据访问对象(DAO)类\n * \n * 功能说明：\n * 1. 实现竞品表的CRUD操作\n * 2. 提供竞品特有的查询方法\n * 3. 支持按城市、园区类型等条件筛选\n * 4. 提供竞品活跃状态管理\n */\n\nimport { BaseDAO } from './base-dao';\nimport { executeQuery } from '../database';\nimport { \n  Competitor, \n  CreateCompetitorInput, \n  UpdateCompetitorInput,\n  CompetitorFilters,\n  PaginationParams,\n  PaginatedResult\n} from '@/types';\n\n/**\n * 竞品DAO类\n * 继承基础DAO类，实现竞品特有的数据操作\n */\nexport class CompetitorDAO extends BaseDAO<Competitor, CreateCompetitorInput, UpdateCompetitorInput> {\n  constructor() {\n    super('Competitors', 'competitor_id');\n  }\n\n  /**\n   * 根据筛选条件查询竞品列表（分页）\n   * @param params 分页参数\n   * @param filters 筛选条件\n   * @returns 分页查询结果\n   */\n  async findWithFilters(\n    params: PaginationParams,\n    filters: CompetitorFilters = {}\n  ): Promise<PaginatedResult<Competitor>> {\n    try {\n      console.log('🔍 根据筛选条件查询竞品列表');\n      console.log('📝 筛选条件:', filters);\n\n      const whereConditions: string[] = [];\n      const whereParams: any[] = [];\n\n      // 按竞品名称搜索\n      if (filters.competitor_name) {\n        whereConditions.push('competitor_name LIKE ?');\n        whereParams.push(`%${filters.competitor_name}%`);\n      }\n\n      // 按城市筛选\n      if (filters.city) {\n        whereConditions.push('city = ?');\n        whereParams.push(filters.city);\n      }\n\n      // 按园区类型筛选\n      if (filters.park_type) {\n        whereConditions.push('park_type = ?');\n        whereParams.push(filters.park_type);\n      }\n\n      // 按活跃状态筛选\n      if (filters.is_active !== undefined) {\n        whereConditions.push('is_active = ?');\n        whereParams.push(filters.is_active);\n      }\n\n      const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';\n\n      return await this.findWithPagination(params, whereClause, whereParams);\n    } catch (error) {\n      console.error('❌ 根据筛选条件查询竞品失败:', error);\n      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据竞品名称查询（精确匹配）\n   * @param competitorName 竞品名称\n   * @returns 竞品信息\n   */\n  async findByName(competitorName: string): Promise<Competitor | null> {\n    try {\n      console.log(`🔍 查询竞品名称为\"${competitorName}\"的记录`);\n\n      const query = 'SELECT * FROM Competitors WHERE competitor_name = ?';\n      const results = await executeQuery<Competitor>(query, [competitorName]);\n\n      if (results.length === 0) {\n        console.log(`⚠️ 未找到名称为\"${competitorName}\"的竞品`);\n        return null;\n      }\n\n      console.log('✅ 成功查询到竞品信息');\n      return results[0];\n    } catch (error) {\n      console.error('❌ 根据名称查询竞品失败:', error);\n      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取所有活跃的竞品\n   * @returns 活跃竞品列表\n   */\n  async findActiveCompetitors(): Promise<Competitor[]> {\n    try {\n      console.log('🔍 查询所有活跃的竞品');\n\n      const query = `\n        SELECT * FROM Competitors \n        WHERE is_active = true \n        ORDER BY competitor_name ASC\n      `;\n      const results = await executeQuery<Competitor>(query);\n\n      console.log(`✅ 成功查询到${results.length}个活跃竞品`);\n      return results;\n    } catch (error) {\n      console.error('❌ 查询活跃竞品失败:', error);\n      throw new Error(`查询活跃竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据城市获取竞品列表\n   * @param city 城市名称\n   * @returns 竞品列表\n   */\n  async findByCity(city: string): Promise<Competitor[]> {\n    try {\n      console.log(`🔍 查询城市\"${city}\"的竞品列表`);\n\n      const query = `\n        SELECT * FROM Competitors \n        WHERE city = ? \n        ORDER BY competitor_name ASC\n      `;\n      const results = await executeQuery<Competitor>(query, [city]);\n\n      console.log(`✅ 成功查询到${results.length}个竞品`);\n      return results;\n    } catch (error) {\n      console.error('❌ 根据城市查询竞品失败:', error);\n      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据园区类型获取竞品列表\n   * @param parkType 园区类型\n   * @returns 竞品列表\n   */\n  async findByParkType(parkType: string): Promise<Competitor[]> {\n    try {\n      console.log(`🔍 查询园区类型\"${parkType}\"的竞品列表`);\n\n      const query = `\n        SELECT * FROM Competitors \n        WHERE park_type = ? \n        ORDER BY competitor_name ASC\n      `;\n      const results = await executeQuery<Competitor>(query, [parkType]);\n\n      console.log(`✅ 成功查询到${results.length}个竞品`);\n      return results;\n    } catch (error) {\n      console.error('❌ 根据园区类型查询竞品失败:', error);\n      throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取所有城市列表\n   * @returns 城市列表\n   */\n  async getAllCities(): Promise<string[]> {\n    try {\n      console.log('🔍 获取所有城市列表');\n\n      const query = `\n        SELECT DISTINCT city \n        FROM Competitors \n        WHERE city IS NOT NULL AND city != '' \n        ORDER BY city ASC\n      `;\n      const results = await executeQuery<{ city: string }>(query);\n\n      const cities = results.map(row => row.city);\n      console.log(`✅ 成功获取${cities.length}个城市`);\n      return cities;\n    } catch (error) {\n      console.error('❌ 获取城市列表失败:', error);\n      throw new Error(`获取城市列表失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取所有园区类型列表\n   * @returns 园区类型列表\n   */\n  async getAllParkTypes(): Promise<string[]> {\n    try {\n      console.log('🔍 获取所有园区类型列表');\n\n      const query = `\n        SELECT DISTINCT park_type \n        FROM Competitors \n        WHERE park_type IS NOT NULL AND park_type != '' \n        ORDER BY park_type ASC\n      `;\n      const results = await executeQuery<{ park_type: string }>(query);\n\n      const parkTypes = results.map(row => row.park_type);\n      console.log(`✅ 成功获取${parkTypes.length}个园区类型`);\n      return parkTypes;\n    } catch (error) {\n      console.error('❌ 获取园区类型列表失败:', error);\n      throw new Error(`获取园区类型列表失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 更新竞品活跃状态\n   * @param competitorId 竞品ID\n   * @param isActive 是否活跃\n   * @returns 是否更新成功\n   */\n  async updateActiveStatus(competitorId: number, isActive: boolean): Promise<boolean> {\n    try {\n      console.log(`🔄 更新竞品${competitorId}的活跃状态为${isActive ? '活跃' : '非活跃'}`);\n\n      const query = 'UPDATE Competitors SET is_active = ? WHERE competitor_id = ?';\n      const results = await executeQuery(query, [isActive, competitorId]);\n      const affectedRows = (results as any).affectedRows;\n\n      if (affectedRows === 0) {\n        console.log(`⚠️ 未找到ID为${competitorId}的竞品`);\n        return false;\n      }\n\n      console.log('✅ 成功更新竞品活跃状态');\n      return true;\n    } catch (error) {\n      console.error('❌ 更新竞品活跃状态失败:', error);\n      throw new Error(`更新活跃状态失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 批量更新竞品活跃状态\n   * @param competitorIds 竞品ID列表\n   * @param isActive 是否活跃\n   * @returns 更新的记录数量\n   */\n  async batchUpdateActiveStatus(competitorIds: number[], isActive: boolean): Promise<number> {\n    try {\n      if (competitorIds.length === 0) {\n        console.log('⚠️ 没有需要更新的竞品');\n        return 0;\n      }\n\n      console.log(`🔄 批量更新${competitorIds.length}个竞品的活跃状态`);\n\n      const placeholders = competitorIds.map(() => '?').join(', ');\n      const query = `\n        UPDATE Competitors \n        SET is_active = ? \n        WHERE competitor_id IN (${placeholders})\n      `;\n\n      const results = await executeQuery(query, [isActive, ...competitorIds]);\n      const affectedRows = (results as any).affectedRows;\n\n      console.log(`✅ 成功更新${affectedRows}个竞品的活跃状态`);\n      return affectedRows;\n    } catch (error) {\n      console.error('❌ 批量更新竞品活跃状态失败:', error);\n      throw new Error(`批量更新失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 检查竞品名称是否已存在\n   * @param competitorName 竞品名称\n   * @param excludeId 排除的竞品ID（用于更新时检查）\n   * @returns 是否已存在\n   */\n  async isNameExists(competitorName: string, excludeId?: number): Promise<boolean> {\n    try {\n      let query = 'SELECT 1 FROM Competitors WHERE competitor_name = ?';\n      const params: any[] = [competitorName];\n\n      if (excludeId) {\n        query += ' AND competitor_id != ?';\n        params.push(excludeId);\n      }\n\n      const results = await executeQuery(query, params);\n      return results.length > 0;\n    } catch (error) {\n      console.error('❌ 检查竞品名称是否存在失败:', error);\n      throw new Error(`检查名称失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取竞品统计信息\n   * @returns 统计信息\n   */\n  async getStatistics(): Promise<{\n    total: number;\n    active: number;\n    inactive: number;\n    cityCount: number;\n    parkTypeCount: number;\n  }> {\n    try {\n      console.log('📊 获取竞品统计信息');\n\n      const queries = [\n        'SELECT COUNT(*) as total FROM Competitors',\n        'SELECT COUNT(*) as active FROM Competitors WHERE is_active = true',\n        'SELECT COUNT(*) as inactive FROM Competitors WHERE is_active = false',\n        'SELECT COUNT(DISTINCT city) as cityCount FROM Competitors WHERE city IS NOT NULL AND city != \"\"',\n        'SELECT COUNT(DISTINCT park_type) as parkTypeCount FROM Competitors WHERE park_type IS NOT NULL AND park_type != \"\"'\n      ];\n\n      const [totalResult, activeResult, inactiveResult, cityResult, parkTypeResult] = await Promise.all(\n        queries.map(query => executeQuery<{ [key: string]: number }>(query))\n      );\n\n      const statistics = {\n        total: totalResult[0].total,\n        active: activeResult[0].active,\n        inactive: inactiveResult[0].inactive,\n        cityCount: cityResult[0].cityCount,\n        parkTypeCount: parkTypeResult[0].parkTypeCount\n      };\n\n      console.log('✅ 成功获取竞品统计信息:', statistics);\n      return statistics;\n    } catch (error) {\n      console.error('❌ 获取竞品统计信息失败:', error);\n      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n}\n\n// 导出单例实例\nexport const competitorDAO = new CompetitorDAO();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAED;AACA;;;AAcO,MAAM,sBAAsB,kIAAA,CAAA,UAAO;IACxC,aAAc;QACZ,KAAK,CAAC,eAAe;IACvB;IAEA;;;;;GAKC,GACD,MAAM,gBACJ,MAAwB,EACxB,UAA6B,CAAC,CAAC,EACO;QACtC,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,YAAY;YAExB,MAAM,kBAA4B,EAAE;YACpC,MAAM,cAAqB,EAAE;YAE7B,UAAU;YACV,IAAI,QAAQ,eAAe,EAAE;gBAC3B,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,eAAe,CAAC,CAAC,CAAC;YACjD;YAEA,QAAQ;YACR,IAAI,QAAQ,IAAI,EAAE;gBAChB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,IAAI;YAC/B;YAEA,UAAU;YACV,IAAI,QAAQ,SAAS,EAAE;gBACrB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,SAAS;YACpC;YAEA,UAAU;YACV,IAAI,QAAQ,SAAS,KAAK,WAAW;gBACnC,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,SAAS;YACpC;YAEA,MAAM,cAAc,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,IAAI,CAAC,WAAW;YAEjF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,aAAa;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,WAAW,cAAsB,EAA8B;QACnE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,eAAe,IAAI,CAAC;YAE9C,MAAM,QAAQ;YACd,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAc,OAAO;gBAAC;aAAe;YAEtE,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,eAAe,IAAI,CAAC;gBAC7C,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,OAAO,CAAC,EAAE;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;GAGC,GACD,MAAM,wBAA+C;QACnD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,QAAQ,CAAC;;;;MAIf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAc;YAE/C,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,KAAK,CAAC;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;GAIC,GACD,MAAM,WAAW,IAAY,EAAyB;QACpD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,MAAM,CAAC;YAEnC,MAAM,QAAQ,CAAC;;;;MAIf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAc,OAAO;gBAAC;aAAK;YAE5D,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,GAAG,CAAC;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,eAAe,QAAgB,EAAyB;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC;YAEzC,MAAM,QAAQ,CAAC;;;;MAIf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAc,OAAO;gBAAC;aAAS;YAEhE,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,GAAG,CAAC;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;GAGC,GACD,MAAM,eAAkC;QACtC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,QAAQ,CAAC;;;;;MAKf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAoB;YAErD,MAAM,SAAS,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;YAC1C,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;GAGC,GACD,MAAM,kBAAqC;QACzC,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,QAAQ,CAAC;;;;;MAKf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAyB;YAE1D,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,SAAS;YAClD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,UAAU,MAAM,CAAC,KAAK,CAAC;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClF;IACF;IAEA;;;;;GAKC,GACD,MAAM,mBAAmB,YAAoB,EAAE,QAAiB,EAAoB;QAClF,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,aAAa,MAAM,EAAE,WAAW,OAAO,OAAO;YAEpE,MAAM,QAAQ;YACd,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAAC;gBAAU;aAAa;YAClE,MAAM,eAAe,AAAC,QAAgB,YAAY;YAElD,IAAI,iBAAiB,GAAG;gBACtB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa,GAAG,CAAC;gBACzC,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;;GAKC,GACD,MAAM,wBAAwB,aAAuB,EAAE,QAAiB,EAAmB;QACzF,IAAI;YACF,IAAI,cAAc,MAAM,KAAK,GAAG;gBAC9B,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,cAAc,MAAM,CAAC,QAAQ,CAAC;YAEpD,MAAM,eAAe,cAAc,GAAG,CAAC,IAAM,KAAK,IAAI,CAAC;YACvD,MAAM,QAAQ,CAAC;;;gCAGW,EAAE,aAAa;MACzC,CAAC;YAED,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;gBAAC;mBAAa;aAAc;YACtE,MAAM,eAAe,AAAC,QAAgB,YAAY;YAElD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,QAAQ,CAAC;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;;GAKC,GACD,MAAM,aAAa,cAAsB,EAAE,SAAkB,EAAoB;QAC/E,IAAI;YACF,IAAI,QAAQ;YACZ,MAAM,SAAgB;gBAAC;aAAe;YAEtC,IAAI,WAAW;gBACb,SAAS;gBACT,OAAO,IAAI,CAAC;YACd;YAEA,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YAC1C,OAAO,QAAQ,MAAM,GAAG;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;GAGC,GACD,MAAM,gBAMH;QACD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,UAAU;gBACd;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,CAAC,aAAa,cAAc,gBAAgB,YAAY,eAAe,GAAG,MAAM,QAAQ,GAAG,CAC/F,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAA6B;YAG/D,MAAM,aAAa;gBACjB,OAAO,WAAW,CAAC,EAAE,CAAC,KAAK;gBAC3B,QAAQ,YAAY,CAAC,EAAE,CAAC,MAAM;gBAC9B,UAAU,cAAc,CAAC,EAAE,CAAC,QAAQ;gBACpC,WAAW,UAAU,CAAC,EAAE,CAAC,SAAS;gBAClC,eAAe,cAAc,CAAC,EAAE,CAAC,aAAa;YAChD;YAEA,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;AACF;AAGO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/dao/ticket-type-dao.ts"], "sourcesContent": ["/**\n * 票种数据访问对象(DAO)类\n * \n * 功能说明：\n * 1. 实现票种表的CRUD操作\n * 2. 提供票种特有的查询方法\n * 3. 支持按分类筛选票种\n * 4. 提供票种统计和分析功能\n */\n\nimport { BaseDAO } from './base-dao';\nimport { executeQuery } from '../database';\nimport { \n  TicketType, \n  CreateTicketTypeInput, \n  UpdateTicketTypeInput,\n  TicketTypeFilters,\n  PaginationParams,\n  PaginatedResult\n} from '@/types';\n\n/**\n * 票种DAO类\n * 继承基础DAO类，实现票种特有的数据操作\n */\nexport class TicketTypeDAO extends BaseDAO<TicketType, CreateTicketTypeInput, UpdateTicketTypeInput> {\n  constructor() {\n    super('TicketTypes', 'ticket_type_id');\n  }\n\n  /**\n   * 根据筛选条件查询票种列表（分页）\n   * @param params 分页参数\n   * @param filters 筛选条件\n   * @returns 分页查询结果\n   */\n  async findWithFilters(\n    params: PaginationParams,\n    filters: TicketTypeFilters = {}\n  ): Promise<PaginatedResult<TicketType>> {\n    try {\n      console.log('🔍 根据筛选条件查询票种列表');\n      console.log('📝 筛选条件:', filters);\n\n      const whereConditions: string[] = [];\n      const whereParams: any[] = [];\n\n      // 按票种名称搜索\n      if (filters.ticket_type_name) {\n        whereConditions.push('ticket_type_name LIKE ?');\n        whereParams.push(`%${filters.ticket_type_name}%`);\n      }\n\n      // 按分类筛选\n      if (filters.category) {\n        whereConditions.push('category = ?');\n        whereParams.push(filters.category);\n      }\n\n      const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';\n\n      return await this.findWithPagination(params, whereClause, whereParams);\n    } catch (error) {\n      console.error('❌ 根据筛选条件查询票种失败:', error);\n      throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据票种名称查询（精确匹配）\n   * @param ticketTypeName 票种名称\n   * @returns 票种信息\n   */\n  async findByName(ticketTypeName: string): Promise<TicketType | null> {\n    try {\n      console.log(`🔍 查询票种名称为\"${ticketTypeName}\"的记录`);\n\n      const query = 'SELECT * FROM TicketTypes WHERE ticket_type_name = ?';\n      const results = await executeQuery<TicketType>(query, [ticketTypeName]);\n\n      if (results.length === 0) {\n        console.log(`⚠️ 未找到名称为\"${ticketTypeName}\"的票种`);\n        return null;\n      }\n\n      console.log('✅ 成功查询到票种信息');\n      return results[0];\n    } catch (error) {\n      console.error('❌ 根据名称查询票种失败:', error);\n      throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据分类获取票种列表\n   * @param category 票种分类\n   * @returns 票种列表\n   */\n  async findByCategory(category: string): Promise<TicketType[]> {\n    try {\n      console.log(`🔍 查询分类\"${category}\"的票种列表`);\n\n      const query = `\n        SELECT * FROM TicketTypes \n        WHERE category = ? \n        ORDER BY ticket_type_name ASC\n      `;\n      const results = await executeQuery<TicketType>(query, [category]);\n\n      console.log(`✅ 成功查询到${results.length}个票种`);\n      return results;\n    } catch (error) {\n      console.error('❌ 根据分类查询票种失败:', error);\n      throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取所有票种分类列表\n   * @returns 分类列表\n   */\n  async getAllCategories(): Promise<string[]> {\n    try {\n      console.log('🔍 获取所有票种分类列表');\n\n      const query = `\n        SELECT DISTINCT category \n        FROM TicketTypes \n        WHERE category IS NOT NULL AND category != '' \n        ORDER BY category ASC\n      `;\n      const results = await executeQuery<{ category: string }>(query);\n\n      const categories = results.map(row => row.category);\n      console.log(`✅ 成功获取${categories.length}个分类`);\n      return categories;\n    } catch (error) {\n      console.error('❌ 获取分类列表失败:', error);\n      throw new Error(`获取分类列表失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 搜索票种（模糊匹配票种名称）\n   * @param keyword 搜索关键词\n   * @param limit 返回结果数量限制\n   * @returns 票种列表\n   */\n  async searchByKeyword(keyword: string, limit: number = 10): Promise<TicketType[]> {\n    try {\n      console.log(`🔍 搜索票种，关键词: \"${keyword}\"`);\n\n      const query = `\n        SELECT * FROM TicketTypes \n        WHERE ticket_type_name LIKE ? OR category LIKE ?\n        ORDER BY \n          CASE \n            WHEN ticket_type_name = ? THEN 1\n            WHEN ticket_type_name LIKE ? THEN 2\n            ELSE 3\n          END,\n          ticket_type_name ASC\n        LIMIT ?\n      `;\n\n      const searchPattern = `%${keyword}%`;\n      const results = await executeQuery<TicketType>(query, [\n        searchPattern,\n        searchPattern,\n        keyword,\n        `${keyword}%`,\n        limit\n      ]);\n\n      console.log(`✅ 搜索到${results.length}个票种`);\n      return results;\n    } catch (error) {\n      console.error('❌ 搜索票种失败:', error);\n      throw new Error(`搜索票种失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 检查票种名称是否已存在\n   * @param ticketTypeName 票种名称\n   * @param excludeId 排除的票种ID（用于更新时检查）\n   * @returns 是否已存在\n   */\n  async isNameExists(ticketTypeName: string, excludeId?: number): Promise<boolean> {\n    try {\n      let query = 'SELECT 1 FROM TicketTypes WHERE ticket_type_name = ?';\n      const params: any[] = [ticketTypeName];\n\n      if (excludeId) {\n        query += ' AND ticket_type_id != ?';\n        params.push(excludeId);\n      }\n\n      const results = await executeQuery(query, params);\n      return results.length > 0;\n    } catch (error) {\n      console.error('❌ 检查票种名称是否存在失败:', error);\n      throw new Error(`检查名称失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取票种使用统计（关联促销活动表）\n   * @param ticketTypeId 票种ID\n   * @returns 使用统计信息\n   */\n  async getUsageStatistics(ticketTypeId: number): Promise<{\n    promotionCount: number;\n    avgPrice: number;\n    minPrice: number;\n    maxPrice: number;\n    latestPromotion?: Date;\n  }> {\n    try {\n      console.log(`📊 获取票种${ticketTypeId}的使用统计`);\n\n      const query = `\n        SELECT \n          COUNT(*) as promotionCount,\n          AVG(promo_price) as avgPrice,\n          MIN(promo_price) as minPrice,\n          MAX(promo_price) as maxPrice,\n          MAX(entry_date) as latestPromotion\n        FROM Promotions \n        WHERE ticket_type_id = ? AND promo_price IS NOT NULL\n      `;\n\n      const results = await executeQuery<{\n        promotionCount: number;\n        avgPrice: number;\n        minPrice: number;\n        maxPrice: number;\n        latestPromotion: Date;\n      }>(query, [ticketTypeId]);\n\n      const statistics = results[0] || {\n        promotionCount: 0,\n        avgPrice: 0,\n        minPrice: 0,\n        maxPrice: 0,\n        latestPromotion: undefined\n      };\n\n      console.log('✅ 成功获取票种使用统计:', statistics);\n      return statistics;\n    } catch (error) {\n      console.error('❌ 获取票种使用统计失败:', error);\n      throw new Error(`获取使用统计失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取热门票种排行榜\n   * @param limit 返回数量限制\n   * @returns 热门票种列表\n   */\n  async getPopularTicketTypes(limit: number = 10): Promise<Array<{\n    ticket_type_id: number;\n    ticket_type_name: string;\n    category: string;\n    promotion_count: number;\n    avg_price: number;\n  }>> {\n    try {\n      console.log(`📊 获取热门票种排行榜，前${limit}名`);\n\n      const query = `\n        SELECT \n          tt.ticket_type_id,\n          tt.ticket_type_name,\n          tt.category,\n          COUNT(p.promotion_id) as promotion_count,\n          AVG(p.promo_price) as avg_price\n        FROM TicketTypes tt\n        LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id\n        GROUP BY tt.ticket_type_id, tt.ticket_type_name, tt.category\n        ORDER BY promotion_count DESC, avg_price DESC\n        LIMIT ?\n      `;\n\n      const results = await executeQuery<{\n        ticket_type_id: number;\n        ticket_type_name: string;\n        category: string;\n        promotion_count: number;\n        avg_price: number;\n      }>(query, [limit]);\n\n      console.log(`✅ 成功获取${results.length}个热门票种`);\n      return results;\n    } catch (error) {\n      console.error('❌ 获取热门票种排行榜失败:', error);\n      throw new Error(`获取排行榜失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取票种统计信息\n   * @returns 统计信息\n   */\n  async getStatistics(): Promise<{\n    total: number;\n    categoryCount: number;\n    withPromotions: number;\n    withoutPromotions: number;\n  }> {\n    try {\n      console.log('📊 获取票种统计信息');\n\n      const queries = [\n        'SELECT COUNT(*) as total FROM TicketTypes',\n        'SELECT COUNT(DISTINCT category) as categoryCount FROM TicketTypes WHERE category IS NOT NULL AND category != \"\"',\n        'SELECT COUNT(DISTINCT tt.ticket_type_id) as withPromotions FROM TicketTypes tt INNER JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id',\n        'SELECT COUNT(*) as withoutPromotions FROM TicketTypes tt LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id WHERE p.ticket_type_id IS NULL'\n      ];\n\n      const [totalResult, categoryResult, withPromotionsResult, withoutPromotionsResult] = await Promise.all(\n        queries.map(query => executeQuery<{ [key: string]: number }>(query))\n      );\n\n      const statistics = {\n        total: totalResult[0].total,\n        categoryCount: categoryResult[0].categoryCount,\n        withPromotions: withPromotionsResult[0].withPromotions,\n        withoutPromotions: withoutPromotionsResult[0].withoutPromotions\n      };\n\n      console.log('✅ 成功获取票种统计信息:', statistics);\n      return statistics;\n    } catch (error) {\n      console.error('❌ 获取票种统计信息失败:', error);\n      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取分类统计信息\n   * @returns 分类统计列表\n   */\n  async getCategoryStatistics(): Promise<Array<{\n    category: string;\n    ticket_count: number;\n    promotion_count: number;\n    avg_price: number;\n  }>> {\n    try {\n      console.log('📊 获取分类统计信息');\n\n      const query = `\n        SELECT \n          COALESCE(tt.category, '未分类') as category,\n          COUNT(DISTINCT tt.ticket_type_id) as ticket_count,\n          COUNT(p.promotion_id) as promotion_count,\n          AVG(p.promo_price) as avg_price\n        FROM TicketTypes tt\n        LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id\n        GROUP BY tt.category\n        ORDER BY ticket_count DESC, promotion_count DESC\n      `;\n\n      const results = await executeQuery<{\n        category: string;\n        ticket_count: number;\n        promotion_count: number;\n        avg_price: number;\n      }>(query);\n\n      console.log(`✅ 成功获取${results.length}个分类的统计信息`);\n      return results;\n    } catch (error) {\n      console.error('❌ 获取分类统计信息失败:', error);\n      throw new Error(`获取分类统计失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n}\n\n// 导出单例实例\nexport const ticketTypeDAO = new TicketTypeDAO();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAED;AACA;;;AAcO,MAAM,sBAAsB,kIAAA,CAAA,UAAO;IACxC,aAAc;QACZ,KAAK,CAAC,eAAe;IACvB;IAEA;;;;;GAKC,GACD,MAAM,gBACJ,MAAwB,EACxB,UAA6B,CAAC,CAAC,EACO;QACtC,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,YAAY;YAExB,MAAM,kBAA4B,EAAE;YACpC,MAAM,cAAqB,EAAE;YAE7B,UAAU;YACV,IAAI,QAAQ,gBAAgB,EAAE;gBAC5B,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,gBAAgB,CAAC,CAAC,CAAC;YAClD;YAEA,QAAQ;YACR,IAAI,QAAQ,QAAQ,EAAE;gBACpB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,QAAQ;YACnC;YAEA,MAAM,cAAc,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,IAAI,CAAC,WAAW;YAEjF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,aAAa;QAC5D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,WAAW,cAAsB,EAA8B;QACnE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,eAAe,IAAI,CAAC;YAE9C,MAAM,QAAQ;YACd,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAc,OAAO;gBAAC;aAAe;YAEtE,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,eAAe,IAAI,CAAC;gBAC7C,OAAO;YACT;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO,OAAO,CAAC,EAAE;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,eAAe,QAAgB,EAAyB;QAC5D,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC;YAEvC,MAAM,QAAQ,CAAC;;;;MAIf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAc,OAAO;gBAAC;aAAS;YAEhE,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,GAAG,CAAC;YACzC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;GAGC,GACD,MAAM,mBAAsC;QAC1C,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,QAAQ,CAAC;;;;;MAKf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAwB;YAEzD,MAAM,aAAa,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,QAAQ;YAClD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,MAAM,CAAC,GAAG,CAAC;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;;GAKC,GACD,MAAM,gBAAgB,OAAe,EAAE,QAAgB,EAAE,EAAyB;QAChF,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAEvC,MAAM,QAAQ,CAAC;;;;;;;;;;;MAWf,CAAC;YAED,MAAM,gBAAgB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;YACpC,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAc,OAAO;gBACpD;gBACA;gBACA;gBACA,GAAG,QAAQ,CAAC,CAAC;gBACb;aACD;YAED,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,MAAM,CAAC,GAAG,CAAC;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;;GAKC,GACD,MAAM,aAAa,cAAsB,EAAE,SAAkB,EAAoB;QAC/E,IAAI;YACF,IAAI,QAAQ;YACZ,MAAM,SAAgB;gBAAC;aAAe;YAEtC,IAAI,WAAW;gBACb,SAAS;gBACT,OAAO,IAAI,CAAC;YACd;YAEA,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE,OAAO;YAC1C,OAAO,QAAQ,MAAM,GAAG;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,mBAAmB,YAAoB,EAM1C;QACD,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,aAAa,KAAK,CAAC;YAEzC,MAAM,QAAQ,CAAC;;;;;;;;;MASf,CAAC;YAED,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAM9B,OAAO;gBAAC;aAAa;YAExB,MAAM,aAAa,OAAO,CAAC,EAAE,IAAI;gBAC/B,gBAAgB;gBAChB,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,iBAAiB;YACnB;YAEA,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;GAIC,GACD,MAAM,sBAAsB,QAAgB,EAAE,EAM1C;QACF,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAErC,MAAM,QAAQ,CAAC;;;;;;;;;;;;MAYf,CAAC;YAED,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAM9B,OAAO;gBAAC;aAAM;YAEjB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,MAAM,CAAC,KAAK,CAAC;YAC1C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC/E;IACF;IAEA;;;GAGC,GACD,MAAM,gBAKH;QACD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,UAAU;gBACd;gBACA;gBACA;gBACA;aACD;YAED,MAAM,CAAC,aAAa,gBAAgB,sBAAsB,wBAAwB,GAAG,MAAM,QAAQ,GAAG,CACpG,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAA6B;YAG/D,MAAM,aAAa;gBACjB,OAAO,WAAW,CAAC,EAAE,CAAC,KAAK;gBAC3B,eAAe,cAAc,CAAC,EAAE,CAAC,aAAa;gBAC9C,gBAAgB,oBAAoB,CAAC,EAAE,CAAC,cAAc;gBACtD,mBAAmB,uBAAuB,CAAC,EAAE,CAAC,iBAAiB;YACjE;YAEA,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;GAGC,GACD,MAAM,wBAKF;QACF,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,QAAQ,CAAC;;;;;;;;;;MAUf,CAAC;YAED,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAK9B;YAEH,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,MAAM,CAAC,QAAQ,CAAC;YAC7C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;AACF;AAGO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 1145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/dao/promotion-dao.ts"], "sourcesContent": ["/**\n * 促销活动数据访问对象(DAO)类\n * \n * 功能说明：\n * 1. 实现促销活动表的CRUD操作\n * 2. 提供复杂的关联查询（竞品、票种）\n * 3. 支持多维度筛选和搜索\n * 4. 提供促销活动统计和分析功能\n */\n\nimport { BaseDAO } from './base-dao';\nimport { executeQuery } from '../database';\nimport { \n  Promotion, \n  PromotionDetail,\n  CreatePromotionInput, \n  UpdatePromotionInput,\n  PromotionFilters,\n  PaginationParams,\n  PaginatedResult\n} from '@/types';\n\n/**\n * 促销活动DAO类\n * 继承基础DAO类，实现促销活动特有的数据操作\n */\nexport class PromotionDAO extends BaseDAO<Promotion, CreatePromotionInput, UpdatePromotionInput> {\n  constructor() {\n    super('Promotions', 'promotion_id');\n  }\n\n  /**\n   * 根据筛选条件查询促销活动列表（分页，包含关联数据）\n   * @param params 分页参数\n   * @param filters 筛选条件\n   * @returns 分页查询结果\n   */\n  async findWithFilters(\n    params: PaginationParams,\n    filters: PromotionFilters = {}\n  ): Promise<PaginatedResult<PromotionDetail>> {\n    try {\n      console.log('🔍 根据筛选条件查询促销活动列表');\n      console.log('📝 筛选条件:', filters);\n\n      const whereConditions: string[] = [];\n      const whereParams: any[] = [];\n\n      // 按竞品筛选\n      if (filters.competitor_id) {\n        whereConditions.push('p.competitor_id = ?');\n        whereParams.push(filters.competitor_id);\n      }\n\n      // 按票种筛选\n      if (filters.ticket_type_id) {\n        whereConditions.push('p.ticket_type_id = ?');\n        whereParams.push(filters.ticket_type_id);\n      }\n\n      // 按活动名称搜索\n      if (filters.activity_name) {\n        whereConditions.push('p.activity_name LIKE ?');\n        whereParams.push(`%${filters.activity_name}%`);\n      }\n\n      // 按销售渠道筛选\n      if (filters.sales_channel) {\n        whereConditions.push('p.sales_channel LIKE ?');\n        whereParams.push(`%${filters.sales_channel}%`);\n      }\n\n      // 按销售日期范围筛选\n      if (filters.sale_date_start) {\n        whereConditions.push('p.sale_start_date >= ?');\n        whereParams.push(filters.sale_date_start);\n      }\n      if (filters.sale_date_end) {\n        whereConditions.push('p.sale_end_date <= ?');\n        whereParams.push(filters.sale_date_end);\n      }\n\n      // 按使用日期范围筛选\n      if (filters.use_date_start) {\n        whereConditions.push('p.use_start_date >= ?');\n        whereParams.push(filters.use_date_start);\n      }\n      if (filters.use_date_end) {\n        whereConditions.push('p.use_end_date <= ?');\n        whereParams.push(filters.use_date_end);\n      }\n\n      // 按价格范围筛选\n      if (filters.min_price) {\n        whereConditions.push('p.promo_price >= ?');\n        whereParams.push(filters.min_price);\n      }\n      if (filters.max_price) {\n        whereConditions.push('p.promo_price <= ?');\n        whereParams.push(filters.max_price);\n      }\n\n      const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';\n\n      return await this.findWithPaginationAndJoins(params, whereClause, whereParams);\n    } catch (error) {\n      console.error('❌ 根据筛选条件查询促销活动失败:', error);\n      throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 分页查询促销活动（包含关联的竞品和票种信息）\n   * @param params 分页参数\n   * @param whereClause WHERE子句\n   * @param whereParams WHERE参数\n   * @returns 分页查询结果\n   */\n  private async findWithPaginationAndJoins(\n    params: PaginationParams,\n    whereClause: string = '',\n    whereParams: any[] = []\n  ): Promise<PaginatedResult<PromotionDetail>> {\n    try {\n      const { page, pageSize, sortBy = 'p.promotion_id', sortOrder = 'DESC' } = params;\n      const offset = (page - 1) * pageSize;\n\n      // 构建WHERE子句\n      const whereSQL = whereClause ? `WHERE ${whereClause}` : '';\n\n      // 查询总数\n      const countQuery = `\n        SELECT COUNT(*) as total \n        FROM Promotions p\n        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id\n        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id\n        ${whereSQL}\n      `;\n      const countResults = await executeQuery<{ total: number }>(countQuery, whereParams);\n      const total = countResults[0].total;\n\n      // 查询数据（包含关联信息）\n      const dataQuery = `\n        SELECT\n          p.*,\n          c.competitor_name,\n          c.city,\n          c.park_type,\n          c.is_active as competitor_is_active,\n          tt.ticket_type_name,\n          tt.category as ticket_category,\n          CASE\n            WHEN p.rack_rate > 0 AND p.promo_price > 0\n            THEN ROUND((p.rack_rate - p.promo_price) / p.rack_rate, 4)\n            ELSE NULL\n          END as discount_rate\n        FROM Promotions p\n        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id\n        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id\n        ${whereSQL}\n        ORDER BY ${sortBy} ${sortOrder}\n        LIMIT ${pageSize} OFFSET ${offset}\n      `;\n\n      const dataResults = await executeQuery<any>(dataQuery, whereParams);\n\n      // 转换数据格式\n      const promotions: PromotionDetail[] = dataResults.map(row => ({\n        promotion_id: row.promotion_id,\n        competitor_id: row.competitor_id,\n        ticket_type_id: row.ticket_type_id,\n        activity_name: row.activity_name,\n        rack_rate: row.rack_rate,\n        promo_price: row.promo_price,\n        sale_start_date: row.sale_start_date,\n        sale_end_date: row.sale_end_date,\n        use_start_date: row.use_start_date,\n        use_end_date: row.use_end_date,\n        sales_channel: row.sales_channel,\n        usage_rules: row.usage_rules,\n        data_source_url: row.data_source_url,\n        entry_date: row.entry_date,\n        remarks: row.remarks,\n        competitor: row.competitor_name ? {\n          competitor_id: row.competitor_id,\n          competitor_name: row.competitor_name,\n          city: row.city,\n          park_type: row.park_type,\n          is_active: row.competitor_is_active\n        } : undefined,\n        ticket_type: row.ticket_type_name ? {\n          ticket_type_id: row.ticket_type_id,\n          ticket_type_name: row.ticket_type_name,\n          category: row.ticket_category\n        } : undefined,\n        discount_rate: row.discount_rate\n      }));\n\n      const totalPages = Math.ceil(total / pageSize);\n\n      console.log(`✅ 分页查询成功 - 总计${total}条记录，当前页${promotions.length}条`);\n\n      return {\n        data: promotions,\n        total,\n        page,\n        pageSize,\n        totalPages\n      };\n    } catch (error) {\n      console.error('❌ 分页查询促销活动失败:', error);\n      throw new Error(`分页查询失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据ID查询促销活动详情（包含关联数据）\n   * @param promotionId 促销活动ID\n   * @returns 促销活动详情\n   */\n  async findDetailById(promotionId: number): Promise<PromotionDetail | null> {\n    try {\n      console.log(`🔍 查询促销活动详情，ID: ${promotionId}`);\n\n      const query = `\n        SELECT \n          p.*,\n          c.competitor_name,\n          c.city,\n          c.park_type,\n          c.is_active as competitor_is_active,\n          tt.ticket_type_name,\n          tt.category as ticket_category,\n          CASE \n            WHEN p.rack_rate > 0 AND p.promo_price > 0 \n            THEN ROUND((p.rack_rate - p.promo_price) / p.rack_rate, 4)\n            ELSE NULL \n          END as discount_rate\n        FROM Promotions p\n        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id\n        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id\n        WHERE p.promotion_id = ?\n      `;\n\n      const results = await executeQuery<any>(query, [promotionId]);\n\n      if (results.length === 0) {\n        console.log(`⚠️ 未找到ID为${promotionId}的促销活动`);\n        return null;\n      }\n\n      const row = results[0];\n      const promotion: PromotionDetail = {\n        promotion_id: row.promotion_id,\n        competitor_id: row.competitor_id,\n        ticket_type_id: row.ticket_type_id,\n        activity_name: row.activity_name,\n        rack_rate: row.rack_rate,\n        promo_price: row.promo_price,\n        sale_start_date: row.sale_start_date,\n        sale_end_date: row.sale_end_date,\n        use_start_date: row.use_start_date,\n        use_end_date: row.use_end_date,\n        sales_channel: row.sales_channel,\n        usage_rules: row.usage_rules,\n        data_source_url: row.data_source_url,\n        entry_date: row.entry_date,\n        remarks: row.remarks,\n        competitor: row.competitor_name ? {\n          competitor_id: row.competitor_id,\n          competitor_name: row.competitor_name,\n          city: row.city,\n          park_type: row.park_type,\n          is_active: row.competitor_is_active\n        } : undefined,\n        ticket_type: row.ticket_type_name ? {\n          ticket_type_id: row.ticket_type_id,\n          ticket_type_name: row.ticket_type_name,\n          category: row.ticket_category\n        } : undefined,\n        discount_rate: row.discount_rate\n      };\n\n      console.log('✅ 成功查询到促销活动详情');\n      return promotion;\n    } catch (error) {\n      console.error('❌ 查询促销活动详情失败:', error);\n      throw new Error(`查询详情失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据竞品ID查询促销活动列表\n   * @param competitorId 竞品ID\n   * @param limit 返回数量限制\n   * @returns 促销活动列表\n   */\n  async findByCompetitorId(competitorId: number, limit: number = 50): Promise<PromotionDetail[]> {\n    try {\n      console.log(`🔍 查询竞品${competitorId}的促销活动列表`);\n\n      const params: PaginationParams = {\n        page: 1,\n        pageSize: limit,\n        sortBy: 'p.entry_date',\n        sortOrder: 'DESC'\n      };\n\n      const result = await this.findWithPaginationAndJoins(\n        params,\n        'p.competitor_id = ?',\n        [competitorId]\n      );\n\n      console.log(`✅ 成功查询到${result.data.length}个促销活动`);\n      return result.data;\n    } catch (error) {\n      console.error('❌ 根据竞品ID查询促销活动失败:', error);\n      throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 根据票种ID查询促销活动列表\n   * @param ticketTypeId 票种ID\n   * @param limit 返回数量限制\n   * @returns 促销活动列表\n   */\n  async findByTicketTypeId(ticketTypeId: number, limit: number = 50): Promise<PromotionDetail[]> {\n    try {\n      console.log(`🔍 查询票种${ticketTypeId}的促销活动列表`);\n\n      const params: PaginationParams = {\n        page: 1,\n        pageSize: limit,\n        sortBy: 'p.entry_date',\n        sortOrder: 'DESC'\n      };\n\n      const result = await this.findWithPaginationAndJoins(\n        params,\n        'p.ticket_type_id = ?',\n        [ticketTypeId]\n      );\n\n      console.log(`✅ 成功查询到${result.data.length}个促销活动`);\n      return result.data;\n    } catch (error) {\n      console.error('❌ 根据票种ID查询促销活动失败:', error);\n      throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取当前有效的促销活动（销售期内）\n   * @param limit 返回数量限制\n   * @returns 有效促销活动列表\n   */\n  async findCurrentActivePromotions(limit: number = 50): Promise<PromotionDetail[]> {\n    try {\n      console.log('🔍 查询当前有效的促销活动');\n\n      const currentDate = new Date().toISOString().split('T')[0];\n      const params: PaginationParams = {\n        page: 1,\n        pageSize: limit,\n        sortBy: 'p.entry_date',\n        sortOrder: 'DESC'\n      };\n\n      const whereClause = `\n        (p.sale_start_date IS NULL OR p.sale_start_date <= ?) AND\n        (p.sale_end_date IS NULL OR p.sale_end_date >= ?)\n      `;\n\n      const result = await this.findWithPaginationAndJoins(\n        params,\n        whereClause,\n        [currentDate, currentDate]\n      );\n\n      console.log(`✅ 成功查询到${result.data.length}个当前有效的促销活动`);\n      return result.data;\n    } catch (error) {\n      console.error('❌ 查询当前有效促销活动失败:', error);\n      throw new Error(`查询有效促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取即将到期的促销活动\n   * @param days 提前天数\n   * @param limit 返回数量限制\n   * @returns 即将到期的促销活动列表\n   */\n  async findExpiringPromotions(days: number = 7, limit: number = 50): Promise<PromotionDetail[]> {\n    try {\n      console.log(`🔍 查询${days}天内即将到期的促销活动`);\n\n      const currentDate = new Date();\n      const futureDate = new Date(currentDate.getTime() + days * 24 * 60 * 60 * 1000);\n\n      const currentDateStr = currentDate.toISOString().split('T')[0];\n      const futureDateStr = futureDate.toISOString().split('T')[0];\n\n      const params: PaginationParams = {\n        page: 1,\n        pageSize: limit,\n        sortBy: 'p.sale_end_date',\n        sortOrder: 'ASC'\n      };\n\n      const whereClause = `\n        p.sale_end_date IS NOT NULL AND\n        p.sale_end_date >= ? AND\n        p.sale_end_date <= ?\n      `;\n\n      const result = await this.findWithPaginationAndJoins(\n        params,\n        whereClause,\n        [currentDateStr, futureDateStr]\n      );\n\n      console.log(`✅ 成功查询到${result.data.length}个即将到期的促销活动`);\n      return result.data;\n    } catch (error) {\n      console.error('❌ 查询即将到期促销活动失败:', error);\n      throw new Error(`查询即将到期促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取所有销售渠道列表\n   * @returns 销售渠道列表\n   */\n  async getAllSalesChannels(): Promise<string[]> {\n    try {\n      console.log('🔍 获取所有销售渠道列表');\n\n      const query = `\n        SELECT DISTINCT sales_channel\n        FROM Promotions\n        WHERE sales_channel IS NOT NULL AND sales_channel != ''\n        ORDER BY sales_channel ASC\n      `;\n      const results = await executeQuery<{ sales_channel: string }>(query);\n\n      const channels = results.map(row => row.sales_channel);\n      console.log(`✅ 成功获取${channels.length}个销售渠道`);\n      return channels;\n    } catch (error) {\n      console.error('❌ 获取销售渠道列表失败:', error);\n      throw new Error(`获取销售渠道列表失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取促销活动统计信息\n   * @returns 统计信息\n   */\n  async getStatistics(): Promise<{\n    total: number;\n    activePromotions: number;\n    expiredPromotions: number;\n    avgDiscountRate: number;\n    avgPrice: number;\n    totalCompetitors: number;\n    totalTicketTypes: number;\n  }> {\n    try {\n      console.log('📊 获取促销活动统计信息');\n\n      const currentDate = new Date().toISOString().split('T')[0];\n\n      const queries = [\n        'SELECT COUNT(*) as total FROM Promotions',\n        `SELECT COUNT(*) as activePromotions FROM Promotions WHERE (sale_start_date IS NULL OR sale_start_date <= '${currentDate}') AND (sale_end_date IS NULL OR sale_end_date >= '${currentDate}')`,\n        `SELECT COUNT(*) as expiredPromotions FROM Promotions WHERE sale_end_date IS NOT NULL AND sale_end_date < '${currentDate}'`,\n        'SELECT AVG(CASE WHEN rack_rate > 0 AND promo_price > 0 THEN (rack_rate - promo_price) / rack_rate ELSE NULL END) as avgDiscountRate FROM Promotions',\n        'SELECT AVG(promo_price) as avgPrice FROM Promotions WHERE promo_price IS NOT NULL',\n        'SELECT COUNT(DISTINCT competitor_id) as totalCompetitors FROM Promotions',\n        'SELECT COUNT(DISTINCT ticket_type_id) as totalTicketTypes FROM Promotions'\n      ];\n\n      const [\n        totalResult,\n        activeResult,\n        expiredResult,\n        discountResult,\n        priceResult,\n        competitorResult,\n        ticketTypeResult\n      ] = await Promise.all(\n        queries.map(query => executeQuery<{ [key: string]: number }>(query))\n      );\n\n      const statistics = {\n        total: totalResult[0].total,\n        activePromotions: activeResult[0].activePromotions,\n        expiredPromotions: expiredResult[0].expiredPromotions,\n        avgDiscountRate: discountResult[0].avgDiscountRate || 0,\n        avgPrice: priceResult[0].avgPrice || 0,\n        totalCompetitors: competitorResult[0].totalCompetitors,\n        totalTicketTypes: ticketTypeResult[0].totalTicketTypes\n      };\n\n      console.log('✅ 成功获取促销活动统计信息:', statistics);\n      return statistics;\n    } catch (error) {\n      console.error('❌ 获取促销活动统计信息失败:', error);\n      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 获取热门竞品排行榜（按促销活动数量）\n   * @param limit 返回数量限制\n   * @returns 热门竞品列表\n   */\n  async getTopCompetitorsByPromotions(limit: number = 10): Promise<Array<{\n    competitor_id: number;\n    competitor_name: string;\n    promotion_count: number;\n    avg_price: number;\n    avg_discount_rate: number;\n  }>> {\n    try {\n      console.log(`📊 获取热门竞品排行榜，前${limit}名`);\n\n      const query = `\n        SELECT\n          c.competitor_id,\n          c.competitor_name,\n          COUNT(p.promotion_id) as promotion_count,\n          AVG(p.promo_price) as avg_price,\n          AVG(CASE WHEN p.rack_rate > 0 AND p.promo_price > 0 THEN (p.rack_rate - p.promo_price) / p.rack_rate ELSE NULL END) as avg_discount_rate\n        FROM Competitors c\n        LEFT JOIN Promotions p ON c.competitor_id = p.competitor_id\n        GROUP BY c.competitor_id, c.competitor_name\n        ORDER BY promotion_count DESC, avg_price DESC\n        LIMIT ?\n      `;\n\n      const results = await executeQuery<{\n        competitor_id: number;\n        competitor_name: string;\n        promotion_count: number;\n        avg_price: number;\n        avg_discount_rate: number;\n      }>(query, [limit]);\n\n      console.log(`✅ 成功获取${results.length}个热门竞品`);\n      return results;\n    } catch (error) {\n      console.error('❌ 获取热门竞品排行榜失败:', error);\n      throw new Error(`获取排行榜失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n}\n\n// 导出单例实例\nexport const promotionDAO = new PromotionDAO();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAED;AACA;;;AAeO,MAAM,qBAAqB,kIAAA,CAAA,UAAO;IACvC,aAAc;QACZ,KAAK,CAAC,cAAc;IACtB;IAEA;;;;;GAKC,GACD,MAAM,gBACJ,MAAwB,EACxB,UAA4B,CAAC,CAAC,EACa;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,YAAY;YAExB,MAAM,kBAA4B,EAAE;YACpC,MAAM,cAAqB,EAAE;YAE7B,QAAQ;YACR,IAAI,QAAQ,aAAa,EAAE;gBACzB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,aAAa;YACxC;YAEA,QAAQ;YACR,IAAI,QAAQ,cAAc,EAAE;gBAC1B,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,cAAc;YACzC;YAEA,UAAU;YACV,IAAI,QAAQ,aAAa,EAAE;gBACzB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,CAAC,CAAC;YAC/C;YAEA,UAAU;YACV,IAAI,QAAQ,aAAa,EAAE;gBACzB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,aAAa,CAAC,CAAC,CAAC;YAC/C;YAEA,YAAY;YACZ,IAAI,QAAQ,eAAe,EAAE;gBAC3B,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,eAAe;YAC1C;YACA,IAAI,QAAQ,aAAa,EAAE;gBACzB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,aAAa;YACxC;YAEA,YAAY;YACZ,IAAI,QAAQ,cAAc,EAAE;gBAC1B,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,cAAc;YACzC;YACA,IAAI,QAAQ,YAAY,EAAE;gBACxB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,YAAY;YACvC;YAEA,UAAU;YACV,IAAI,QAAQ,SAAS,EAAE;gBACrB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,SAAS;YACpC;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,gBAAgB,IAAI,CAAC;gBACrB,YAAY,IAAI,CAAC,QAAQ,SAAS;YACpC;YAEA,MAAM,cAAc,gBAAgB,MAAM,GAAG,IAAI,gBAAgB,IAAI,CAAC,WAAW;YAEjF,OAAO,MAAM,IAAI,CAAC,0BAA0B,CAAC,QAAQ,aAAa;QACpE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;;;GAMC,GACD,MAAc,2BACZ,MAAwB,EACxB,cAAsB,EAAE,EACxB,cAAqB,EAAE,EACoB;QAC3C,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,gBAAgB,EAAE,YAAY,MAAM,EAAE,GAAG;YAC1E,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;YAE5B,YAAY;YACZ,MAAM,WAAW,cAAc,CAAC,MAAM,EAAE,aAAa,GAAG;YAExD,OAAO;YACP,MAAM,aAAa,CAAC;;;;;QAKlB,EAAE,SAAS;MACb,CAAC;YACD,MAAM,eAAe,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAqB,YAAY;YACvE,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,KAAK;YAEnC,eAAe;YACf,MAAM,YAAY,CAAC;;;;;;;;;;;;;;;;;QAiBjB,EAAE,SAAS;iBACF,EAAE,OAAO,CAAC,EAAE,UAAU;cACzB,EAAE,SAAS,QAAQ,EAAE,OAAO;MACpC,CAAC;YAED,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAO,WAAW;YAEvD,SAAS;YACT,MAAM,aAAgC,YAAY,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC5D,cAAc,IAAI,YAAY;oBAC9B,eAAe,IAAI,aAAa;oBAChC,gBAAgB,IAAI,cAAc;oBAClC,eAAe,IAAI,aAAa;oBAChC,WAAW,IAAI,SAAS;oBACxB,aAAa,IAAI,WAAW;oBAC5B,iBAAiB,IAAI,eAAe;oBACpC,eAAe,IAAI,aAAa;oBAChC,gBAAgB,IAAI,cAAc;oBAClC,cAAc,IAAI,YAAY;oBAC9B,eAAe,IAAI,aAAa;oBAChC,aAAa,IAAI,WAAW;oBAC5B,iBAAiB,IAAI,eAAe;oBACpC,YAAY,IAAI,UAAU;oBAC1B,SAAS,IAAI,OAAO;oBACpB,YAAY,IAAI,eAAe,GAAG;wBAChC,eAAe,IAAI,aAAa;wBAChC,iBAAiB,IAAI,eAAe;wBACpC,MAAM,IAAI,IAAI;wBACd,WAAW,IAAI,SAAS;wBACxB,WAAW,IAAI,oBAAoB;oBACrC,IAAI;oBACJ,aAAa,IAAI,gBAAgB,GAAG;wBAClC,gBAAgB,IAAI,cAAc;wBAClC,kBAAkB,IAAI,gBAAgB;wBACtC,UAAU,IAAI,eAAe;oBAC/B,IAAI;oBACJ,eAAe,IAAI,aAAa;gBAClC,CAAC;YAED,MAAM,aAAa,KAAK,IAAI,CAAC,QAAQ;YAErC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,MAAM,OAAO,EAAE,WAAW,MAAM,CAAC,CAAC,CAAC;YAE/D,OAAO;gBACL,MAAM;gBACN;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;GAIC,GACD,MAAM,eAAe,WAAmB,EAAmC;QACzE,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,aAAa;YAE5C,MAAM,QAAQ,CAAC;;;;;;;;;;;;;;;;;;MAkBf,CAAC;YAED,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAO,OAAO;gBAAC;aAAY;YAE5D,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,YAAY,KAAK,CAAC;gBAC1C,OAAO;YACT;YAEA,MAAM,MAAM,OAAO,CAAC,EAAE;YACtB,MAAM,YAA6B;gBACjC,cAAc,IAAI,YAAY;gBAC9B,eAAe,IAAI,aAAa;gBAChC,gBAAgB,IAAI,cAAc;gBAClC,eAAe,IAAI,aAAa;gBAChC,WAAW,IAAI,SAAS;gBACxB,aAAa,IAAI,WAAW;gBAC5B,iBAAiB,IAAI,eAAe;gBACpC,eAAe,IAAI,aAAa;gBAChC,gBAAgB,IAAI,cAAc;gBAClC,cAAc,IAAI,YAAY;gBAC9B,eAAe,IAAI,aAAa;gBAChC,aAAa,IAAI,WAAW;gBAC5B,iBAAiB,IAAI,eAAe;gBACpC,YAAY,IAAI,UAAU;gBAC1B,SAAS,IAAI,OAAO;gBACpB,YAAY,IAAI,eAAe,GAAG;oBAChC,eAAe,IAAI,aAAa;oBAChC,iBAAiB,IAAI,eAAe;oBACpC,MAAM,IAAI,IAAI;oBACd,WAAW,IAAI,SAAS;oBACxB,WAAW,IAAI,oBAAoB;gBACrC,IAAI;gBACJ,aAAa,IAAI,gBAAgB,GAAG;oBAClC,gBAAgB,IAAI,cAAc;oBAClC,kBAAkB,IAAI,gBAAgB;oBACtC,UAAU,IAAI,eAAe;gBAC/B,IAAI;gBACJ,eAAe,IAAI,aAAa;YAClC;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC9E;IACF;IAEA;;;;;GAKC,GACD,MAAM,mBAAmB,YAAoB,EAAE,QAAgB,EAAE,EAA8B;QAC7F,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC;YAE3C,MAAM,SAA2B;gBAC/B,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,WAAW;YACb;YAEA,MAAM,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAClD,QACA,uBACA;gBAAC;aAAa;YAGhB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;;GAKC,GACD,MAAM,mBAAmB,YAAoB,EAAE,QAAgB,EAAE,EAA8B;QAC7F,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC;YAE3C,MAAM,SAA2B;gBAC/B,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,WAAW;YACb;YAEA,MAAM,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAClD,QACA,wBACA;gBAAC;aAAa;YAGhB,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAC/C,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;GAIC,GACD,MAAM,4BAA4B,QAAgB,EAAE,EAA8B;QAChF,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,MAAM,SAA2B;gBAC/B,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,WAAW;YACb;YAEA,MAAM,cAAc,CAAC;;;MAGrB,CAAC;YAED,MAAM,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAClD,QACA,aACA;gBAAC;gBAAa;aAAY;YAG5B,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACpD,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClF;IACF;IAEA;;;;;GAKC,GACD,MAAM,uBAAuB,OAAe,CAAC,EAAE,QAAgB,EAAE,EAA8B;QAC7F,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,WAAW,CAAC;YAErC,MAAM,cAAc,IAAI;YACxB,MAAM,aAAa,IAAI,KAAK,YAAY,OAAO,KAAK,OAAO,KAAK,KAAK,KAAK;YAE1E,MAAM,iBAAiB,YAAY,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC9D,MAAM,gBAAgB,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAE5D,MAAM,SAA2B;gBAC/B,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,WAAW;YACb;YAEA,MAAM,cAAc,CAAC;;;;MAIrB,CAAC;YAED,MAAM,SAAS,MAAM,IAAI,CAAC,0BAA0B,CAClD,QACA,aACA;gBAAC;gBAAgB;aAAc;YAGjC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACpD,OAAO,OAAO,IAAI;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACpF;IACF;IAEA;;;GAGC,GACD,MAAM,sBAAyC;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,QAAQ,CAAC;;;;;MAKf,CAAC;YACD,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAA6B;YAE9D,MAAM,WAAW,QAAQ,GAAG,CAAC,CAAA,MAAO,IAAI,aAAa;YACrD,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,MAAM,CAAC,KAAK,CAAC;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,YAAY,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAClF;IACF;IAEA;;;GAGC,GACD,MAAM,gBAQH;QACD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAE1D,MAAM,UAAU;gBACd;gBACA,CAAC,0GAA0G,EAAE,YAAY,mDAAmD,EAAE,YAAY,EAAE,CAAC;gBAC7L,CAAC,0GAA0G,EAAE,YAAY,CAAC,CAAC;gBAC3H;gBACA;gBACA;gBACA;aACD;YAED,MAAM,CACJ,aACA,cACA,eACA,gBACA,aACA,kBACA,iBACD,GAAG,MAAM,QAAQ,GAAG,CACnB,QAAQ,GAAG,CAAC,CAAA,QAAS,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAA6B;YAG/D,MAAM,aAAa;gBACjB,OAAO,WAAW,CAAC,EAAE,CAAC,KAAK;gBAC3B,kBAAkB,YAAY,CAAC,EAAE,CAAC,gBAAgB;gBAClD,mBAAmB,aAAa,CAAC,EAAE,CAAC,iBAAiB;gBACrD,iBAAiB,cAAc,CAAC,EAAE,CAAC,eAAe,IAAI;gBACtD,UAAU,WAAW,CAAC,EAAE,CAAC,QAAQ,IAAI;gBACrC,kBAAkB,gBAAgB,CAAC,EAAE,CAAC,gBAAgB;gBACtD,kBAAkB,gBAAgB,CAAC,EAAE,CAAC,gBAAgB;YACxD;YAEA,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;;GAIC,GACD,MAAM,8BAA8B,QAAgB,EAAE,EAMlD;QACF,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAErC,MAAM,QAAQ,CAAC;;;;;;;;;;;;MAYf,CAAC;YAED,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAM9B,OAAO;gBAAC;aAAM;YAEjB,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,MAAM,CAAC,KAAK,CAAC;YAC1C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;YAChC,MAAM,IAAI,MAAM,CAAC,SAAS,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAC/E;IACF;AACF;AAGO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/dao/index.ts"], "sourcesContent": ["/**\n * 数据访问对象(DAO)统一导出文件\n * \n * 功能说明：\n * 1. 统一导出所有DAO类和实例\n * 2. 提供DAO的中央管理\n * 3. 方便其他模块引用和使用\n */\n\n// ============================================================================\n// 导出基础DAO类\n// ============================================================================\n\nexport { BaseDAO } from './base-dao';\n\n// ============================================================================\n// 导出具体DAO类和实例\n// ============================================================================\n\n// 竞品DAO\nexport { CompetitorDAO, competitorDAO } from './competitor-dao';\n\n// 票种DAO\nexport { TicketTypeDAO, ticketTypeDAO } from './ticket-type-dao';\n\n// 促销活动DAO\nexport { PromotionDAO, promotionDAO } from './promotion-dao';\n\n// 导入DAO实例\nimport { competitorDAO } from './competitor-dao';\nimport { ticketTypeDAO } from './ticket-type-dao';\nimport { promotionDAO } from './promotion-dao';\n\n// ============================================================================\n// 创建DAO管理器类\n// ============================================================================\n\n/**\n * DAO管理器类\n * 提供统一的DAO访问接口\n */\nexport class DAOManager {\n  // DAO实例 - 使用getter延迟初始化\n  public get competitor() {\n    return competitorDAO;\n  }\n\n  public get ticketType() {\n    return ticketTypeDAO;\n  }\n\n  public get promotion() {\n    return promotionDAO;\n  }\n\n  /**\n   * 获取所有DAO实例\n   * @returns DAO实例对象\n   */\n  getAllDAOs() {\n    return {\n      competitor: this.competitor,\n      ticketType: this.ticketType,\n      promotion: this.promotion\n    };\n  }\n\n  /**\n   * 检查所有DAO的数据库连接\n   * @returns 连接状态\n   */\n  async checkConnections(): Promise<{\n    competitor: boolean;\n    ticketType: boolean;\n    promotion: boolean;\n    allConnected: boolean;\n  }> {\n    try {\n      console.log('🔍 检查所有DAO的数据库连接状态');\n\n      const [competitorCheck, ticketTypeCheck, promotionCheck] = await Promise.all([\n        this.competitor.exists(1).then(() => true).catch(() => false),\n        this.ticketType.exists(1).then(() => true).catch(() => false),\n        this.promotion.exists(1).then(() => true).catch(() => false)\n      ]);\n\n      const result = {\n        competitor: competitorCheck,\n        ticketType: ticketTypeCheck,\n        promotion: promotionCheck,\n        allConnected: competitorCheck && ticketTypeCheck && promotionCheck\n      };\n\n      console.log('✅ DAO连接检查完成:', result);\n      return result;\n    } catch (error) {\n      console.error('❌ DAO连接检查失败:', error);\n      return {\n        competitor: false,\n        ticketType: false,\n        promotion: false,\n        allConnected: false\n      };\n    }\n  }\n\n  /**\n   * 获取系统整体统计信息\n   * @returns 统计信息\n   */\n  async getSystemStatistics(): Promise<{\n    competitors: any;\n    ticketTypes: any;\n    promotions: any;\n    summary: {\n      totalRecords: number;\n      lastUpdated: string;\n    };\n  }> {\n    try {\n      console.log('📊 获取系统整体统计信息');\n\n      const [competitorStats, ticketTypeStats, promotionStats] = await Promise.all([\n        this.competitor.getStatistics(),\n        this.ticketType.getStatistics(),\n        this.promotion.getStatistics()\n      ]);\n\n      const totalRecords = competitorStats.total + ticketTypeStats.total + promotionStats.total;\n\n      const result = {\n        competitors: competitorStats,\n        ticketTypes: ticketTypeStats,\n        promotions: promotionStats,\n        summary: {\n          totalRecords,\n          lastUpdated: new Date().toISOString()\n        }\n      };\n\n      console.log('✅ 成功获取系统统计信息');\n      return result;\n    } catch (error) {\n      console.error('❌ 获取系统统计信息失败:', error);\n      throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);\n    }\n  }\n\n  /**\n   * 清理和优化所有表\n   * @returns 清理结果\n   */\n  async optimizeTables(): Promise<{\n    success: boolean;\n    message: string;\n    details: string[];\n  }> {\n    try {\n      console.log('🔧 开始优化数据库表');\n\n      const details: string[] = [];\n\n      // 这里可以添加具体的优化操作\n      // 例如：清理无效数据、重建索引等\n      details.push('数据库表优化功能待实现');\n\n      console.log('✅ 数据库表优化完成');\n\n      return {\n        success: true,\n        message: '数据库表优化完成',\n        details\n      };\n    } catch (error) {\n      console.error('❌ 数据库表优化失败:', error);\n      return {\n        success: false,\n        message: `优化失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        details: []\n      };\n    }\n  }\n}\n\n// ============================================================================\n// 导出DAO管理器单例实例\n// ============================================================================\n\nexport const daoManager = new DAOManager();\n\n// ============================================================================\n// 导出常用的DAO操作函数\n// ============================================================================\n\n/**\n * 快速获取竞品列表\n * @param filters 筛选条件\n * @param pagination 分页参数\n * @returns 竞品列表\n */\nexport const getCompetitors = (filters?: any, pagination?: any) => {\n  return competitorDAO.findWithFilters(pagination || { page: 1, pageSize: 10 }, filters);\n};\n\n/**\n * 快速获取票种列表\n * @param filters 筛选条件\n * @param pagination 分页参数\n * @returns 票种列表\n */\nexport const getTicketTypes = (filters?: any, pagination?: any) => {\n  return ticketTypeDAO.findWithFilters(pagination || { page: 1, pageSize: 10 }, filters);\n};\n\n/**\n * 快速获取促销活动列表\n * @param filters 筛选条件\n * @param pagination 分页参数\n * @returns 促销活动列表\n */\nexport const getPromotions = (filters?: any, pagination?: any) => {\n  return promotionDAO.findWithFilters(pagination || { page: 1, pageSize: 10 }, filters);\n};\n\n/**\n * 快速创建竞品\n * @param data 竞品数据\n * @returns 创建结果\n */\nexport const createCompetitor = (data: any) => {\n  return competitorDAO.create(data);\n};\n\n/**\n * 快速创建票种\n * @param data 票种数据\n * @returns 创建结果\n */\nexport const createTicketType = (data: any) => {\n  return ticketTypeDAO.create(data);\n};\n\n/**\n * 快速创建促销活动\n * @param data 促销活动数据\n * @returns 创建结果\n */\nexport const createPromotion = (data: any) => {\n  return promotionDAO.create(data);\n};\n\n// ============================================================================\n// 导出默认实例（向后兼容）\n// ============================================================================\n\nexport default daoManager;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,+EAA+E;AAC/E,WAAW;AACX,+EAA+E;;;;;;;;;;;;AAE/E;AAEA,+EAA+E;AAC/E,cAAc;AACd,+EAA+E;AAE/E,QAAQ;AACR;AAEA,QAAQ;AACR;AAEA,UAAU;AACV;;;;;;;;AAeO,MAAM;IACX,wBAAwB;IACxB,IAAW,aAAa;QACtB,OAAO,wIAAA,CAAA,gBAAa;IACtB;IAEA,IAAW,aAAa;QACtB,OAAO,4IAAA,CAAA,gBAAa;IACtB;IAEA,IAAW,YAAY;QACrB,OAAO,uIAAA,CAAA,eAAY;IACrB;IAEA;;;GAGC,GACD,aAAa;QACX,OAAO;YACL,YAAY,IAAI,CAAC,UAAU;YAC3B,YAAY,IAAI,CAAC,UAAU;YAC3B,WAAW,IAAI,CAAC,SAAS;QAC3B;IACF;IAEA;;;GAGC,GACD,MAAM,mBAKH;QACD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,CAAC,iBAAiB,iBAAiB,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3E,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAM,MAAM,KAAK,CAAC,IAAM;gBACvD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAM,MAAM,KAAK,CAAC,IAAM;gBACvD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAM,MAAM,KAAK,CAAC,IAAM;aACvD;YAED,MAAM,SAAS;gBACb,YAAY;gBACZ,YAAY;gBACZ,WAAW;gBACX,cAAc,mBAAmB,mBAAmB;YACtD;YAEA,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,OAAO;gBACL,YAAY;gBACZ,YAAY;gBACZ,WAAW;gBACX,cAAc;YAChB;QACF;IACF;IAEA;;;GAGC,GACD,MAAM,sBAQH;QACD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,CAAC,iBAAiB,iBAAiB,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC3E,IAAI,CAAC,UAAU,CAAC,aAAa;gBAC7B,IAAI,CAAC,UAAU,CAAC,aAAa;gBAC7B,IAAI,CAAC,SAAS,CAAC,aAAa;aAC7B;YAED,MAAM,eAAe,gBAAgB,KAAK,GAAG,gBAAgB,KAAK,GAAG,eAAe,KAAK;YAEzF,MAAM,SAAS;gBACb,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,SAAS;oBACP;oBACA,aAAa,IAAI,OAAO,WAAW;gBACrC;YACF;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QAChF;IACF;IAEA;;;GAGC,GACD,MAAM,iBAIH;QACD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,MAAM,UAAoB,EAAE;YAE5B,gBAAgB;YAChB,kBAAkB;YAClB,QAAQ,IAAI,CAAC;YAEb,QAAQ,GAAG,CAAC;YAEZ,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,eAAe;YAC7B,OAAO;gBACL,SAAS;gBACT,SAAS,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;gBACnE,SAAS,EAAE;YACb;QACF;IACF;AACF;AAMO,MAAM,aAAa,IAAI;AAYvB,MAAM,iBAAiB,CAAC,SAAe;IAC5C,OAAO,wIAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,cAAc;QAAE,MAAM;QAAG,UAAU;IAAG,GAAG;AAChF;AAQO,MAAM,iBAAiB,CAAC,SAAe;IAC5C,OAAO,4IAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,cAAc;QAAE,MAAM;QAAG,UAAU;IAAG,GAAG;AAChF;AAQO,MAAM,gBAAgB,CAAC,SAAe;IAC3C,OAAO,uIAAA,CAAA,eAAY,CAAC,eAAe,CAAC,cAAc;QAAE,MAAM;QAAG,UAAU;IAAG,GAAG;AAC/E;AAOO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,wIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;AAC9B;AAOO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,4IAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;AAC9B;AAOO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,uIAAA,CAAA,eAAY,CAAC,MAAM,CAAC;AAC7B;uCAMe", "debugId": null}}, {"offset": {"line": 1785, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/types/forms.ts"], "sourcesContent": ["/**\n * 表单相关的TypeScript类型定义\n * \n * 功能说明：\n * 1. 定义表单字段的验证规则\n * 2. 提供表单状态管理类型\n * 3. 支持表单错误处理和用户反馈\n */\n\n// ============================================================================\n// 表单验证相关类型定义\n// ============================================================================\n\n/**\n * 表单字段验证错误接口\n */\nexport interface FieldError {\n  field: string;               // 字段名称\n  message: string;             // 错误消息\n  code?: string;              // 错误代码\n}\n\n/**\n * 表单验证结果接口\n */\nexport interface ValidationResult {\n  isValid: boolean;            // 是否验证通过\n  errors: FieldError[];        // 验证错误列表\n}\n\n/**\n * 表单状态枚举\n */\nexport enum FormStatus {\n  IDLE = 'idle',              // 空闲状态\n  LOADING = 'loading',        // 加载中\n  SUBMITTING = 'submitting',  // 提交中\n  SUCCESS = 'success',        // 成功\n  ERROR = 'error'             // 错误\n}\n\n/**\n * 表单状态接口\n */\nexport interface FormState {\n  status: FormStatus;          // 当前状态\n  message?: string;           // 状态消息\n  errors?: FieldError[];      // 字段错误列表\n}\n\n// ============================================================================\n// 竞品表单相关类型定义\n// ============================================================================\n\n/**\n * 竞品表单数据接口\n */\nexport interface CompetitorFormData {\n  competitor_name: string;     // 竞品名称\n  city: string;               // 所在城市\n  park_type: string;          // 园区类型\n  is_active: boolean;         // 是否活跃\n}\n\n/**\n * 竞品表单验证规则接口\n */\nexport interface CompetitorFormValidation {\n  competitor_name: {\n    required: boolean;         // 是否必填\n    minLength: number;         // 最小长度\n    maxLength: number;         // 最大长度\n  };\n  city: {\n    maxLength: number;         // 最大长度\n  };\n  park_type: {\n    maxLength: number;         // 最大长度\n  };\n}\n\n// ============================================================================\n// 票种表单相关类型定义\n// ============================================================================\n\n/**\n * 票种表单数据接口\n */\nexport interface TicketTypeFormData {\n  ticket_type_name: string;    // 票种名称\n  category: string;           // 票种分类\n}\n\n/**\n * 票种表单验证规则接口\n */\nexport interface TicketTypeFormValidation {\n  ticket_type_name: {\n    required: boolean;         // 是否必填\n    minLength: number;         // 最小长度\n    maxLength: number;         // 最大长度\n  };\n  category: {\n    maxLength: number;         // 最大长度\n  };\n}\n\n// ============================================================================\n// 促销活动表单相关类型定义\n// ============================================================================\n\n/**\n * 促销活动表单数据接口\n */\nexport interface PromotionFormData {\n  competitor_id: number | '';  // 竞品ID\n  ticket_type_id: number | ''; // 票种ID\n  activity_name: string;       // 活动名称\n  rack_rate: number | '';      // 门市价\n  promo_price: number | '';    // 促销价\n  sale_start_date: string;     // 销售开始日期\n  sale_end_date: string;       // 销售结束日期\n  use_start_date: string;      // 使用开始日期\n  use_end_date: string;        // 使用结束日期\n  sales_channel: string;       // 销售渠道\n  usage_rules: string;         // 使用规则\n  data_source_url: string;     // 数据来源URL\n  remarks: string;             // 备注\n}\n\n/**\n * 促销活动表单验证规则接口\n */\nexport interface PromotionFormValidation {\n  competitor_id: {\n    required: boolean;         // 是否必填\n  };\n  ticket_type_id: {\n    required: boolean;         // 是否必填\n  };\n  activity_name: {\n    required: boolean;         // 是否必填\n    minLength: number;         // 最小长度\n    maxLength: number;         // 最大长度\n  };\n  rack_rate: {\n    min: number;              // 最小值\n    max: number;              // 最大值\n  };\n  promo_price: {\n    min: number;              // 最小值\n    max: number;              // 最大值\n  };\n  sales_channel: {\n    maxLength: number;         // 最大长度\n  };\n  data_source_url: {\n    maxLength: number;         // 最大长度\n    pattern?: RegExp;         // URL格式验证\n  };\n}\n\n// ============================================================================\n// 日历表单相关类型定义\n// ============================================================================\n\n/**\n * 销售日历表单数据接口\n */\nexport interface DailySalesFormData {\n  sales_date: string;          // 销售日期\n  promotion_id: number | '';   // 促销活动ID\n  promo_price: number | '';    // 促销价\n  rack_rate: number | '';      // 门市价\n  discount_rate: number | '';  // 折扣率\n}\n\n/**\n * 使用日历表单数据接口\n */\nexport interface DailyUsageFormData {\n  usage_date: string;          // 使用日期\n  promotion_id: number | '';   // 促销活动ID\n  promo_price: number | '';    // 促销价\n  rack_rate: number | '';      // 门市价\n  discount_rate: number | '';  // 折扣率\n}\n\n// ============================================================================\n// 搜索和筛选表单相关类型定义\n// ============================================================================\n\n/**\n * 搜索表单数据接口\n */\nexport interface SearchFormData {\n  keyword: string;             // 搜索关键词\n  category: string;           // 搜索分类\n  dateRange: {\n    start: string;            // 开始日期\n    end: string;              // 结束日期\n  };\n  priceRange: {\n    min: number | '';         // 最低价格\n    max: number | '';         // 最高价格\n  };\n}\n\n/**\n * 筛选表单数据接口\n */\nexport interface FilterFormData {\n  competitor_ids: number[];    // 选中的竞品ID列表\n  ticket_type_ids: number[];  // 选中的票种ID列表\n  cities: string[];           // 选中的城市列表\n  park_types: string[];       // 选中的园区类型列表\n  sales_channels: string[];   // 选中的销售渠道列表\n  is_active?: boolean;        // 是否活跃筛选\n}\n\n// ============================================================================\n// 表单选项相关类型定义\n// ============================================================================\n\n/**\n * 下拉选项接口\n */\nexport interface SelectOption {\n  value: string | number;      // 选项值\n  label: string;              // 显示文本\n  disabled?: boolean;         // 是否禁用\n  group?: string;             // 分组名称\n}\n\n/**\n * 竞品选项接口\n */\nexport interface CompetitorOption extends SelectOption {\n  value: number;              // 竞品ID\n  city?: string;              // 城市信息\n  park_type?: string;         // 园区类型\n  is_active: boolean;         // 是否活跃\n}\n\n/**\n * 票种选项接口\n */\nexport interface TicketTypeOption extends SelectOption {\n  value: number;              // 票种ID\n  category?: string;          // 分类信息\n}\n\n// ============================================================================\n// 表单操作相关类型定义\n// ============================================================================\n\n/**\n * 表单操作类型枚举\n */\nexport enum FormAction {\n  CREATE = 'create',          // 创建\n  UPDATE = 'update',          // 更新\n  DELETE = 'delete',          // 删除\n  VIEW = 'view'               // 查看\n}\n\n/**\n * 表单模式接口\n */\nexport interface FormMode {\n  action: FormAction;         // 操作类型\n  title: string;              // 表单标题\n  submitText: string;         // 提交按钮文本\n  readonly: boolean;          // 是否只读\n}\n\n/**\n * 表单配置接口\n */\nexport interface FormConfig {\n  mode: FormMode;             // 表单模式\n  initialData?: any;          // 初始数据\n  validation?: any;           // 验证规则\n  onSubmit: (data: any) => Promise<void>; // 提交处理函数\n  onCancel?: () => void;      // 取消处理函数\n}\n\n// ============================================================================\n// 批量操作相关类型定义\n// ============================================================================\n\n/**\n * 批量操作类型枚举\n */\nexport enum BatchAction {\n  DELETE = 'delete',          // 批量删除\n  UPDATE_STATUS = 'update_status', // 批量更新状态\n  EXPORT = 'export',          // 批量导出\n  IMPORT = 'import'           // 批量导入\n}\n\n/**\n * 批量操作数据接口\n */\nexport interface BatchOperationData {\n  action: BatchAction;        // 操作类型\n  selectedIds: (string | number)[]; // 选中的记录ID列表\n  params?: any;              // 操作参数\n}\n\n/**\n * 批量操作结果接口\n */\nexport interface BatchOperationResult {\n  success: boolean;           // 是否成功\n  successCount: number;       // 成功数量\n  failureCount: number;       // 失败数量\n  errors?: string[];          // 错误信息列表\n  message?: string;           // 结果消息\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,+EAA+E;AAC/E,aAAa;AACb,+EAA+E;AAE/E;;CAEC;;;;;AAkBM,IAAA,AAAK,oCAAA;;;;;mCAKkB,KAAK;WALvB;;AAkOL,IAAA,AAAK,oCAAA;;;;iCAIkB,KAAK;WAJvB;;AAmCL,IAAA,AAAK,qCAAA;;;;sCAIkB,OAAO;WAJzB", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/types/api.ts"], "sourcesContent": ["/**\n * API相关的TypeScript类型定义\n * \n * 功能说明：\n * 1. 定义API请求和响应的数据结构\n * 2. 提供统一的错误处理类型\n * 3. 支持API状态管理和加载状态\n */\n\nimport { \n  Competitor, \n  TicketType, \n  Promotion, \n  PromotionDetail,\n  DailySales, \n  DailyUsage,\n  PaginatedResult,\n  PaginationParams\n} from './database';\n\n// ============================================================================\n// 基础API类型定义\n// ============================================================================\n\n/**\n * HTTP方法枚举\n */\nexport enum HttpMethod {\n  GET = 'GET',\n  POST = 'POST',\n  PUT = 'PUT',\n  PATCH = 'PATCH',\n  DELETE = 'DELETE'\n}\n\n/**\n * API错误类型枚举\n */\nexport enum ApiErrorType {\n  NETWORK_ERROR = 'NETWORK_ERROR',           // 网络错误\n  VALIDATION_ERROR = 'VALIDATION_ERROR',     // 验证错误\n  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR', // 认证错误\n  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',   // 授权错误\n  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',       // 资源不存在\n  SERVER_ERROR = 'SERVER_ERROR',             // 服务器错误\n  DATABASE_ERROR = 'DATABASE_ERROR',         // 数据库错误\n  UNKNOWN_ERROR = 'UNKNOWN_ERROR'            // 未知错误\n}\n\n/**\n * API错误详情接口\n */\nexport interface ApiError {\n  type: ApiErrorType;        // 错误类型\n  message: string;           // 错误消息\n  code?: string | number;    // 错误代码\n  details?: any;            // 错误详情\n  timestamp: string;        // 错误时间戳\n}\n\n/**\n * 统一API响应接口\n */\nexport interface ApiResponse<T = any> {\n  success: boolean;          // 请求是否成功\n  data?: T;                 // 响应数据\n  message?: string;         // 响应消息\n  error?: ApiError;         // 错误信息\n  timestamp: string;        // 响应时间戳\n}\n\n/**\n * API请求状态枚举\n */\nexport enum ApiStatus {\n  IDLE = 'idle',            // 空闲状态\n  LOADING = 'loading',      // 加载中\n  SUCCESS = 'success',      // 成功\n  ERROR = 'error'           // 错误\n}\n\n/**\n * API请求状态接口\n */\nexport interface ApiState<T = any> {\n  status: ApiStatus;        // 请求状态\n  data?: T;                // 响应数据\n  error?: ApiError;        // 错误信息\n  loading: boolean;        // 是否正在加载\n}\n\n// ============================================================================\n// 竞品相关API类型定义\n// ============================================================================\n\n/**\n * 获取竞品列表的API响应\n */\nexport type GetCompetitorsResponse = ApiResponse<PaginatedResult<Competitor>>;\n\n/**\n * 获取单个竞品的API响应\n */\nexport type GetCompetitorResponse = ApiResponse<Competitor>;\n\n/**\n * 创建竞品的API请求体\n */\nexport interface CreateCompetitorRequest {\n  competitor_name: string;\n  city?: string;\n  park_type?: string;\n  is_active?: boolean;\n}\n\n/**\n * 创建竞品的API响应\n */\nexport type CreateCompetitorResponse = ApiResponse<Competitor>;\n\n/**\n * 更新竞品的API请求体\n */\nexport interface UpdateCompetitorRequest {\n  competitor_name?: string;\n  city?: string;\n  park_type?: string;\n  is_active?: boolean;\n}\n\n/**\n * 更新竞品的API响应\n */\nexport type UpdateCompetitorResponse = ApiResponse<Competitor>;\n\n/**\n * 删除竞品的API响应\n */\nexport type DeleteCompetitorResponse = ApiResponse<{ deleted: boolean }>;\n\n// ============================================================================\n// 票种相关API类型定义\n// ============================================================================\n\n/**\n * 获取票种列表的API响应\n */\nexport type GetTicketTypesResponse = ApiResponse<PaginatedResult<TicketType>>;\n\n/**\n * 获取单个票种的API响应\n */\nexport type GetTicketTypeResponse = ApiResponse<TicketType>;\n\n/**\n * 创建票种的API请求体\n */\nexport interface CreateTicketTypeRequest {\n  ticket_type_name: string;\n  category?: string;\n}\n\n/**\n * 创建票种的API响应\n */\nexport type CreateTicketTypeResponse = ApiResponse<TicketType>;\n\n/**\n * 更新票种的API请求体\n */\nexport interface UpdateTicketTypeRequest {\n  ticket_type_name?: string;\n  category?: string;\n}\n\n/**\n * 更新票种的API响应\n */\nexport type UpdateTicketTypeResponse = ApiResponse<TicketType>;\n\n/**\n * 删除票种的API响应\n */\nexport type DeleteTicketTypeResponse = ApiResponse<{ deleted: boolean }>;\n\n// ============================================================================\n// 促销活动相关API类型定义\n// ============================================================================\n\n/**\n * 获取促销活动列表的API响应\n */\nexport type GetPromotionsResponse = ApiResponse<PaginatedResult<PromotionDetail>>;\n\n/**\n * 获取单个促销活动的API响应\n */\nexport type GetPromotionResponse = ApiResponse<PromotionDetail>;\n\n/**\n * 创建促销活动的API请求体\n */\nexport interface CreatePromotionRequest {\n  competitor_id: number;\n  ticket_type_id: number;\n  activity_name: string;\n  rack_rate?: number;\n  promo_price?: number;\n  sale_start_date?: string;\n  sale_end_date?: string;\n  use_start_date?: string;\n  use_end_date?: string;\n  sales_channel?: string;\n  usage_rules?: string;\n  data_source_url?: string;\n  remarks?: string;\n}\n\n/**\n * 创建促销活动的API响应\n */\nexport type CreatePromotionResponse = ApiResponse<Promotion>;\n\n/**\n * 更新促销活动的API请求体\n */\nexport interface UpdatePromotionRequest {\n  competitor_id?: number;\n  ticket_type_id?: number;\n  activity_name?: string;\n  rack_rate?: number;\n  promo_price?: number;\n  sale_start_date?: string;\n  sale_end_date?: string;\n  use_start_date?: string;\n  use_end_date?: string;\n  sales_channel?: string;\n  usage_rules?: string;\n  data_source_url?: string;\n  remarks?: string;\n}\n\n/**\n * 更新促销活动的API响应\n */\nexport type UpdatePromotionResponse = ApiResponse<Promotion>;\n\n/**\n * 删除促销活动的API响应\n */\nexport type DeletePromotionResponse = ApiResponse<{ deleted: boolean }>;\n\n// ============================================================================\n// 日历相关API类型定义\n// ============================================================================\n\n/**\n * 获取销售日历列表的API响应\n */\nexport type GetDailySalesResponse = ApiResponse<PaginatedResult<DailySales>>;\n\n/**\n * 创建销售日历记录的API请求体\n */\nexport interface CreateDailySalesRequest {\n  sales_date: string;\n  promotion_id: number;\n  promo_price?: number;\n  rack_rate?: number;\n  discount_rate?: number;\n}\n\n/**\n * 创建销售日历记录的API响应\n */\nexport type CreateDailySalesResponse = ApiResponse<DailySales>;\n\n/**\n * 获取使用日历列表的API响应\n */\nexport type GetDailyUsageResponse = ApiResponse<PaginatedResult<DailyUsage>>;\n\n/**\n * 创建使用日历记录的API请求体\n */\nexport interface CreateDailyUsageRequest {\n  usage_date: string;\n  promotion_id: number;\n  promo_price?: number;\n  rack_rate?: number;\n  discount_rate?: number;\n}\n\n/**\n * 创建使用日历记录的API响应\n */\nexport type CreateDailyUsageResponse = ApiResponse<DailyUsage>;\n\n// ============================================================================\n// 查询和筛选相关API类型定义\n// ============================================================================\n\n/**\n * 通用查询参数接口\n */\nexport interface QueryParams extends PaginationParams {\n  search?: string;           // 搜索关键词\n  filters?: Record<string, any>; // 筛选条件\n}\n\n/**\n * 促销活动查询参数接口\n */\nexport interface PromotionQueryParams extends QueryParams {\n  competitor_id?: number;\n  ticket_type_id?: number;\n  activity_name?: string;\n  sales_channel?: string;\n  sale_date_start?: string;\n  sale_date_end?: string;\n  use_date_start?: string;\n  use_date_end?: string;\n  min_price?: number;\n  max_price?: number;\n}\n\n/**\n * 竞品查询参数接口\n */\nexport interface CompetitorQueryParams extends QueryParams {\n  competitor_name?: string;\n  city?: string;\n  park_type?: string;\n  is_active?: boolean;\n}\n\n/**\n * 票种查询参数接口\n */\nexport interface TicketTypeQueryParams extends QueryParams {\n  ticket_type_name?: string;\n  category?: string;\n}\n\n// ============================================================================\n// 批量操作相关API类型定义\n// ============================================================================\n\n/**\n * 批量删除请求体\n */\nexport interface BatchDeleteRequest {\n  ids: (string | number)[];  // 要删除的记录ID列表\n}\n\n/**\n * 批量删除响应\n */\nexport interface BatchDeleteResponse {\n  success: boolean;\n  deleted_count: number;     // 成功删除的数量\n  failed_count: number;      // 删除失败的数量\n  errors?: string[];         // 错误信息列表\n}\n\n/**\n * 批量操作的API响应\n */\nexport type BatchOperationResponse = ApiResponse<BatchDeleteResponse>;\n\n// ============================================================================\n// 统计和分析相关API类型定义\n// ============================================================================\n\n/**\n * 统计数据接口\n */\nexport interface StatisticsData {\n  total_competitors: number;     // 竞品总数\n  total_ticket_types: number;    // 票种总数\n  total_promotions: number;      // 促销活动总数\n  active_promotions: number;     // 活跃促销活动数\n  avg_discount_rate: number;     // 平均折扣率\n  top_competitors: Array<{       // 热门竞品\n    competitor_name: string;\n    promotion_count: number;\n  }>;\n  top_ticket_types: Array<{      // 热门票种\n    ticket_type_name: string;\n    promotion_count: number;\n  }>;\n}\n\n/**\n * 获取统计数据的API响应\n */\nexport type GetStatisticsResponse = ApiResponse<StatisticsData>;\n\n// ============================================================================\n// 导入导出相关API类型定义\n// ============================================================================\n\n/**\n * 导出数据请求参数\n */\nexport interface ExportRequest {\n  format: 'csv' | 'excel' | 'json'; // 导出格式\n  filters?: Record<string, any>;     // 筛选条件\n  fields?: string[];                 // 要导出的字段\n}\n\n/**\n * 导出数据响应\n */\nexport interface ExportResponse {\n  download_url: string;              // 下载链接\n  filename: string;                  // 文件名\n  expires_at: string;                // 过期时间\n}\n\n/**\n * 导出数据的API响应\n */\nexport type ExportDataResponse = ApiResponse<ExportResponse>;\n\n/**\n * 导入数据结果\n */\nexport interface ImportResult {\n  success_count: number;             // 成功导入数量\n  failure_count: number;             // 导入失败数量\n  errors?: Array<{                   // 错误详情\n    row: number;\n    message: string;\n  }>;\n}\n\n/**\n * 导入数据的API响应\n */\nexport type ImportDataResponse = ApiResponse<ImportResult>;\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AAoBM,IAAA,AAAK,oCAAA;;;;;;WAAA;;AAWL,IAAA,AAAK,sCAAA;;;;;;;;qDAQiC,OAAO;WARxC;;AAoCL,IAAA,AAAK,mCAAA;;;;kCAIgB,KAAK;WAJrB", "debugId": null}}, {"offset": {"line": 1870, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/types/index.ts"], "sourcesContent": ["/**\n * TypeScript类型定义统一导出文件\n * \n * 功能说明：\n * 1. 统一导出所有类型定义，方便其他模块引用\n * 2. 提供类型定义的中央管理\n * 3. 避免循环依赖和重复导入\n */\n\n// ============================================================================\n// 数据库相关类型导出\n// ============================================================================\n\nexport type {\n  // 基础类型\n  BaseRecord,\n  PaginationParams,\n  PaginatedResult,\n  ApiResponse,\n\n  // 竞品相关类型\n  Competitor,\n  CreateCompetitorInput,\n  UpdateCompetitorInput,\n\n  // 票种相关类型\n  TicketType,\n  CreateTicketTypeInput,\n  UpdateTicketTypeInput,\n\n  // 促销活动相关类型\n  Promotion,\n  PromotionDetail,\n  CreatePromotionInput,\n  UpdatePromotionInput,\n\n  // 日历相关类型\n  DailySales,\n  DailySalesDetail,\n  CreateDailySalesInput,\n  DailyUsage,\n  DailyUsageDetail,\n  CreateDailyUsageInput,\n\n  // 查询筛选相关类型\n  PromotionFilters,\n  CompetitorFilters,\n  TicketTypeFilters,\n} from './database';\n\n// ============================================================================\n// 表单相关类型导出\n// ============================================================================\n\nexport type {\n  // 验证相关类型\n  FieldError,\n  ValidationResult,\n\n  // 表单状态类型\n  FormState,\n\n  // 表单数据类型\n  CompetitorFormData,\n  CompetitorFormValidation,\n  TicketTypeFormData,\n  TicketTypeFormValidation,\n  PromotionFormData,\n  PromotionFormValidation,\n  DailySalesFormData,\n  DailyUsageFormData,\n\n  // 搜索筛选表单类型\n  SearchFormData,\n  FilterFormData,\n\n  // 表单选项类型\n  SelectOption,\n  CompetitorOption,\n  TicketTypeOption,\n\n  // 表单操作类型\n  FormMode,\n  FormConfig,\n\n  // 批量操作类型\n  BatchOperationData,\n  BatchOperationResult,\n} from './forms';\n\nexport {\n  // 枚举导出\n  FormStatus,\n  FormAction,\n  BatchAction,\n} from './forms';\n\n// ============================================================================\n// API相关类型导出\n// ============================================================================\n\nexport type {\n  // 基础API类型\n  ApiError,\n  ApiState,\n\n  // 竞品API类型\n  GetCompetitorsResponse,\n  GetCompetitorResponse,\n  CreateCompetitorRequest,\n  CreateCompetitorResponse,\n  UpdateCompetitorRequest,\n  UpdateCompetitorResponse,\n  DeleteCompetitorResponse,\n\n  // 票种API类型\n  GetTicketTypesResponse,\n  GetTicketTypeResponse,\n  CreateTicketTypeRequest,\n  CreateTicketTypeResponse,\n  UpdateTicketTypeRequest,\n  UpdateTicketTypeResponse,\n  DeleteTicketTypeResponse,\n\n  // 促销活动API类型\n  GetPromotionsResponse,\n  GetPromotionResponse,\n  CreatePromotionRequest,\n  CreatePromotionResponse,\n  UpdatePromotionRequest,\n  UpdatePromotionResponse,\n  DeletePromotionResponse,\n\n  // 日历API类型\n  GetDailySalesResponse,\n  CreateDailySalesRequest,\n  CreateDailySalesResponse,\n  GetDailyUsageResponse,\n  CreateDailyUsageRequest,\n  CreateDailyUsageResponse,\n\n  // 查询参数类型\n  QueryParams,\n  PromotionQueryParams,\n  CompetitorQueryParams,\n  TicketTypeQueryParams,\n\n  // 批量操作API类型\n  BatchDeleteRequest,\n  BatchDeleteResponse,\n  BatchOperationResponse,\n\n  // 统计分析API类型\n  StatisticsData,\n  GetStatisticsResponse,\n\n  // 导入导出API类型\n  ExportRequest,\n  ExportResponse,\n  ExportDataResponse,\n  ImportResult,\n  ImportDataResponse,\n} from './api';\n\nexport {\n  // 枚举导出\n  HttpMethod,\n  ApiErrorType,\n  ApiStatus,\n} from './api';\n\n// ============================================================================\n// 常用类型别名定义\n// ============================================================================\n\n/**\n * 通用ID类型\n */\nexport type ID = string | number;\n\n/**\n * 日期字符串类型（ISO格式）\n */\nexport type DateString = string;\n\n/**\n * 价格类型（支持小数）\n */\nexport type Price = number;\n\n/**\n * 折扣率类型（0-1之间的小数）\n */\nexport type DiscountRate = number;\n\n/**\n * 通用回调函数类型\n */\nexport type Callback<T = void> = () => T;\n\n/**\n * 异步回调函数类型\n */\nexport type AsyncCallback<T = void> = () => Promise<T>;\n\n/**\n * 事件处理函数类型\n */\nexport type EventHandler<T = any> = (event: T) => void;\n\n/**\n * 数据变更处理函数类型\n */\nexport type ChangeHandler<T = any> = (value: T) => void;\n\n/**\n * 提交处理函数类型\n */\nexport type SubmitHandler<T = any> = (data: T) => Promise<void>;\n\n// ============================================================================\n// 工具类型定义\n// ============================================================================\n\n/**\n * 使所有属性可选的工具类型\n */\nexport type Partial<T> = {\n  [P in keyof T]?: T[P];\n};\n\n/**\n * 使所有属性必需的工具类型\n */\nexport type Required<T> = {\n  [P in keyof T]-?: T[P];\n};\n\n/**\n * 选择指定属性的工具类型\n */\nexport type Pick<T, K extends keyof T> = {\n  [P in K]: T[P];\n};\n\n/**\n * 排除指定属性的工具类型\n */\nexport type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;\n\n/**\n * 键值对类型\n */\nexport type KeyValuePair<K = string, V = any> = {\n  key: K;\n  value: V;\n};\n\n/**\n * 字典类型\n */\nexport type Dictionary<T = any> = Record<string, T>;\n\n/**\n * 可空类型\n */\nexport type Nullable<T> = T | null;\n\n/**\n * 可选类型\n */\nexport type Optional<T> = T | undefined;\n\n/**\n * 可空或可选类型\n */\nexport type Maybe<T> = T | null | undefined;\n\n// ============================================================================\n// 常量类型定义\n// ============================================================================\n\n/**\n * 表名常量类型\n */\nexport type TableName = \n  | 'Competitors'\n  | 'TicketTypes'\n  | 'Promotions'\n  | 'DailySales'\n  | 'DailyUsage';\n\n/**\n * 排序方向类型\n */\nexport type SortDirection = 'ASC' | 'DESC';\n\n/**\n * 数据状态类型\n */\nexport type DataStatus = 'active' | 'inactive' | 'deleted';\n\n/**\n * 操作权限类型\n */\nexport type Permission = 'read' | 'write' | 'delete' | 'admin';\n\n/**\n * 主题类型\n */\nexport type Theme = 'light' | 'dark' | 'system';\n\n/**\n * 语言类型\n */\nexport type Language = 'zh-CN' | 'en-US';\n\n// ============================================================================\n// 组件Props相关类型定义\n// ============================================================================\n\n/**\n * React组件基础Props类型\n */\nexport interface BaseComponentProps {\n  className?: string;        // CSS类名\n  style?: React.CSSProperties; // 内联样式\n  children?: React.ReactNode;  // 子组件\n}\n\n/**\n * 带有加载状态的组件Props类型\n */\nexport interface LoadingComponentProps extends BaseComponentProps {\n  loading?: boolean;         // 是否显示加载状态\n  loadingText?: string;      // 加载提示文本\n}\n\n/**\n * 带有错误状态的组件Props类型\n */\nexport interface ErrorComponentProps extends BaseComponentProps {\n  error?: string | Error;    // 错误信息\n  onRetry?: Callback;        // 重试回调\n}\n\n/**\n * 数据列表组件Props类型\n */\nexport interface DataListProps<T = any> extends BaseComponentProps {\n  data: T[];                 // 数据列表\n  loading?: boolean;         // 是否加载中\n  error?: string;            // 错误信息\n  onRefresh?: AsyncCallback; // 刷新回调\n  onLoadMore?: AsyncCallback; // 加载更多回调\n}\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GAED,+EAA+E;AAC/E,YAAY;AACZ,+EAA+E;;AA+E/E;AA0EA", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/api-utils.ts"], "sourcesContent": ["/**\n * API工具函数\n * \n * 功能说明：\n * 1. 提供统一的API响应格式\n * 2. 实现请求验证和错误处理\n * 3. 提供通用的API辅助函数\n * 4. 支持分页参数解析和验证\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { ApiResponse, ApiError, ApiErrorType, PaginationParams } from '@/types';\n\n// ============================================================================\n// API响应工具函数\n// ============================================================================\n\n/**\n * 创建成功响应\n * @param data 响应数据\n * @param message 响应消息\n * @returns API响应\n */\nexport function createSuccessResponse<T>(\n  data?: T,\n  message?: string\n): NextResponse<ApiResponse<T>> {\n  const response: ApiResponse<T> = {\n    success: true,\n    data,\n    message,\n    timestamp: new Date().toISOString()\n  };\n\n  return NextResponse.json(response);\n}\n\n/**\n * 创建错误响应\n * @param error 错误信息\n * @param status HTTP状态码\n * @returns API响应\n */\nexport function createErrorResponse(\n  error: ApiError | string,\n  status: number = 500\n): NextResponse<ApiResponse> {\n  const apiError: ApiError = typeof error === 'string' \n    ? {\n        type: ApiErrorType.UNKNOWN_ERROR,\n        message: error,\n        timestamp: new Date().toISOString()\n      }\n    : error;\n\n  const response: ApiResponse = {\n    success: false,\n    error: apiError,\n    timestamp: new Date().toISOString()\n  };\n\n  return NextResponse.json(response, { status });\n}\n\n/**\n * 创建验证错误响应\n * @param message 错误消息\n * @param details 错误详情\n * @returns API响应\n */\nexport function createValidationErrorResponse(\n  message: string,\n  details?: any\n): NextResponse<ApiResponse> {\n  const error: ApiError = {\n    type: ApiErrorType.VALIDATION_ERROR,\n    message,\n    details,\n    timestamp: new Date().toISOString()\n  };\n\n  return createErrorResponse(error, 400);\n}\n\n/**\n * 创建未找到错误响应\n * @param resource 资源名称\n * @param id 资源ID\n * @returns API响应\n */\nexport function createNotFoundResponse(\n  resource: string,\n  id?: string | number\n): NextResponse<ApiResponse> {\n  const message = id \n    ? `${resource} with ID ${id} not found`\n    : `${resource} not found`;\n\n  const error: ApiError = {\n    type: ApiErrorType.NOT_FOUND_ERROR,\n    message,\n    timestamp: new Date().toISOString()\n  };\n\n  return createErrorResponse(error, 404);\n}\n\n// ============================================================================\n// 请求参数解析工具函数\n// ============================================================================\n\n/**\n * 解析分页参数\n * @param request 请求对象\n * @returns 分页参数\n */\nexport function parsePaginationParams(request: NextRequest): PaginationParams {\n  const { searchParams } = new URL(request.url);\n\n  const page = Math.max(1, parseInt(searchParams.get('page') || '1'));\n  const pageSize = Math.min(100, Math.max(1, parseInt(searchParams.get('pageSize') || '10')));\n  const sortBy = searchParams.get('sortBy') || undefined;\n  const sortOrder = (searchParams.get('sortOrder')?.toUpperCase() as 'ASC' | 'DESC') || 'ASC';\n\n  return {\n    page,\n    pageSize,\n    sortBy,\n    sortOrder\n  };\n}\n\n/**\n * 解析查询参数为对象\n * @param request 请求对象\n * @param excludeKeys 要排除的键\n * @returns 查询参数对象\n */\nexport function parseQueryParams(\n  request: NextRequest,\n  excludeKeys: string[] = ['page', 'pageSize', 'sortBy', 'sortOrder']\n): Record<string, any> {\n  const { searchParams } = new URL(request.url);\n  const params: Record<string, any> = {};\n\n  for (const [key, value] of searchParams.entries()) {\n    if (!excludeKeys.includes(key) && value) {\n      // 尝试解析数字\n      if (/^\\d+$/.test(value)) {\n        params[key] = parseInt(value);\n      }\n      // 尝试解析布尔值\n      else if (value === 'true' || value === 'false') {\n        params[key] = value === 'true';\n      }\n      // 字符串值\n      else {\n        params[key] = value;\n      }\n    }\n  }\n\n  return params;\n}\n\n/**\n * 解析请求体JSON数据\n * @param request 请求对象\n * @returns JSON数据\n */\nexport async function parseRequestBody<T = any>(request: NextRequest): Promise<T> {\n  try {\n    const body = await request.json();\n    return body as T;\n  } catch (error) {\n    throw new Error('Invalid JSON in request body');\n  }\n}\n\n// ============================================================================\n// 请求验证工具函数\n// ============================================================================\n\n/**\n * 验证必需字段\n * @param data 数据对象\n * @param requiredFields 必需字段列表\n * @returns 验证结果\n */\nexport function validateRequiredFields(\n  data: any,\n  requiredFields: string[]\n): { isValid: boolean; missingFields: string[] } {\n  const missingFields: string[] = [];\n\n  for (const field of requiredFields) {\n    if (data[field] === undefined || data[field] === null || data[field] === '') {\n      missingFields.push(field);\n    }\n  }\n\n  return {\n    isValid: missingFields.length === 0,\n    missingFields\n  };\n}\n\n/**\n * 验证数字范围\n * @param value 数值\n * @param min 最小值\n * @param max 最大值\n * @returns 是否有效\n */\nexport function validateNumberRange(\n  value: number,\n  min?: number,\n  max?: number\n): boolean {\n  if (typeof value !== 'number' || isNaN(value)) {\n    return false;\n  }\n\n  if (min !== undefined && value < min) {\n    return false;\n  }\n\n  if (max !== undefined && value > max) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * 验证字符串长度\n * @param value 字符串值\n * @param minLength 最小长度\n * @param maxLength 最大长度\n * @returns 是否有效\n */\nexport function validateStringLength(\n  value: string,\n  minLength?: number,\n  maxLength?: number\n): boolean {\n  if (typeof value !== 'string') {\n    return false;\n  }\n\n  if (minLength !== undefined && value.length < minLength) {\n    return false;\n  }\n\n  if (maxLength !== undefined && value.length > maxLength) {\n    return false;\n  }\n\n  return true;\n}\n\n/**\n * 验证日期格式\n * @param dateString 日期字符串\n * @returns 是否有效\n */\nexport function validateDateFormat(dateString: string): boolean {\n  if (!dateString) return false;\n  \n  const date = new Date(dateString);\n  return !isNaN(date.getTime());\n}\n\n/**\n * 验证URL格式\n * @param url URL字符串\n * @returns 是否有效\n */\nexport function validateUrlFormat(url: string): boolean {\n  if (!url) return true; // 空URL被认为是有效的（可选字段）\n  \n  try {\n    new URL(url);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\n// ============================================================================\n// 错误处理工具函数\n// ============================================================================\n\n/**\n * 包装API处理函数，提供统一的错误处理\n * @param handler API处理函数\n * @returns 包装后的处理函数\n */\nexport function withErrorHandling(\n  handler: (request: NextRequest, context?: any) => Promise<NextResponse>\n) {\n  return async (request: NextRequest, context?: any): Promise<NextResponse> => {\n    try {\n      return await handler(request, context);\n    } catch (error) {\n      console.error('API处理函数发生错误:', error);\n\n      // 数据库错误\n      if (error instanceof Error && error.message.includes('数据库')) {\n        const apiError: ApiError = {\n          type: ApiErrorType.DATABASE_ERROR,\n          message: '数据库操作失败',\n          details: error.message,\n          timestamp: new Date().toISOString()\n        };\n        return createErrorResponse(apiError, 500);\n      }\n\n      // 验证错误\n      if (error instanceof Error && error.message.includes('验证')) {\n        const apiError: ApiError = {\n          type: ApiErrorType.VALIDATION_ERROR,\n          message: error.message,\n          timestamp: new Date().toISOString()\n        };\n        return createErrorResponse(apiError, 400);\n      }\n\n      // 通用错误\n      const apiError: ApiError = {\n        type: ApiErrorType.SERVER_ERROR,\n        message: error instanceof Error ? error.message : '服务器内部错误',\n        timestamp: new Date().toISOString()\n      };\n\n      return createErrorResponse(apiError, 500);\n    }\n  };\n}\n\n// ============================================================================\n// HTTP方法验证工具函数\n// ============================================================================\n\n/**\n * 验证HTTP方法\n * @param request 请求对象\n * @param allowedMethods 允许的方法列表\n * @returns 验证结果\n */\nexport function validateHttpMethod(\n  request: NextRequest,\n  allowedMethods: string[]\n): boolean {\n  return allowedMethods.includes(request.method);\n}\n\n/**\n * 创建方法不允许的响应\n * @param allowedMethods 允许的方法列表\n * @returns API响应\n */\nexport function createMethodNotAllowedResponse(\n  allowedMethods: string[]\n): NextResponse<ApiResponse> {\n  const error: ApiError = {\n    type: ApiErrorType.VALIDATION_ERROR,\n    message: `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`,\n    timestamp: new Date().toISOString()\n  };\n\n  const response = NextResponse.json({\n    success: false,\n    error,\n    timestamp: new Date().toISOString()\n  }, { status: 405 });\n\n  response.headers.set('Allow', allowedMethods.join(', '));\n  return response;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;;AAED;AACA;AAAA;;;AAYO,SAAS,sBACd,IAAQ,EACR,OAAgB;IAEhB,MAAM,WAA2B;QAC/B,SAAS;QACT;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;AAC3B;AAQO,SAAS,oBACd,KAAwB,EACxB,SAAiB,GAAG;IAEpB,MAAM,WAAqB,OAAO,UAAU,WACxC;QACE,MAAM,qHAAA,CAAA,eAAY,CAAC,aAAa;QAChC,SAAS;QACT,WAAW,IAAI,OAAO,WAAW;IACnC,IACA;IAEJ,MAAM,WAAwB;QAC5B,SAAS;QACT,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;QAAE;IAAO;AAC9C;AAQO,SAAS,8BACd,OAAe,EACf,OAAa;IAEb,MAAM,QAAkB;QACtB,MAAM,qHAAA,CAAA,eAAY,CAAC,gBAAgB;QACnC;QACA;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,OAAO,oBAAoB,OAAO;AACpC;AAQO,SAAS,uBACd,QAAgB,EAChB,EAAoB;IAEpB,MAAM,UAAU,KACZ,GAAG,SAAS,SAAS,EAAE,GAAG,UAAU,CAAC,GACrC,GAAG,SAAS,UAAU,CAAC;IAE3B,MAAM,QAAkB;QACtB,MAAM,qHAAA,CAAA,eAAY,CAAC,eAAe;QAClC;QACA,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,OAAO,oBAAoB,OAAO;AACpC;AAWO,SAAS,sBAAsB,OAAoB;IACxD,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAE5C,MAAM,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,WAAW;IAC9D,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,aAAa,GAAG,CAAC,eAAe;IACpF,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;IAC7C,MAAM,YAAY,AAAC,aAAa,GAAG,CAAC,cAAc,iBAAoC;IAEtF,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAQO,SAAS,iBACd,OAAoB,EACpB,cAAwB;IAAC;IAAQ;IAAY;IAAU;CAAY;IAEnE,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,SAA8B,CAAC;IAErC,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,aAAa,OAAO,GAAI;QACjD,IAAI,CAAC,YAAY,QAAQ,CAAC,QAAQ,OAAO;YACvC,SAAS;YACT,IAAI,QAAQ,IAAI,CAAC,QAAQ;gBACvB,MAAM,CAAC,IAAI,GAAG,SAAS;YACzB,OAEK,IAAI,UAAU,UAAU,UAAU,SAAS;gBAC9C,MAAM,CAAC,IAAI,GAAG,UAAU;YAC1B,OAEK;gBACH,MAAM,CAAC,IAAI,GAAG;YAChB;QACF;IACF;IAEA,OAAO;AACT;AAOO,eAAe,iBAA0B,OAAoB;IAClE,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAYO,SAAS,uBACd,IAAS,EACT,cAAwB;IAExB,MAAM,gBAA0B,EAAE;IAElC,KAAK,MAAM,SAAS,eAAgB;QAClC,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,KAAK,IAAI;YAC3E,cAAc,IAAI,CAAC;QACrB;IACF;IAEA,OAAO;QACL,SAAS,cAAc,MAAM,KAAK;QAClC;IACF;AACF;AASO,SAAS,oBACd,KAAa,EACb,GAAY,EACZ,GAAY;IAEZ,IAAI,OAAO,UAAU,YAAY,MAAM,QAAQ;QAC7C,OAAO;IACT;IAEA,IAAI,QAAQ,aAAa,QAAQ,KAAK;QACpC,OAAO;IACT;IAEA,IAAI,QAAQ,aAAa,QAAQ,KAAK;QACpC,OAAO;IACT;IAEA,OAAO;AACT;AASO,SAAS,qBACd,KAAa,EACb,SAAkB,EAClB,SAAkB;IAElB,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,cAAc,aAAa,MAAM,MAAM,GAAG,WAAW;QACvD,OAAO;IACT;IAEA,IAAI,cAAc,aAAa,MAAM,MAAM,GAAG,WAAW;QACvD,OAAO;IACT;IAEA,OAAO;AACT;AAOO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,CAAC,MAAM,KAAK,OAAO;AAC5B;AAOO,SAAS,kBAAkB,GAAW;IAC3C,IAAI,CAAC,KAAK,OAAO,MAAM,oBAAoB;IAE3C,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAWO,SAAS,kBACd,OAAuE;IAEvE,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,OAAO,MAAM,QAAQ,SAAS;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;YAE9B,QAAQ;YACR,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBAC3D,MAAM,WAAqB;oBACzB,MAAM,qHAAA,CAAA,eAAY,CAAC,cAAc;oBACjC,SAAS;oBACT,SAAS,MAAM,OAAO;oBACtB,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,OAAO,oBAAoB,UAAU;YACvC;YAEA,OAAO;YACP,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,OAAO;gBAC1D,MAAM,WAAqB;oBACzB,MAAM,qHAAA,CAAA,eAAY,CAAC,gBAAgB;oBACnC,SAAS,MAAM,OAAO;oBACtB,WAAW,IAAI,OAAO,WAAW;gBACnC;gBACA,OAAO,oBAAoB,UAAU;YACvC;YAEA,OAAO;YACP,MAAM,WAAqB;gBACzB,MAAM,qHAAA,CAAA,eAAY,CAAC,YAAY;gBAC/B,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA,OAAO,oBAAoB,UAAU;QACvC;IACF;AACF;AAYO,SAAS,mBACd,OAAoB,EACpB,cAAwB;IAExB,OAAO,eAAe,QAAQ,CAAC,QAAQ,MAAM;AAC/C;AAOO,SAAS,+BACd,cAAwB;IAExB,MAAM,QAAkB;QACtB,MAAM,qHAAA,CAAA,eAAY,CAAC,gBAAgB;QACnC,SAAS,CAAC,qCAAqC,EAAE,eAAe,IAAI,CAAC,OAAO;QAC5E,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QACjC,SAAS;QACT;QACA,WAAW,IAAI,OAAO,WAAW;IACnC,GAAG;QAAE,QAAQ;IAAI;IAEjB,SAAS,OAAO,CAAC,GAAG,CAAC,SAAS,eAAe,IAAI,CAAC;IAClD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 2120, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/app/api/promotions/route.ts"], "sourcesContent": ["/**\n * 促销活动API路由\n * \n * 功能说明：\n * 1. 处理促销活动的CRUD操作\n * 2. 支持复杂的分页查询和筛选\n * 3. 提供数据验证和错误处理\n * 4. 实现RESTful API规范\n */\n\nimport { NextRequest } from 'next/server';\nimport { promotionDAO, competitorDAO, ticketTypeDAO } from '@/lib/dao';\nimport {\n  createSuccessResponse,\n  createValidationErrorResponse,\n  parsePaginationParams,\n  parseQueryParams,\n  parseRequestBody,\n  validateRequiredFields,\n  validateStringLength,\n  validateNumberRange,\n  validateDateFormat,\n  validateUrlFormat,\n  withErrorHandling,\n  createMethodNotAllowedResponse\n} from '@/lib/api-utils';\nimport { CreatePromotionInput, PromotionFilters } from '@/types';\n\n/**\n * GET /api/promotions\n * 获取促销活动列表（支持分页和筛选）\n */\nexport const GET = withErrorHandling(async (request: NextRequest) => {\n  console.log('📥 GET /api/promotions - 获取促销活动列表');\n\n  // 解析分页参数\n  const paginationParams = parsePaginationParams(request);\n  console.log('📄 分页参数:', paginationParams);\n\n  // 解析筛选参数\n  const queryParams = parseQueryParams(request);\n  const filters: PromotionFilters = {\n    competitor_id: queryParams.competitor_id,\n    ticket_type_id: queryParams.ticket_type_id,\n    activity_name: queryParams.activity_name,\n    sales_channel: queryParams.sales_channel,\n    sale_date_start: queryParams.sale_date_start,\n    sale_date_end: queryParams.sale_date_end,\n    use_date_start: queryParams.use_date_start,\n    use_date_end: queryParams.use_date_end,\n    min_price: queryParams.min_price,\n    max_price: queryParams.max_price\n  };\n  console.log('🔍 筛选条件:', filters);\n\n  // 查询数据\n  const result = await promotionDAO.findWithFilters(paginationParams, filters);\n\n  console.log(`✅ 成功获取促销活动列表，共${result.total}条记录`);\n  return createSuccessResponse(result, '获取促销活动列表成功');\n});\n\n/**\n * POST /api/promotions\n * 创建新促销活动\n */\nexport const POST = withErrorHandling(async (request: NextRequest) => {\n  console.log('📥 POST /api/promotions - 创建新促销活动');\n\n  // 解析请求体\n  const body = await parseRequestBody<CreatePromotionInput>(request);\n  console.log('📝 创建数据:', body);\n\n  // 验证必需字段\n  const { isValid, missingFields } = validateRequiredFields(body, [\n    'competitor_id',\n    'ticket_type_id',\n    'activity_name'\n  ]);\n  if (!isValid) {\n    console.log('❌ 缺少必需字段:', missingFields);\n    return createValidationErrorResponse(\n      `缺少必需字段: ${missingFields.join(', ')}`,\n      { missingFields }\n    );\n  }\n\n  // 验证外键关联\n  const competitor = await competitorDAO.findById(body.competitor_id);\n  if (!competitor) {\n    return createValidationErrorResponse('指定的竞品不存在');\n  }\n\n  const ticketType = await ticketTypeDAO.findById(body.ticket_type_id);\n  if (!ticketType) {\n    return createValidationErrorResponse('指定的票种不存在');\n  }\n\n  // 验证字段格式和长度\n  if (!validateStringLength(body.activity_name, 1, 255)) {\n    return createValidationErrorResponse('活动名称长度必须在1-255个字符之间');\n  }\n\n  if (body.rack_rate !== undefined && !validateNumberRange(body.rack_rate, 0, 999999.99)) {\n    return createValidationErrorResponse('门市价必须在0-999999.99之间');\n  }\n\n  if (body.promo_price !== undefined && !validateNumberRange(body.promo_price, 0, 999999.99)) {\n    return createValidationErrorResponse('促销价必须在0-999999.99之间');\n  }\n\n  // 验证日期格式\n  if (body.sale_start_date && !validateDateFormat(body.sale_start_date)) {\n    return createValidationErrorResponse('销售开始日期格式无效');\n  }\n\n  if (body.sale_end_date && !validateDateFormat(body.sale_end_date)) {\n    return createValidationErrorResponse('销售结束日期格式无效');\n  }\n\n  if (body.use_start_date && !validateDateFormat(body.use_start_date)) {\n    return createValidationErrorResponse('使用开始日期格式无效');\n  }\n\n  if (body.use_end_date && !validateDateFormat(body.use_end_date)) {\n    return createValidationErrorResponse('使用结束日期格式无效');\n  }\n\n  // 验证日期逻辑\n  if (body.sale_start_date && body.sale_end_date) {\n    if (new Date(body.sale_start_date) > new Date(body.sale_end_date)) {\n      return createValidationErrorResponse('销售开始日期不能晚于结束日期');\n    }\n  }\n\n  if (body.use_start_date && body.use_end_date) {\n    if (new Date(body.use_start_date) > new Date(body.use_end_date)) {\n      return createValidationErrorResponse('使用开始日期不能晚于结束日期');\n    }\n  }\n\n  // 验证其他字段\n  if (body.sales_channel && !validateStringLength(body.sales_channel, 0, 255)) {\n    return createValidationErrorResponse('销售渠道长度不能超过255个字符');\n  }\n\n  if (body.data_source_url && !validateUrlFormat(body.data_source_url)) {\n    return createValidationErrorResponse('数据来源URL格式无效');\n  }\n\n  if (body.data_source_url && !validateStringLength(body.data_source_url, 0, 512)) {\n    return createValidationErrorResponse('数据来源URL长度不能超过512个字符');\n  }\n\n  // 创建促销活动\n  const promotionId = await promotionDAO.create(body);\n\n  // 获取创建的促销活动详情\n  const newPromotion = await promotionDAO.findDetailById(promotionId);\n\n  console.log(`✅ 成功创建促销活动，ID: ${promotionId}`);\n  return createSuccessResponse(newPromotion, '创建促销活动成功');\n});\n\n/**\n * 处理不支持的HTTP方法\n */\nexport async function PUT(request: NextRequest) {\n  return createMethodNotAllowedResponse(['GET', 'POST']);\n}\n\nexport async function DELETE(request: NextRequest) {\n  return createMethodNotAllowedResponse(['GET', 'POST']);\n}\n\nexport async function PATCH(request: NextRequest) {\n  return createMethodNotAllowedResponse(['GET', 'POST']);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAGD;AAAA;AAAA;AAAA;AACA;;;AAoBO,MAAM,MAAM,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IAC1C,QAAQ,GAAG,CAAC;IAEZ,SAAS;IACT,MAAM,mBAAmB,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/C,QAAQ,GAAG,CAAC,YAAY;IAExB,SAAS;IACT,MAAM,cAAc,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAE;IACrC,MAAM,UAA4B;QAChC,eAAe,YAAY,aAAa;QACxC,gBAAgB,YAAY,cAAc;QAC1C,eAAe,YAAY,aAAa;QACxC,eAAe,YAAY,aAAa;QACxC,iBAAiB,YAAY,eAAe;QAC5C,eAAe,YAAY,aAAa;QACxC,gBAAgB,YAAY,cAAc;QAC1C,cAAc,YAAY,YAAY;QACtC,WAAW,YAAY,SAAS;QAChC,WAAW,YAAY,SAAS;IAClC;IACA,QAAQ,GAAG,CAAC,YAAY;IAExB,OAAO;IACP,MAAM,SAAS,MAAM,uIAAA,CAAA,eAAY,CAAC,eAAe,CAAC,kBAAkB;IAEpE,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC;IAC9C,OAAO,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE,QAAQ;AACvC;AAMO,MAAM,OAAO,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IAC3C,QAAQ,GAAG,CAAC;IAEZ,QAAQ;IACR,MAAM,OAAO,MAAM,CAAA,GAAA,4HAAA,CAAA,mBAAgB,AAAD,EAAwB;IAC1D,QAAQ,GAAG,CAAC,YAAY;IAExB,SAAS;IACT,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE,MAAM;QAC9D;QACA;QACA;KACD;IACD,IAAI,CAAC,SAAS;QACZ,QAAQ,GAAG,CAAC,aAAa;QACzB,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EACjC,CAAC,QAAQ,EAAE,cAAc,IAAI,CAAC,OAAO,EACrC;YAAE;QAAc;IAEpB;IAEA,SAAS;IACT,MAAM,aAAa,MAAM,wIAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,KAAK,aAAa;IAClE,IAAI,CAAC,YAAY;QACf,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,MAAM,aAAa,MAAM,4IAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,KAAK,cAAc;IACnE,IAAI,CAAC,YAAY;QACf,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,YAAY;IACZ,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,aAAa,EAAE,GAAG,MAAM;QACrD,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,IAAI,KAAK,SAAS,KAAK,aAAa,CAAC,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,SAAS,EAAE,GAAG,YAAY;QACtF,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,IAAI,KAAK,WAAW,KAAK,aAAa,CAAC,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,WAAW,EAAE,GAAG,YAAY;QAC1F,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,SAAS;IACT,IAAI,KAAK,eAAe,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,eAAe,GAAG;QACrE,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,IAAI,KAAK,aAAa,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,aAAa,GAAG;QACjE,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,IAAI,KAAK,cAAc,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,cAAc,GAAG;QACnE,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,IAAI,KAAK,YAAY,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,YAAY,GAAG;QAC/D,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,SAAS;IACT,IAAI,KAAK,eAAe,IAAI,KAAK,aAAa,EAAE;QAC9C,IAAI,IAAI,KAAK,KAAK,eAAe,IAAI,IAAI,KAAK,KAAK,aAAa,GAAG;YACjE,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;QACvC;IACF;IAEA,IAAI,KAAK,cAAc,IAAI,KAAK,YAAY,EAAE;QAC5C,IAAI,IAAI,KAAK,KAAK,cAAc,IAAI,IAAI,KAAK,KAAK,YAAY,GAAG;YAC/D,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;QACvC;IACF;IAEA,SAAS;IACT,IAAI,KAAK,aAAa,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,aAAa,EAAE,GAAG,MAAM;QAC3E,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,IAAI,KAAK,eAAe,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,eAAe,GAAG;QACpE,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,IAAI,KAAK,eAAe,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,eAAe,EAAE,GAAG,MAAM;QAC/E,OAAO,CAAA,GAAA,4HAAA,CAAA,gCAA6B,AAAD,EAAE;IACvC;IAEA,SAAS;IACT,MAAM,cAAc,MAAM,uIAAA,CAAA,eAAY,CAAC,MAAM,CAAC;IAE9C,cAAc;IACd,MAAM,eAAe,MAAM,uIAAA,CAAA,eAAY,CAAC,cAAc,CAAC;IAEvD,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa;IAC3C,OAAO,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE,cAAc;AAC7C;AAKO,eAAe,IAAI,OAAoB;IAC5C,OAAO,CAAA,GAAA,4HAAA,CAAA,iCAA8B,AAAD,EAAE;QAAC;QAAO;KAAO;AACvD;AAEO,eAAe,OAAO,OAAoB;IAC/C,OAAO,CAAA,GAAA,4HAAA,CAAA,iCAA8B,AAAD,EAAE;QAAC;QAAO;KAAO;AACvD;AAEO,eAAe,MAAM,OAAoB;IAC9C,OAAO,CAAA,GAAA,4HAAA,CAAA,iCAA8B,AAAD,EAAE;QAAC;QAAO;KAAO;AACvD", "debugId": null}}]}