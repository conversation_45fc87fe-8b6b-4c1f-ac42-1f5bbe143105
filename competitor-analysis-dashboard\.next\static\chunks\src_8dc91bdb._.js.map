{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/layout/mobile-nav.tsx"], "sourcesContent": ["/**\n * 移动端导航组件\n * \n * 功能说明：\n * 1. 专为移动端设计的导航菜单\n * 2. 底部标签栏导航\n * 3. 手势友好的交互设计\n * 4. 适配不同屏幕尺寸\n */\n\n'use client';\n\nimport React from 'react';\nimport { Home, Users, Ticket, Database, BarChart3, Settings } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\n// ============================================================================\n// 导航项配置\n// ============================================================================\n\ninterface NavItem {\n  key: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  href: string;\n  badge?: number;\n}\n\nconst navItems: NavItem[] = [\n  {\n    key: 'dashboard',\n    label: '首页',\n    icon: Home,\n    href: '/'\n  },\n  {\n    key: 'competitors',\n    label: '竞品',\n    icon: Users,\n    href: '/competitors'\n  },\n  {\n    key: 'ticket-types',\n    label: '票种',\n    icon: Ticket,\n    href: '/ticket-types'\n  },\n  {\n    key: 'promotions',\n    label: '促销',\n    icon: Database,\n    href: '/promotions'\n  },\n  {\n    key: 'analytics',\n    label: '分析',\n    icon: BarChart3,\n    href: '/analytics'\n  }\n];\n\n// ============================================================================\n// 移动端底部导航组件\n// ============================================================================\n\nexport interface MobileBottomNavProps {\n  currentPath?: string;\n  className?: string;\n}\n\nexport const MobileBottomNav: React.FC<MobileBottomNavProps> = ({\n  currentPath = '/',\n  className\n}) => {\n  return (\n    <nav className={cn(\n      'fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 md:hidden',\n      'safe-area-pb', // 适配iPhone底部安全区域\n      className\n    )}>\n      <div className=\"flex items-center justify-around px-2 py-1\">\n        {navItems.map((item) => {\n          const isActive = currentPath === item.href || \n            (item.href !== '/' && currentPath.startsWith(item.href));\n          const Icon = item.icon;\n\n          return (\n            <a\n              key={item.key}\n              href={item.href}\n              className={cn(\n                'flex flex-col items-center justify-center min-w-0 flex-1 px-1 py-2 text-xs transition-colors',\n                'active:bg-gray-100 rounded-lg',\n                isActive\n                  ? 'text-blue-600'\n                  : 'text-gray-600 hover:text-gray-900'\n              )}\n            >\n              <div className=\"relative\">\n                <Icon className={cn(\n                  'h-5 w-5 mb-1',\n                  isActive ? 'text-blue-600' : 'text-gray-400'\n                )} />\n                {item.badge && item.badge > 0 && (\n                  <span className=\"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 99 ? '99+' : item.badge}\n                  </span>\n                )}\n              </div>\n              <span className={cn(\n                'truncate max-w-full',\n                isActive ? 'font-medium' : 'font-normal'\n              )}>\n                {item.label}\n              </span>\n            </a>\n          );\n        })}\n      </div>\n    </nav>\n  );\n};\n\n// ============================================================================\n// 移动端侧边抽屉导航组件\n// ============================================================================\n\nexport interface MobileDrawerNavProps {\n  open: boolean;\n  onClose: () => void;\n  currentPath?: string;\n  className?: string;\n}\n\nexport const MobileDrawerNav: React.FC<MobileDrawerNavProps> = ({\n  open,\n  onClose,\n  currentPath = '/',\n  className\n}) => {\n  return (\n    <>\n      {/* 遮罩层 */}\n      {open && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* 抽屉内容 */}\n      <div\n        className={cn(\n          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out md:hidden',\n          open ? 'translate-x-0' : '-translate-x-full',\n          className\n        )}\n      >\n        {/* 抽屉头部 */}\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">\n            竞品分析系统\n          </h2>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n          >\n            <span className=\"sr-only\">关闭菜单</span>\n            <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n          {navItems.map((item) => {\n            const isActive = currentPath === item.href || \n              (item.href !== '/' && currentPath.startsWith(item.href));\n            const Icon = item.icon;\n\n            return (\n              <a\n                key={item.key}\n                href={item.href}\n                onClick={onClose}\n                className={cn(\n                  'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',\n                  isActive\n                    ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n                    : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                )}\n              >\n                <Icon className=\"mr-3 h-5 w-5\" />\n                <span className=\"flex-1\">{item.label}</span>\n                {item.badge && item.badge > 0 && (\n                  <span className=\"ml-2 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 99 ? '99+' : item.badge}\n                  </span>\n                )}\n              </a>\n            );\n          })}\n        </nav>\n\n        {/* 抽屉底部 */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"text-xs text-gray-500 text-center\">\n            版本 1.0.0\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\n// ============================================================================\n// 移动端顶部导航栏组件\n// ============================================================================\n\nexport interface MobileTopNavProps {\n  title?: string;\n  onMenuClick?: () => void;\n  actions?: React.ReactNode;\n  className?: string;\n}\n\nexport const MobileTopNav: React.FC<MobileTopNavProps> = ({\n  title = '竞品分析系统',\n  onMenuClick,\n  actions,\n  className\n}) => {\n  return (\n    <header className={cn(\n      'sticky top-0 z-30 bg-white border-b border-gray-200 md:hidden',\n      className\n    )}>\n      <div className=\"flex items-center justify-between h-14 px-4\">\n        {/* 左侧菜单按钮 */}\n        <button\n          onClick={onMenuClick}\n          className=\"p-2 -ml-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100\"\n        >\n          <span className=\"sr-only\">打开菜单</span>\n          <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n          </svg>\n        </button>\n\n        {/* 中间标题 */}\n        <h1 className=\"text-lg font-semibold text-gray-900 truncate flex-1 mx-4 text-center\">\n          {title}\n        </h1>\n\n        {/* 右侧操作按钮 */}\n        <div className=\"flex items-center space-x-2\">\n          {actions}\n        </div>\n      </div>\n    </header>\n  );\n};\n\n// ============================================================================\n// 移动端浮动操作按钮组件\n// ============================================================================\n\nexport interface MobileFABProps {\n  onClick?: () => void;\n  icon?: React.ComponentType<{ className?: string }>;\n  label?: string;\n  className?: string;\n}\n\nexport const MobileFAB: React.FC<MobileFABProps> = ({\n  onClick,\n  icon: Icon = Database,\n  label = '添加',\n  className\n}) => {\n  return (\n    <button\n      onClick={onClick}\n      className={cn(\n        'fixed bottom-20 right-4 z-40 md:hidden',\n        'h-14 w-14 bg-blue-600 text-white rounded-full shadow-lg',\n        'flex items-center justify-center',\n        'hover:bg-blue-700 active:bg-blue-800',\n        'transition-colors duration-200',\n        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',\n        className\n      )}\n    >\n      <Icon className=\"h-6 w-6\" />\n      <span className=\"sr-only\">{label}</span>\n    </button>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  MobileBottomNav as default,\n  MobileDrawerNav,\n  MobileTopNav,\n  MobileFAB\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;AAKD;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;AAkBA,MAAM,WAAsB;IAC1B;QACE,KAAK;QACL,OAAO;QACP,MAAM,sMAAA,CAAA,OAAI;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,yMAAA,CAAA,SAAM;QACZ,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,qNAAA,CAAA,YAAS;QACf,MAAM;IACR;CACD;AAWM,MAAM,kBAAkD;QAAC,EAC9D,cAAc,GAAG,EACjB,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,kFACA,gBACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC;gBACb,MAAM,WAAW,gBAAgB,KAAK,IAAI,IACvC,KAAK,IAAI,KAAK,OAAO,YAAY,UAAU,CAAC,KAAK,IAAI;gBACxD,MAAM,OAAO,KAAK,IAAI;gBAEtB,qBACE,6LAAC;oBAEC,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,iCACA,WACI,kBACA;;sCAGN,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,gBACA,WAAW,kBAAkB;;;;;;gCAE9B,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,6LAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;;;;;;sCAI3C,6LAAC;4BAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,uBACA,WAAW,gBAAgB;sCAE1B,KAAK,KAAK;;;;;;;mBAzBR,KAAK,GAAG;;;;;YA6BnB;;;;;;;;;;;AAIR;KAnDa;AAgEN,MAAM,kBAAkD;QAAC,EAC9D,IAAI,EACJ,OAAO,EACP,cAAc,GAAG,EACjB,SAAS,EACV;IACC,qBACE;;YAEG,sBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yHACA,OAAO,kBAAkB,qBACzB;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAC9D,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,WAAW,gBAAgB,KAAK,IAAI,IACvC,KAAK,IAAI,KAAK,OAAO,YAAY,UAAU,CAAC,KAAK,IAAI;4BACxD,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,6LAAC;gCAEC,MAAM,KAAK,IAAI;gCACf,SAAS;gCACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,yDACA;;kDAGN,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU,KAAK,KAAK;;;;;;oCACnC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;+BAdpC,KAAK,GAAG;;;;;wBAmBnB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;AAO7D;MAhFa;AA6FN,MAAM,eAA4C;QAAC,EACxD,QAAQ,QAAQ,EAChB,WAAW,EACX,OAAO,EACP,SAAS,EACV;IACC,qBACE,6LAAC;QAAO,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAClB,iEACA;kBAEA,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,6LAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,6LAAC;4BAAI,WAAU;4BAAU,MAAK;4BAAO,SAAQ;4BAAY,QAAO;sCAC9D,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;8BAKzE,6LAAC;oBAAG,WAAU;8BACX;;;;;;8BAIH,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;MAnCa;AAgDN,MAAM,YAAsC;QAAC,EAClD,OAAO,EACP,MAAM,OAAO,6MAAA,CAAA,WAAQ,EACrB,QAAQ,IAAI,EACZ,SAAS,EACV;IACC,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,2DACA,oCACA,wCACA,kCACA,2EACA;;0BAGF,6LAAC;gBAAK,WAAU;;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAW;;;;;;;;;;;;AAGjC;MAvBa", "debugId": null}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/layout/main-layout.tsx"], "sourcesContent": ["/**\n * 主布局组件\n * \n * 功能说明：\n * 1. 提供应用程序的主要布局结构\n * 2. 包含导航栏、侧边栏、主内容区域\n * 3. 响应式设计，支持移动端\n * 4. 可配置的布局选项\n */\n\nimport React, { useState } from 'react';\nimport { Menu, X, Home, Database, BarChart3, Settings, Users, Ticket } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/button';\nimport { MobileBottomNav, MobileDrawerNav, MobileTopNav } from './mobile-nav';\n\n// ============================================================================\n// 导航菜单配置\n// ============================================================================\n\nexport interface MenuItem {\n  key: string;\n  label: string;\n  icon: React.ComponentType<{ className?: string }>;\n  href: string;\n  children?: MenuItem[];\n}\n\nconst defaultMenuItems: MenuItem[] = [\n  {\n    key: 'dashboard',\n    label: '仪表板',\n    icon: Home,\n    href: '/'\n  },\n  {\n    key: 'competitors',\n    label: '竞品管理',\n    icon: Users,\n    href: '/competitors'\n  },\n  {\n    key: 'ticket-types',\n    label: '票种管理',\n    icon: Ticket,\n    href: '/ticket-types'\n  },\n  {\n    key: 'promotions',\n    label: '促销活动',\n    icon: Database,\n    href: '/promotions'\n  },\n  {\n    key: 'analytics',\n    label: '数据分析',\n    icon: BarChart3,\n    href: '/analytics'\n  },\n  {\n    key: 'settings',\n    label: '系统设置',\n    icon: Settings,\n    href: '/settings'\n  }\n];\n\n// ============================================================================\n// 布局组件接口\n// ============================================================================\n\nexport interface MainLayoutProps {\n  children: React.ReactNode;\n  menuItems?: MenuItem[];\n  currentPath?: string;\n  title?: string;\n  className?: string;\n}\n\n// ============================================================================\n// 主布局组件实现\n// ============================================================================\n\nexport const MainLayout: React.FC<MainLayoutProps> = ({\n  children,\n  menuItems = defaultMenuItems,\n  currentPath = '/',\n  title = '竞品分析管理系统',\n  className\n}) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  // 渲染菜单项\n  const renderMenuItem = (item: MenuItem) => {\n    const isActive = currentPath === item.href || currentPath.startsWith(item.href + '/');\n    const Icon = item.icon;\n\n    return (\n      <a\n        key={item.key}\n        href={item.href}\n        className={cn(\n          'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',\n          isActive\n            ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700'\n            : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n        )}\n        onClick={() => setSidebarOpen(false)}\n      >\n        <Icon className=\"mr-3 h-5 w-5\" />\n        {item.label}\n      </a>\n    );\n  };\n\n  return (\n    <div className={cn('min-h-screen bg-gray-50', className)}>\n      {/* 移动端导航组件 */}\n      <MobileDrawerNav\n        open={sidebarOpen}\n        onClose={() => setSidebarOpen(false)}\n        currentPath={currentPath}\n      />\n\n      {/* 桌面端侧边栏 */}\n      <div\n        className={cn(\n          'hidden lg:fixed lg:inset-y-0 lg:left-0 lg:z-50 lg:w-64 lg:bg-white lg:shadow-lg lg:block'\n        )}\n      >\n        {/* 侧边栏头部 */}\n        <div className=\"flex items-center justify-between h-16 px-4 border-b border-gray-200\">\n          <h1 className=\"text-lg font-semibold text-gray-900 truncate\">\n            {title}\n          </h1>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          >\n            <X className=\"h-5 w-5\" />\n          </Button>\n        </div>\n\n        {/* 导航菜单 */}\n        <nav className=\"flex-1 px-4 py-6 space-y-2 overflow-y-auto\">\n          {menuItems.map(renderMenuItem)}\n        </nav>\n\n        {/* 侧边栏底部 */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <div className=\"text-xs text-gray-500 text-center\">\n            版本 1.0.0\n          </div>\n        </div>\n      </div>\n\n      {/* 主内容区域 */}\n      <div className=\"lg:pl-64\">\n        {/* 移动端顶部导航 */}\n        <MobileTopNav\n          title={menuItems.find(item => item.href === currentPath)?.label || '首页'}\n          onMenuClick={() => setSidebarOpen(true)}\n        />\n\n        {/* 桌面端顶部导航栏 */}\n        <header className=\"hidden lg:block bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\n\n            {/* 页面标题 */}\n            <div className=\"flex-1 lg:flex-none\">\n              <h2 className=\"text-xl font-semibold text-gray-900\">\n                {/* 这里可以根据当前路径动态显示页面标题 */}\n                {menuItems.find(item => item.href === currentPath)?.label || '首页'}\n              </h2>\n            </div>\n\n            {/* 用户操作区域 */}\n            <div className=\"flex items-center space-x-4\">\n              {/* 通知按钮 */}\n              <Button variant=\"ghost\" size=\"sm\">\n                <span className=\"sr-only\">通知</span>\n                <div className=\"h-5 w-5 rounded-full bg-gray-300\" />\n              </Button>\n\n              {/* 用户头像 */}\n              <Button variant=\"ghost\" size=\"sm\">\n                <span className=\"sr-only\">用户菜单</span>\n                <div className=\"h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium\">\n                  U\n                </div>\n              </Button>\n            </div>\n          </div>\n        </header>\n\n        {/* 主内容 */}\n        <main className=\"flex-1 pb-16 lg:pb-0\">\n          <div className=\"p-4 sm:p-6 lg:p-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n\n      {/* 移动端底部导航 */}\n      <MobileBottomNav currentPath={currentPath} />\n    </div>\n  );\n};\n\n// ============================================================================\n// 页面容器组件\n// ============================================================================\n\nexport interface PageContainerProps {\n  title?: string;\n  description?: string;\n  actions?: React.ReactNode;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const PageContainer: React.FC<PageContainerProps> = ({\n  title,\n  description,\n  actions,\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('space-y-6', className)}>\n      {/* 页面头部 */}\n      {(title || description || actions) && (\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div>\n            {title && (\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                {title}\n              </h1>\n            )}\n            {description && (\n              <p className=\"mt-1 text-sm text-gray-600\">\n                {description}\n              </p>\n            )}\n          </div>\n          {actions && (\n            <div className=\"flex gap-2\">\n              {actions}\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* 页面内容 */}\n      <div>\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 卡片容器组件\n// ============================================================================\n\nexport interface CardContainerProps {\n  title?: string;\n  description?: string;\n  actions?: React.ReactNode;\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const CardContainer: React.FC<CardContainerProps> = ({\n  title,\n  description,\n  actions,\n  children,\n  className\n}) => {\n  return (\n    <div className={cn('bg-white rounded-lg shadow border border-gray-200', className)}>\n      {/* 卡片头部 */}\n      {(title || description || actions) && (\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              {title && (\n                <h3 className=\"text-lg font-medium text-gray-900\">\n                  {title}\n                </h3>\n              )}\n              {description && (\n                <p className=\"mt-1 text-sm text-gray-600\">\n                  {description}\n                </p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex gap-2\">\n                {actions}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* 卡片内容 */}\n      <div className=\"p-6\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  MainLayout as default,\n  PageContainer,\n  CardContainer\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AAcA,MAAM,mBAA+B;IACnC;QACE,KAAK;QACL,OAAO;QACP,MAAM,sMAAA,CAAA,OAAI;QACV,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;QACX,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,yMAAA,CAAA,SAAM;QACZ,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,qNAAA,CAAA,YAAS;QACf,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM,6MAAA,CAAA,WAAQ;QACd,MAAM;IACR;CACD;AAkBM,MAAM,aAAwC;QAAC,EACpD,QAAQ,EACR,YAAY,gBAAgB,EAC5B,cAAc,GAAG,EACjB,QAAQ,UAAU,EAClB,SAAS,EACV;QAyEgB,iBAYA;;IApFf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,QAAQ;IACR,MAAM,iBAAiB,CAAC;QACtB,MAAM,WAAW,gBAAgB,KAAK,IAAI,IAAI,YAAY,UAAU,CAAC,KAAK,IAAI,GAAG;QACjF,MAAM,OAAO,KAAK,IAAI;QAEtB,qBACE,6LAAC;YAEC,MAAM,KAAK,IAAI;YACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gFACA,WACI,yDACA;YAEN,SAAS,IAAM,eAAe;;8BAE9B,6LAAC;oBAAK,WAAU;;;;;;gBACf,KAAK,KAAK;;WAXN,KAAK,GAAG;;;;;IAcnB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAE5C,6LAAC,gJAAA,CAAA,kBAAe;gBACd,MAAM;gBACN,SAAS,IAAM,eAAe;gBAC9B,aAAa;;;;;;0BAIf,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;0CAEH,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAoC;;;;;;;;;;;;;;;;;0BAOvD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,gJAAA,CAAA,eAAY;wBACX,OAAO,EAAA,kBAAA,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,0BAArC,sCAAA,gBAAmD,KAAK,KAAI;wBACnE,aAAa,IAAM,eAAe;;;;;;kCAIpC,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CAGb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAEX,EAAA,mBAAA,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,0BAArC,uCAAA,iBAAmD,KAAK,KAAI;;;;;;;;;;;8CAKjE,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DAAmG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS1H,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAMP,6LAAC,gJAAA,CAAA,kBAAe;gBAAC,aAAa;;;;;;;;;;;;AAGpC;GA9Ha;KAAA;AA4IN,MAAM,gBAA8C;QAAC,EAC1D,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;YAE7B,CAAC,SAAS,eAAe,OAAO,mBAC/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BACE,uBACC,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAGJ,6BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;oBAIN,yBACC,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;0BAOT,6LAAC;0BACE;;;;;;;;;;;;AAIT;MAtCa;AAoDN,MAAM,gBAA8C;QAAC,EAC1D,KAAK,EACL,WAAW,EACX,OAAO,EACP,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;;YAErE,CAAC,SAAS,eAAe,OAAO,mBAC/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;gCACE,uBACC,6LAAC;oCAAG,WAAU;8CACX;;;;;;gCAGJ,6BACC,6LAAC;oCAAE,WAAU;8CACV;;;;;;;;;;;;wBAIN,yBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;0BAQX,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;MAxCa", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/loading.tsx"], "sourcesContent": ["/**\n * 加载组件\n * \n * 功能说明：\n * 1. 提供多种加载状态的显示\n * 2. 支持不同尺寸和样式\n * 3. 可配置加载文本和动画\n */\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\n// ============================================================================\n// 加载动画组件\n// ============================================================================\n\n/**\n * 旋转加载器组件\n */\nexport interface SpinnerProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const Spinner: React.FC<SpinnerProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        sizeClasses[size],\n        className\n      )}\n    />\n  );\n};\n\n/**\n * 脉冲加载器组件\n */\nexport interface PulseProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n}\n\nexport const Pulse: React.FC<PulseProps> = ({ \n  size = 'md', \n  className \n}) => {\n  const sizeClasses = {\n    sm: 'w-2 h-2',\n    md: 'w-3 h-3',\n    lg: 'w-4 h-4'\n  };\n\n  return (\n    <div className={cn('flex space-x-1', className)}>\n      {[0, 1, 2].map((i) => (\n        <div\n          key={i}\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n          style={{\n            animationDelay: `${i * 0.2}s`,\n            animationDuration: '1s'\n          }}\n        />\n      ))}\n    </div>\n  );\n};\n\n// ============================================================================\n// 加载状态组件\n// ============================================================================\n\n/**\n * 内联加载组件\n */\nexport interface LoadingInlineProps {\n  text?: string;\n  size?: 'sm' | 'md' | 'lg';\n  type?: 'spinner' | 'pulse';\n  className?: string;\n}\n\nexport const LoadingInline: React.FC<LoadingInlineProps> = ({\n  text = '加载中...',\n  size = 'md',\n  type = 'spinner',\n  className\n}) => {\n  const LoaderComponent = type === 'spinner' ? Spinner : Pulse;\n\n  return (\n    <div className={cn('flex items-center space-x-2', className)}>\n      <LoaderComponent size={size} />\n      {text && (\n        <span className=\"text-sm text-gray-600 animate-pulse\">\n          {text}\n        </span>\n      )}\n    </div>\n  );\n};\n\n/**\n * 页面加载组件\n */\nexport interface LoadingPageProps {\n  text?: string;\n  description?: string;\n  className?: string;\n}\n\nexport const LoadingPage: React.FC<LoadingPageProps> = ({\n  text = '加载中...',\n  description,\n  className\n}) => {\n  return (\n    <div className={cn(\n      'flex flex-col items-center justify-center min-h-[400px] space-y-4',\n      className\n    )}>\n      <Spinner size=\"lg\" />\n      <div className=\"text-center space-y-2\">\n        <h3 className=\"text-lg font-medium text-gray-900\">\n          {text}\n        </h3>\n        {description && (\n          <p className=\"text-sm text-gray-500 max-w-sm\">\n            {description}\n          </p>\n        )}\n      </div>\n    </div>\n  );\n};\n\n/**\n * 覆盖层加载组件\n */\nexport interface LoadingOverlayProps {\n  visible: boolean;\n  text?: string;\n  className?: string;\n  children?: React.ReactNode;\n}\n\nexport const LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  visible,\n  text = '处理中...',\n  className,\n  children\n}) => {\n  if (!visible) {\n    return <>{children}</>;\n  }\n\n  return (\n    <div className={cn('relative', className)}>\n      {children}\n      <div className=\"absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-50\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6 flex flex-col items-center space-y-4\">\n          <Spinner size=\"lg\" />\n          <p className=\"text-sm font-medium text-gray-900\">\n            {text}\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n/**\n * 表格行加载组件\n */\nexport interface LoadingTableRowProps {\n  columns: number;\n  rows?: number;\n  className?: string;\n}\n\nexport const LoadingTableRow: React.FC<LoadingTableRowProps> = ({\n  columns,\n  rows = 5,\n  className\n}) => {\n  return (\n    <>\n      {Array.from({ length: rows }).map((_, rowIndex) => (\n        <tr key={rowIndex} className={className}>\n          {Array.from({ length: columns }).map((_, colIndex) => (\n            <td key={colIndex} className=\"px-4 py-3\">\n              <div className=\"h-4 bg-gray-200 rounded animate-pulse\" />\n            </td>\n          ))}\n        </tr>\n      ))}\n    </>\n  );\n};\n\n/**\n * 卡片加载组件\n */\nexport interface LoadingCardProps {\n  className?: string;\n}\n\nexport const LoadingCard: React.FC<LoadingCardProps> = ({ className }) => {\n  return (\n    <div className={cn(\n      'border rounded-lg p-6 space-y-4 animate-pulse',\n      className\n    )}>\n      <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n      <div className=\"space-y-2\">\n        <div className=\"h-3 bg-gray-200 rounded\" />\n        <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n      </div>\n      <div className=\"flex space-x-2\">\n        <div className=\"h-6 bg-gray-200 rounded w-16\" />\n        <div className=\"h-6 bg-gray-200 rounded w-20\" />\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 按钮加载状态组件\n// ============================================================================\n\n/**\n * 带加载状态的按钮组件\n */\nexport interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  loading?: boolean;\n  loadingText?: string;\n  children: React.ReactNode;\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n}\n\nexport const LoadingButton: React.FC<LoadingButtonProps> = ({\n  loading = false,\n  loadingText,\n  children,\n  disabled,\n  className,\n  ...props\n}) => {\n  return (\n    <button\n      {...props}\n      disabled={disabled || loading}\n      className={cn(\n        'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',\n        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n        'disabled:pointer-events-none disabled:opacity-50',\n        'bg-primary text-primary-foreground hover:bg-primary/90',\n        'h-10 px-4 py-2',\n        className\n      )}\n    >\n      {loading && (\n        <Spinner size=\"sm\" className=\"mr-2\" />\n      )}\n      {loading && loadingText ? loadingText : children}\n    </button>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  Spinner,\n  Pulse,\n  LoadingInline,\n  LoadingPage,\n  LoadingOverlay,\n  LoadingTableRow,\n  LoadingCard,\n  LoadingButton\n};\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;AAGD;;;AAcO,MAAM,UAAkC;QAAC,EAC9C,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAnBa;AA6BN,MAAM,QAA8B;QAAC,EAC1C,OAAO,IAAI,EACX,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;YAAC;YAAG;YAAG;SAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;gBAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;gBAEnB,OAAO;oBACL,gBAAgB,AAAC,GAAU,OAAR,IAAI,KAAI;oBAC3B,mBAAmB;gBACrB;eARK;;;;;;;;;;AAaf;MA3Ba;AA2CN,MAAM,gBAA8C;QAAC,EAC1D,OAAO,QAAQ,EACf,OAAO,IAAI,EACX,OAAO,SAAS,EAChB,SAAS,EACV;IACC,MAAM,kBAAkB,SAAS,YAAY,UAAU;IAEvD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;0BAChD,6LAAC;gBAAgB,MAAM;;;;;;YACtB,sBACC,6LAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAKX;MAlBa;AA6BN,MAAM,cAA0C;QAAC,EACtD,OAAO,QAAQ,EACf,WAAW,EACX,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qEACA;;0BAEA,6LAAC;gBAAQ,MAAK;;;;;;0BACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCACX;;;;;;oBAEF,6BACC,6LAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;;;;;;;AAMb;MAvBa;AAmCN,MAAM,iBAAgD;QAAC,EAC5D,OAAO,EACP,OAAO,QAAQ,EACf,SAAS,EACT,QAAQ,EACT;IACC,IAAI,CAAC,SAAS;QACZ,qBAAO;sBAAG;;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B;0BACD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAQ,MAAK;;;;;;sCACd,6LAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAMb;MAvBa;AAkCN,MAAM,kBAAkD;QAAC,EAC9D,OAAO,EACP,OAAO,CAAC,EACR,SAAS,EACV;IACC,qBACE;kBACG,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAK,GAAG,GAAG,CAAC,CAAC,GAAG,yBACpC,6LAAC;gBAAkB,WAAW;0BAC3B,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACvC,6LAAC;wBAAkB,WAAU;kCAC3B,cAAA,6LAAC;4BAAI,WAAU;;;;;;uBADR;;;;;eAFJ;;;;;;AAUjB;MAlBa;AA2BN,MAAM,cAA0C;QAAC,EAAE,SAAS,EAAE;IACnE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,iDACA;;0BAEA,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;MAjBa;AAkCN,MAAM,gBAA8C;QAAC,EAC1D,UAAU,KAAK,EACf,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC;QACE,GAAG,KAAK;QACT,UAAU,YAAY;QACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA,uGACA,oDACA,0DACA,kBACA;;YAGD,yBACC,6LAAC;gBAAQ,MAAK;gBAAK,WAAU;;;;;;YAE9B,WAAW,cAAc,cAAc;;;;;;;AAG9C;MA3Ba", "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1611, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/pagination.tsx"], "sourcesContent": ["/**\n * 分页组件\n * \n * 功能说明：\n * 1. 提供完整的分页功能\n * 2. 支持页码跳转和页面大小选择\n * 3. 响应式设计，适配移动端\n * 4. 可配置显示样式和行为\n */\n\nimport React from 'react';\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './button';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';\n\n// ============================================================================\n// 分页组件接口定义\n// ============================================================================\n\nexport interface PaginationProps {\n  currentPage: number;          // 当前页码\n  totalPages: number;           // 总页数\n  totalItems: number;           // 总记录数\n  pageSize: number;             // 每页记录数\n  onPageChange: (page: number) => void;        // 页码变更回调\n  onPageSizeChange?: (pageSize: number) => void; // 页面大小变更回调\n  showPageSizeSelector?: boolean;               // 是否显示页面大小选择器\n  pageSizeOptions?: number[];                   // 页面大小选项\n  showTotal?: boolean;                          // 是否显示总数信息\n  showQuickJumper?: boolean;                    // 是否显示快速跳转\n  className?: string;\n}\n\n// ============================================================================\n// 分页组件实现\n// ============================================================================\n\nexport const Pagination: React.FC<PaginationProps> = ({\n  currentPage,\n  totalPages,\n  totalItems,\n  pageSize,\n  onPageChange,\n  onPageSizeChange,\n  showPageSizeSelector = true,\n  pageSizeOptions = [10, 20, 50, 100],\n  showTotal = true,\n  showQuickJumper = false,\n  className\n}) => {\n  // 计算显示的页码范围\n  const getVisiblePages = (): (number | 'ellipsis')[] => {\n    const delta = 2; // 当前页前后显示的页数\n    const range: (number | 'ellipsis')[] = [];\n    const rangeWithDots: (number | 'ellipsis')[] = [];\n\n    // 总是显示第一页\n    range.push(1);\n\n    // 计算当前页周围的页码\n    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {\n      range.push(i);\n    }\n\n    // 总是显示最后一页（如果总页数大于1）\n    if (totalPages > 1) {\n      range.push(totalPages);\n    }\n\n    // 添加省略号\n    let prev = 0;\n    for (const page of range) {\n      if (typeof page === 'number') {\n        if (page - prev === 2) {\n          rangeWithDots.push(prev + 1);\n        } else if (page - prev !== 1) {\n          rangeWithDots.push('ellipsis');\n        }\n        rangeWithDots.push(page);\n        prev = page;\n      }\n    }\n\n    return rangeWithDots;\n  };\n\n  const visiblePages = getVisiblePages();\n\n  // 计算当前页显示的记录范围\n  const startItem = (currentPage - 1) * pageSize + 1;\n  const endItem = Math.min(currentPage * pageSize, totalItems);\n\n  // 快速跳转处理\n  const handleQuickJump = (event: React.KeyboardEvent<HTMLInputElement>) => {\n    if (event.key === 'Enter') {\n      const target = event.target as HTMLInputElement;\n      const page = parseInt(target.value);\n      if (page >= 1 && page <= totalPages) {\n        onPageChange(page);\n        target.value = '';\n      }\n    }\n  };\n\n  return (\n    <div className={cn('flex flex-col sm:flex-row items-center justify-between gap-4', className)}>\n      {/* 总数信息 */}\n      {showTotal && (\n        <div className=\"text-sm text-gray-700 order-2 sm:order-1\">\n          显示第 <span className=\"font-medium\">{startItem}</span> 到{' '}\n          <span className=\"font-medium\">{endItem}</span> 条，共{' '}\n          <span className=\"font-medium\">{totalItems}</span> 条记录\n        </div>\n      )}\n\n      {/* 分页控件 */}\n      <div className=\"flex items-center gap-2 order-1 sm:order-2\">\n        {/* 页面大小选择器 */}\n        {showPageSizeSelector && onPageSizeChange && (\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">每页</span>\n            <Select\n              value={pageSize.toString()}\n              onValueChange={(value) => onPageSizeChange(parseInt(value))}\n            >\n              <SelectTrigger className=\"w-20\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                {pageSizeOptions.map((size) => (\n                  <SelectItem key={size} value={size.toString()}>\n                    {size}\n                  </SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">条</span>\n          </div>\n        )}\n\n        {/* 页码导航 */}\n        <div className=\"flex items-center gap-1\">\n          {/* 上一页按钮 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(currentPage - 1)}\n            disabled={currentPage <= 1}\n            className=\"h-8 w-8 p-0\"\n          >\n            <ChevronLeft className=\"h-4 w-4\" />\n          </Button>\n\n          {/* 页码按钮 */}\n          {visiblePages.map((page, index) => {\n            if (page === 'ellipsis') {\n              return (\n                <div key={`ellipsis-${index}`} className=\"flex h-8 w-8 items-center justify-center\">\n                  <MoreHorizontal className=\"h-4 w-4\" />\n                </div>\n              );\n            }\n\n            return (\n              <Button\n                key={page}\n                variant={page === currentPage ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => onPageChange(page)}\n                className=\"h-8 w-8 p-0\"\n              >\n                {page}\n              </Button>\n            );\n          })}\n\n          {/* 下一页按钮 */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => onPageChange(currentPage + 1)}\n            disabled={currentPage >= totalPages}\n            className=\"h-8 w-8 p-0\"\n          >\n            <ChevronRight className=\"h-4 w-4\" />\n          </Button>\n        </div>\n\n        {/* 快速跳转 */}\n        {showQuickJumper && (\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">跳转到</span>\n            <input\n              type=\"number\"\n              min={1}\n              max={totalPages}\n              placeholder=\"页码\"\n              onKeyDown={handleQuickJump}\n              className=\"w-16 h-8 px-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n            <span className=\"text-sm text-gray-700 whitespace-nowrap\">页</span>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 简化版分页组件\n// ============================================================================\n\nexport interface SimplePaginationProps {\n  currentPage: number;\n  totalPages: number;\n  onPageChange: (page: number) => void;\n  className?: string;\n}\n\nexport const SimplePagination: React.FC<SimplePaginationProps> = ({\n  currentPage,\n  totalPages,\n  onPageChange,\n  className\n}) => {\n  return (\n    <div className={cn('flex items-center justify-center gap-2', className)}>\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => onPageChange(currentPage - 1)}\n        disabled={currentPage <= 1}\n      >\n        <ChevronLeft className=\"h-4 w-4 mr-1\" />\n        上一页\n      </Button>\n\n      <span className=\"text-sm text-gray-700 px-4\">\n        第 {currentPage} 页，共 {totalPages} 页\n      </span>\n\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => onPageChange(currentPage + 1)}\n        disabled={currentPage >= totalPages}\n      >\n        下一页\n        <ChevronRight className=\"h-4 w-4 ml-1\" />\n      </Button>\n    </div>\n  );\n};\n\n// ============================================================================\n// 移动端分页组件\n// ============================================================================\n\nexport interface MobilePaginationProps {\n  currentPage: number;\n  totalPages: number;\n  onPageChange: (page: number) => void;\n  className?: string;\n}\n\nexport const MobilePagination: React.FC<MobilePaginationProps> = ({\n  currentPage,\n  totalPages,\n  onPageChange,\n  className\n}) => {\n  return (\n    <div className={cn('flex flex-col gap-3', className)}>\n      {/* 页码信息 */}\n      <div className=\"text-center text-sm text-gray-700\">\n        第 {currentPage} 页，共 {totalPages} 页\n      </div>\n\n      {/* 导航按钮 */}\n      <div className=\"flex gap-2\">\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => onPageChange(currentPage - 1)}\n          disabled={currentPage <= 1}\n          className=\"flex-1\"\n        >\n          <ChevronLeft className=\"h-4 w-4 mr-1\" />\n          上一页\n        </Button>\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={() => onPageChange(currentPage + 1)}\n          disabled={currentPage >= totalPages}\n          className=\"flex-1\"\n        >\n          下一页\n          <ChevronRight className=\"h-4 w-4 ml-1\" />\n        </Button>\n      </div>\n    </div>\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  Pagination as default,\n  SimplePagination,\n  MobilePagination\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAGD;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAwBO,MAAM,aAAwC;QAAC,EACpD,WAAW,EACX,UAAU,EACV,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,gBAAgB,EAChB,uBAAuB,IAAI,EAC3B,kBAAkB;QAAC;QAAI;QAAI;QAAI;KAAI,EACnC,YAAY,IAAI,EAChB,kBAAkB,KAAK,EACvB,SAAS,EACV;IACC,YAAY;IACZ,MAAM,kBAAkB;QACtB,MAAM,QAAQ,GAAG,aAAa;QAC9B,MAAM,QAAiC,EAAE;QACzC,MAAM,gBAAyC,EAAE;QAEjD,UAAU;QACV,MAAM,IAAI,CAAC;QAEX,aAAa;QACb,IAAK,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,cAAc,QAAQ,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,cAAc,QAAQ,IAAK;YACtG,MAAM,IAAI,CAAC;QACb;QAEA,qBAAqB;QACrB,IAAI,aAAa,GAAG;YAClB,MAAM,IAAI,CAAC;QACb;QAEA,QAAQ;QACR,IAAI,OAAO;QACX,KAAK,MAAM,QAAQ,MAAO;YACxB,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,OAAO,SAAS,GAAG;oBACrB,cAAc,IAAI,CAAC,OAAO;gBAC5B,OAAO,IAAI,OAAO,SAAS,GAAG;oBAC5B,cAAc,IAAI,CAAC;gBACrB;gBACA,cAAc,IAAI,CAAC;gBACnB,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,eAAe;IACf,MAAM,YAAY,CAAC,cAAc,CAAC,IAAI,WAAW;IACjD,MAAM,UAAU,KAAK,GAAG,CAAC,cAAc,UAAU;IAEjD,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,GAAG,KAAK,SAAS;YACzB,MAAM,SAAS,MAAM,MAAM;YAC3B,MAAM,OAAO,SAAS,OAAO,KAAK;YAClC,IAAI,QAAQ,KAAK,QAAQ,YAAY;gBACnC,aAAa;gBACb,OAAO,KAAK,GAAG;YACjB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gEAAgE;;YAEhF,2BACC,6LAAC;gBAAI,WAAU;;oBAA2C;kCACpD,6LAAC;wBAAK,WAAU;kCAAe;;;;;;oBAAiB;oBAAG;kCACvD,6LAAC;wBAAK,WAAU;kCAAe;;;;;;oBAAe;oBAAK;kCACnD,6LAAC;wBAAK,WAAU;kCAAe;;;;;;oBAAkB;;;;;;;0BAKrD,6LAAC;gBAAI,WAAU;;oBAEZ,wBAAwB,kCACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,SAAS,QAAQ;gCACxB,eAAe,CAAC,QAAU,iBAAiB,SAAS;;kDAEpD,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;kDAEd,6LAAC,qIAAA,CAAA,gBAAa;kDACX,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,qIAAA,CAAA,aAAU;gDAAY,OAAO,KAAK,QAAQ;0DACxC;+CADc;;;;;;;;;;;;;;;;0CAMvB,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;;;;;;;kCAK9D,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,cAAc;gCAC1C,UAAU,eAAe;gCACzB,WAAU;0CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;4BAIxB,aAAa,GAAG,CAAC,CAAC,MAAM;gCACvB,IAAI,SAAS,YAAY;oCACvB,qBACE,6LAAC;wCAA8B,WAAU;kDACvC,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;uCADlB,AAAC,YAAiB,OAAN;;;;;gCAI1B;gCAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,SAAS,cAAc,YAAY;oCAC5C,MAAK;oCACL,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAET;mCANI;;;;;4BASX;0CAGA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,cAAc;gCAC1C,UAAU,eAAe;gCACzB,WAAU;0CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAK3B,iCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;0CAC1D,6LAAC;gCACC,MAAK;gCACL,KAAK;gCACL,KAAK;gCACL,aAAY;gCACZ,WAAW;gCACX,WAAU;;;;;;0CAEZ,6LAAC;gCAAK,WAAU;0CAA0C;;;;;;;;;;;;;;;;;;;;;;;;AAMtE;KAzKa;AAsLN,MAAM,mBAAoD;QAAC,EAChE,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;0BAC3D,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;;kCAEzB,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAI1C,6LAAC;gBAAK,WAAU;;oBAA6B;oBACxC;oBAAY;oBAAM;oBAAW;;;;;;;0BAGlC,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,aAAa,cAAc;gBAC1C,UAAU,eAAe;;oBAC1B;kCAEC,6LAAC,yNAAA,CAAA,eAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIhC;MAjCa;AA8CN,MAAM,mBAAoD;QAAC,EAChE,WAAW,EACX,UAAU,EACV,YAAY,EACZ,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;;0BAExC,6LAAC;gBAAI,WAAU;;oBAAoC;oBAC9C;oBAAY;oBAAM;oBAAW;;;;;;;0BAIlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,eAAe;wBACzB,WAAU;;0CAEV,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAI1C,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,aAAa,cAAc;wBAC1C,UAAU,eAAe;wBACzB,WAAU;;4BACX;0CAEC,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC;MAvCa", "debugId": null}}, {"offset": {"line": 2079, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/lib/responsive-utils.ts"], "sourcesContent": ["/**\n * 响应式设计工具函数\n * \n * 功能说明：\n * 1. 提供屏幕尺寸检测工具\n * 2. 响应式断点管理\n * 3. 移动端特性检测\n * 4. 触摸设备检测\n */\n\n'use client';\n\nimport { useState, useEffect } from 'react';\n\n// ============================================================================\n// 响应式断点定义\n// ============================================================================\n\nexport const breakpoints = {\n  xs: 0,      // 超小屏幕\n  sm: 640,    // 小屏幕\n  md: 768,    // 中等屏幕\n  lg: 1024,   // 大屏幕\n  xl: 1280,   // 超大屏幕\n  '2xl': 1536 // 2倍超大屏幕\n} as const;\n\nexport type Breakpoint = keyof typeof breakpoints;\n\n// ============================================================================\n// 屏幕尺寸检测Hook\n// ============================================================================\n\nexport interface ScreenSize {\n  width: number;\n  height: number;\n  isMobile: boolean;\n  isTablet: boolean;\n  isDesktop: boolean;\n  currentBreakpoint: Breakpoint;\n}\n\nexport const useScreenSize = (): ScreenSize => {\n  const [screenSize, setScreenSize] = useState<ScreenSize>({\n    width: 0,\n    height: 0,\n    isMobile: false,\n    isTablet: false,\n    isDesktop: false,\n    currentBreakpoint: 'xs'\n  });\n\n  useEffect(() => {\n    const updateScreenSize = () => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n\n      // 确定当前断点\n      let currentBreakpoint: Breakpoint = 'xs';\n      if (width >= breakpoints['2xl']) currentBreakpoint = '2xl';\n      else if (width >= breakpoints.xl) currentBreakpoint = 'xl';\n      else if (width >= breakpoints.lg) currentBreakpoint = 'lg';\n      else if (width >= breakpoints.md) currentBreakpoint = 'md';\n      else if (width >= breakpoints.sm) currentBreakpoint = 'sm';\n\n      setScreenSize({\n        width,\n        height,\n        isMobile: width < breakpoints.md,\n        isTablet: width >= breakpoints.md && width < breakpoints.lg,\n        isDesktop: width >= breakpoints.lg,\n        currentBreakpoint\n      });\n    };\n\n    // 初始化\n    updateScreenSize();\n\n    // 监听窗口大小变化\n    window.addEventListener('resize', updateScreenSize);\n    \n    return () => {\n      window.removeEventListener('resize', updateScreenSize);\n    };\n  }, []);\n\n  return screenSize;\n};\n\n// ============================================================================\n// 断点匹配Hook\n// ============================================================================\n\nexport const useBreakpoint = (breakpoint: Breakpoint): boolean => {\n  const [matches, setMatches] = useState(false);\n\n  useEffect(() => {\n    const checkBreakpoint = () => {\n      setMatches(window.innerWidth >= breakpoints[breakpoint]);\n    };\n\n    // 初始检查\n    checkBreakpoint();\n\n    // 监听窗口大小变化\n    window.addEventListener('resize', checkBreakpoint);\n    \n    return () => {\n      window.removeEventListener('resize', checkBreakpoint);\n    };\n  }, [breakpoint]);\n\n  return matches;\n};\n\n// ============================================================================\n// 移动端检测Hook\n// ============================================================================\n\nexport const useIsMobile = (): boolean => {\n  return useBreakpoint('md') === false;\n};\n\nexport const useIsTablet = (): boolean => {\n  const isMd = useBreakpoint('md');\n  const isLg = useBreakpoint('lg');\n  return isMd && !isLg;\n};\n\nexport const useIsDesktop = (): boolean => {\n  return useBreakpoint('lg');\n};\n\n// ============================================================================\n// 触摸设备检测\n// ============================================================================\n\nexport const useIsTouchDevice = (): boolean => {\n  const [isTouchDevice, setIsTouchDevice] = useState(false);\n\n  useEffect(() => {\n    const checkTouchDevice = () => {\n      setIsTouchDevice(\n        'ontouchstart' in window ||\n        navigator.maxTouchPoints > 0 ||\n        // @ts-ignore\n        navigator.msMaxTouchPoints > 0\n      );\n    };\n\n    checkTouchDevice();\n  }, []);\n\n  return isTouchDevice;\n};\n\n// ============================================================================\n// 设备方向检测\n// ============================================================================\n\nexport type Orientation = 'portrait' | 'landscape';\n\nexport const useOrientation = (): Orientation => {\n  const [orientation, setOrientation] = useState<Orientation>('portrait');\n\n  useEffect(() => {\n    const updateOrientation = () => {\n      setOrientation(window.innerHeight > window.innerWidth ? 'portrait' : 'landscape');\n    };\n\n    // 初始化\n    updateOrientation();\n\n    // 监听方向变化\n    window.addEventListener('resize', updateOrientation);\n    window.addEventListener('orientationchange', updateOrientation);\n    \n    return () => {\n      window.removeEventListener('resize', updateOrientation);\n      window.removeEventListener('orientationchange', updateOrientation);\n    };\n  }, []);\n\n  return orientation;\n};\n\n// ============================================================================\n// 响应式值Hook\n// ============================================================================\n\nexport interface ResponsiveValue<T> {\n  xs?: T;\n  sm?: T;\n  md?: T;\n  lg?: T;\n  xl?: T;\n  '2xl'?: T;\n}\n\nexport const useResponsiveValue = <T>(values: ResponsiveValue<T>): T | undefined => {\n  const { currentBreakpoint } = useScreenSize();\n\n  // 按优先级查找值\n  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];\n  const currentIndex = breakpointOrder.indexOf(currentBreakpoint);\n\n  for (let i = currentIndex; i < breakpointOrder.length; i++) {\n    const bp = breakpointOrder[i];\n    if (values[bp] !== undefined) {\n      return values[bp];\n    }\n  }\n\n  return undefined;\n};\n\n// ============================================================================\n// 工具函数\n// ============================================================================\n\n/**\n * 检查当前是否为移动端\n */\nexport const isMobileDevice = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth < breakpoints.md;\n};\n\n/**\n * 检查当前是否为平板设备\n */\nexport const isTabletDevice = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  const width = window.innerWidth;\n  return width >= breakpoints.md && width < breakpoints.lg;\n};\n\n/**\n * 检查当前是否为桌面设备\n */\nexport const isDesktopDevice = (): boolean => {\n  if (typeof window === 'undefined') return false;\n  return window.innerWidth >= breakpoints.lg;\n};\n\n/**\n * 获取当前断点\n */\nexport const getCurrentBreakpoint = (): Breakpoint => {\n  if (typeof window === 'undefined') return 'xs';\n  \n  const width = window.innerWidth;\n  if (width >= breakpoints['2xl']) return '2xl';\n  if (width >= breakpoints.xl) return 'xl';\n  if (width >= breakpoints.lg) return 'lg';\n  if (width >= breakpoints.md) return 'md';\n  if (width >= breakpoints.sm) return 'sm';\n  return 'xs';\n};\n\n/**\n * 生成响应式类名\n */\nexport const getResponsiveClasses = (\n  baseClass: string,\n  responsiveClasses: Partial<Record<Breakpoint, string>>\n): string => {\n  const classes = [baseClass];\n  \n  Object.entries(responsiveClasses).forEach(([breakpoint, className]) => {\n    if (className) {\n      const prefix = breakpoint === 'xs' ? '' : `${breakpoint}:`;\n      classes.push(`${prefix}${className}`);\n    }\n  });\n  \n  return classes.join(' ');\n};\n\n// ============================================================================\n// 导出所有工具\n// ============================================================================\n\nexport {\n  useScreenSize,\n  useBreakpoint,\n  useIsMobile,\n  useIsTablet,\n  useIsDesktop,\n  useIsTouchDevice,\n  useOrientation,\n  useResponsiveValue\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;;;;;;;;;;AAID;;AAFA;;AAQO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO,KAAK,SAAS;AACvB;AAiBO,MAAM,gBAAgB;;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;QACX,mBAAmB;IACrB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;4DAAmB;oBACvB,MAAM,QAAQ,OAAO,UAAU;oBAC/B,MAAM,SAAS,OAAO,WAAW;oBAEjC,SAAS;oBACT,IAAI,oBAAgC;oBACpC,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,oBAAoB;yBAChD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;yBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;yBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;yBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;oBAEtD,cAAc;wBACZ;wBACA;wBACA,UAAU,QAAQ,YAAY,EAAE;wBAChC,UAAU,SAAS,YAAY,EAAE,IAAI,QAAQ,YAAY,EAAE;wBAC3D,WAAW,SAAS,YAAY,EAAE;wBAClC;oBACF;gBACF;;YAEA,MAAM;YACN;YAEA,WAAW;YACX,OAAO,gBAAgB,CAAC,UAAU;YAElC;2CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;kCAAG,EAAE;IAEL,OAAO;AACT;GA7Ca;AAmDN,MAAM,gBAAgB,CAAC;;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;2DAAkB;oBACtB,WAAW,OAAO,UAAU,IAAI,WAAW,CAAC,WAAW;gBACzD;;YAEA,OAAO;YACP;YAEA,WAAW;YACX,OAAO,gBAAgB,CAAC,UAAU;YAElC;2CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;kCAAG;QAAC;KAAW;IAEf,OAAO;AACT;IApBa;AA0BN,MAAM,cAAc;;IACzB,OAAO,cAAc,UAAU;AACjC;IAFa;;QACJ;;;AAGF,MAAM,cAAc;;IACzB,MAAM,OAAO,cAAc;IAC3B,MAAM,OAAO,cAAc;IAC3B,OAAO,QAAQ,CAAC;AAClB;IAJa;;QACE;QACA;;;AAIR,MAAM,eAAe;;IAC1B,OAAO,cAAc;AACvB;IAFa;;QACJ;;;AAOF,MAAM,mBAAmB;;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;+DAAmB;oBACvB,iBACE,kBAAkB,UAClB,UAAU,cAAc,GAAG,KAC3B,aAAa;oBACb,UAAU,gBAAgB,GAAG;gBAEjC;;YAEA;QACF;qCAAG,EAAE;IAEL,OAAO;AACT;IAjBa;AAyBN,MAAM,iBAAiB;;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE5D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;8DAAoB;oBACxB,eAAe,OAAO,WAAW,GAAG,OAAO,UAAU,GAAG,aAAa;gBACvE;;YAEA,MAAM;YACN;YAEA,SAAS;YACT,OAAO,gBAAgB,CAAC,UAAU;YAClC,OAAO,gBAAgB,CAAC,qBAAqB;YAE7C;4CAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;oBACrC,OAAO,mBAAmB,CAAC,qBAAqB;gBAClD;;QACF;mCAAG,EAAE;IAEL,OAAO;AACT;IAtBa;AAqCN,MAAM,qBAAqB,CAAI;;IACpC,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,UAAU;IACV,MAAM,kBAAgC;QAAC;QAAO;QAAM;QAAM;QAAM;QAAM;KAAK;IAC3E,MAAM,eAAe,gBAAgB,OAAO,CAAC;IAE7C,IAAK,IAAI,IAAI,cAAc,IAAI,gBAAgB,MAAM,EAAE,IAAK;QAC1D,MAAM,KAAK,eAAe,CAAC,EAAE;QAC7B,IAAI,MAAM,CAAC,GAAG,KAAK,WAAW;YAC5B,OAAO,MAAM,CAAC,GAAG;QACnB;IACF;IAEA,OAAO;AACT;IAfa;;QACmB;;;AAuBzB,MAAM,iBAAiB;IAC5B;;IACA,OAAO,OAAO,UAAU,GAAG,YAAY,EAAE;AAC3C;AAKO,MAAM,iBAAiB;IAC5B;;IACA,MAAM,QAAQ,OAAO,UAAU;IAC/B,OAAO,SAAS,YAAY,EAAE,IAAI,QAAQ,YAAY,EAAE;AAC1D;AAKO,MAAM,kBAAkB;IAC7B;;IACA,OAAO,OAAO,UAAU,IAAI,YAAY,EAAE;AAC5C;AAKO,MAAM,uBAAuB;IAClC;;IAEA,MAAM,QAAQ,OAAO,UAAU;IAC/B,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,OAAO;IACxC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;IACpC,OAAO;AACT;AAKO,MAAM,uBAAuB,CAClC,WACA;IAEA,MAAM,UAAU;QAAC;KAAU;IAE3B,OAAO,OAAO,CAAC,mBAAmB,OAAO,CAAC;YAAC,CAAC,YAAY,UAAU;QAChE,IAAI,WAAW;YACb,MAAM,SAAS,eAAe,OAAO,KAAK,AAAC,GAAa,OAAX,YAAW;YACxD,QAAQ,IAAI,CAAC,AAAC,GAAW,OAAT,QAAmB,OAAV;QAC3B;IACF;IAEA,OAAO,QAAQ,IAAI,CAAC;AACtB", "debugId": null}}, {"offset": {"line": 2337, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 2483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/data-table.tsx"], "sourcesContent": ["/**\n * 数据表格组件\n * \n * 功能说明：\n * 1. 提供完整的数据表格功能\n * 2. 支持排序、筛选、分页\n * 3. 响应式设计，移动端友好\n * 4. 可配置列定义和操作\n */\n\nimport React, { useState } from 'react';\nimport { ChevronUp, ChevronDown, Search, Filter, MoreHorizontal } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './button';\nimport { Input } from './input';\nimport { Badge } from './badge';\nimport { LoadingTableRow } from './loading';\nimport { Pagination } from './pagination';\nimport { useIsMobile } from '@/lib/responsive-utils';\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow,\n} from './table';\n\n// ============================================================================\n// 表格列定义接口\n// ============================================================================\n\nexport interface ColumnDef<T> {\n  key: string;                    // 列的唯一标识\n  title: string;                  // 列标题\n  dataIndex?: keyof T;            // 数据字段名\n  width?: string | number;        // 列宽度\n  sortable?: boolean;             // 是否可排序\n  filterable?: boolean;           // 是否可筛选\n  searchable?: boolean;           // 是否可搜索\n  align?: 'left' | 'center' | 'right'; // 对齐方式\n  className?: string;             // 自定义样式\n  render?: (value: any, record: T, index: number) => React.ReactNode; // 自定义渲染\n  sorter?: (a: T, b: T) => number; // 自定义排序函数\n  filters?: Array<{ text: string; value: any }>; // 筛选选项\n}\n\n// ============================================================================\n// 表格组件接口\n// ============================================================================\n\nexport interface DataTableProps<T> {\n  columns: ColumnDef<T>[];        // 列定义\n  data: T[];                      // 数据源\n  loading?: boolean;              // 加载状态\n  pagination?: {                  // 分页配置\n    current: number;\n    pageSize: number;\n    total: number;\n    showSizeChanger?: boolean;\n    showQuickJumper?: boolean;\n    onChange: (page: number, pageSize: number) => void;\n  };\n  rowKey?: keyof T | ((record: T) => string); // 行唯一标识\n  onRow?: (record: T, index: number) => {     // 行事件处理\n    onClick?: () => void;\n    onDoubleClick?: () => void;\n    className?: string;\n  };\n  emptyText?: string;             // 空数据提示\n  size?: 'small' | 'middle' | 'large'; // 表格尺寸\n  bordered?: boolean;             // 是否显示边框\n  striped?: boolean;              // 是否显示斑马纹\n  className?: string;\n}\n\n// ============================================================================\n// 数据表格组件实现\n// ============================================================================\n\nexport function DataTable<T extends Record<string, any>>({\n  columns,\n  data,\n  loading = false,\n  pagination,\n  rowKey = 'id',\n  onRow,\n  emptyText = '暂无数据',\n  className\n}: DataTableProps<T>) {\n  const [sortConfig, setSortConfig] = useState<{\n    key: string;\n    direction: 'asc' | 'desc';\n  } | null>(null);\n  const [searchText, setSearchText] = useState('');\n  const [filters, setFilters] = useState<Record<string, any>>({});\n  const isMobile = useIsMobile();\n\n  // 获取行的唯一标识\n  const getRowKey = (record: T, index: number): string => {\n    if (typeof rowKey === 'function') {\n      return rowKey(record);\n    }\n    return record[rowKey]?.toString() || index.toString();\n  };\n\n  // 处理排序\n  const handleSort = (column: ColumnDef<T>) => {\n    if (!column.sortable) return;\n\n    let direction: 'asc' | 'desc' = 'asc';\n    if (sortConfig?.key === column.key && sortConfig.direction === 'asc') {\n      direction = 'desc';\n    }\n\n    setSortConfig({ key: column.key, direction });\n  };\n\n  // 处理搜索\n  const handleSearch = (value: string) => {\n    setSearchText(value);\n  };\n\n  // 处理筛选\n  const handleFilter = (columnKey: string, value: any) => {\n    setFilters(prev => ({\n      ...prev,\n      [columnKey]: value\n    }));\n  };\n\n  // 渲染排序图标\n  const renderSortIcon = (column: ColumnDef<T>) => {\n    if (!column.sortable) return null;\n\n    const isActive = sortConfig?.key === column.key;\n    const direction = sortConfig?.direction;\n\n    return (\n      <span className=\"ml-1 inline-flex flex-col\">\n        <ChevronUp \n          className={cn(\n            'h-3 w-3 -mb-1',\n            isActive && direction === 'asc' ? 'text-blue-600' : 'text-gray-400'\n          )} \n        />\n        <ChevronDown \n          className={cn(\n            'h-3 w-3',\n            isActive && direction === 'desc' ? 'text-blue-600' : 'text-gray-400'\n          )} \n        />\n      </span>\n    );\n  };\n\n  // 渲染表头\n  const renderHeader = () => (\n    <TableHeader>\n      <TableRow>\n        {columns.map((column) => (\n          <TableHead\n            key={column.key}\n            className={cn(\n              column.className,\n              column.sortable && 'cursor-pointer hover:bg-gray-50',\n              column.align === 'center' && 'text-center',\n              column.align === 'right' && 'text-right'\n            )}\n            style={{ width: column.width }}\n            onClick={() => handleSort(column)}\n          >\n            <div className=\"flex items-center\">\n              {column.title}\n              {renderSortIcon(column)}\n            </div>\n          </TableHead>\n        ))}\n      </TableRow>\n    </TableHeader>\n  );\n\n  // 渲染单元格内容\n  const renderCell = (column: ColumnDef<T>, record: T, index: number) => {\n    const value = column.dataIndex ? record[column.dataIndex] : undefined;\n    \n    if (column.render) {\n      return column.render(value, record, index);\n    }\n\n    // 默认渲染逻辑\n    if (value === null || value === undefined) {\n      return <span className=\"text-gray-400\">-</span>;\n    }\n\n    if (typeof value === 'boolean') {\n      return (\n        <Badge variant={value ? 'default' : 'secondary'}>\n          {value ? '是' : '否'}\n        </Badge>\n      );\n    }\n\n    if (typeof value === 'number') {\n      return value.toLocaleString();\n    }\n\n    return value.toString();\n  };\n\n  // 渲染表格主体\n  const renderBody = () => {\n    if (loading) {\n      return (\n        <TableBody>\n          <LoadingTableRow columns={columns.length} rows={5} />\n        </TableBody>\n      );\n    }\n\n    if (data.length === 0) {\n      return (\n        <TableBody>\n          <TableRow>\n            <TableCell \n              colSpan={columns.length} \n              className=\"text-center py-8 text-gray-500\"\n            >\n              {emptyText}\n            </TableCell>\n          </TableRow>\n        </TableBody>\n      );\n    }\n\n    return (\n      <TableBody>\n        {data.map((record, index) => {\n          const rowProps = onRow?.(record, index) || {};\n          \n          return (\n            <TableRow\n              key={getRowKey(record, index)}\n              className={cn(\n                rowProps.className,\n                rowProps.onClick && 'cursor-pointer hover:bg-gray-50',\n                index % 2 === 1 && 'bg-gray-50/50'\n              )}\n              onClick={rowProps.onClick}\n              onDoubleClick={rowProps.onDoubleClick}\n            >\n              {columns.map((column) => (\n                <TableCell\n                  key={column.key}\n                  className={cn(\n                    column.align === 'center' && 'text-center',\n                    column.align === 'right' && 'text-right'\n                  )}\n                >\n                  {renderCell(column, record, index)}\n                </TableCell>\n              ))}\n            </TableRow>\n          );\n        })}\n      </TableBody>\n    );\n  };\n\n  return (\n    <div className={cn('space-y-4', className)}>\n      {/* 表格工具栏 */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n        <div className=\"flex gap-2\">\n          {/* 搜索框 */}\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"搜索...\"\n              value={searchText}\n              onChange={(e) => handleSearch(e.target.value)}\n              className=\"pl-9 w-64\"\n            />\n          </div>\n\n          {/* 筛选按钮 */}\n          <Button variant=\"outline\" size=\"sm\">\n            <Filter className=\"h-4 w-4 mr-2\" />\n            筛选\n          </Button>\n        </div>\n\n        {/* 操作按钮 */}\n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" size=\"sm\">\n            <MoreHorizontal className=\"h-4 w-4 mr-2\" />\n            更多操作\n          </Button>\n        </div>\n      </div>\n\n      {/* 表格 */}\n      <div className=\"rounded-md border\">\n        <Table>\n          {renderHeader()}\n          {renderBody()}\n        </Table>\n      </div>\n\n      {/* 分页 */}\n      {pagination && (\n        <Pagination\n          currentPage={pagination.current}\n          totalPages={Math.ceil(pagination.total / pagination.pageSize)}\n          totalItems={pagination.total}\n          pageSize={pagination.pageSize}\n          onPageChange={(page) => pagination.onChange(page, pagination.pageSize)}\n          onPageSizeChange={(pageSize) => pagination.onChange(1, pageSize)}\n          showPageSizeSelector={pagination.showSizeChanger}\n          showQuickJumper={pagination.showQuickJumper}\n        />\n      )}\n    </div>\n  );\n}\n\n// ============================================================================\n// 导出组件\n// ============================================================================\n\nexport { DataTable as default };\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AA6DO,SAAS,UAAyC,KASrC;QATqC,EACvD,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,UAAU,EACV,SAAS,IAAI,EACb,KAAK,EACL,YAAY,MAAM,EAClB,SAAS,EACS,GATqC;;IAUvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGjC;IACV,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC7D,MAAM,WAAW,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD;IAE3B,WAAW;IACX,MAAM,YAAY,CAAC,QAAW;YAIrB;QAHP,IAAI,OAAO,WAAW,YAAY;YAChC,OAAO,OAAO;QAChB;QACA,OAAO,EAAA,iBAAA,MAAM,CAAC,OAAO,cAAd,qCAAA,eAAgB,QAAQ,OAAM,MAAM,QAAQ;IACrD;IAEA,OAAO;IACP,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,OAAO,QAAQ,EAAE;QAEtB,IAAI,YAA4B;QAChC,IAAI,CAAA,uBAAA,iCAAA,WAAY,GAAG,MAAK,OAAO,GAAG,IAAI,WAAW,SAAS,KAAK,OAAO;YACpE,YAAY;QACd;QAEA,cAAc;YAAE,KAAK,OAAO,GAAG;YAAE;QAAU;IAC7C;IAEA,OAAO;IACP,MAAM,eAAe,CAAC;QACpB,cAAc;IAChB;IAEA,OAAO;IACP,MAAM,eAAe,CAAC,WAAmB;QACvC,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,CAAC,UAAU,EAAE;YACf,CAAC;IACH;IAEA,SAAS;IACT,MAAM,iBAAiB,CAAC;QACtB,IAAI,CAAC,OAAO,QAAQ,EAAE,OAAO;QAE7B,MAAM,WAAW,CAAA,uBAAA,iCAAA,WAAY,GAAG,MAAK,OAAO,GAAG;QAC/C,MAAM,YAAY,uBAAA,iCAAA,WAAY,SAAS;QAEvC,qBACE,6LAAC;YAAK,WAAU;;8BACd,6LAAC,mNAAA,CAAA,YAAS;oBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iBACA,YAAY,cAAc,QAAQ,kBAAkB;;;;;;8BAGxD,6LAAC,uNAAA,CAAA,cAAW;oBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,WACA,YAAY,cAAc,SAAS,kBAAkB;;;;;;;;;;;;IAK/D;IAEA,OAAO;IACP,MAAM,eAAe,kBACnB,6LAAC,oIAAA,CAAA,cAAW;sBACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;0BACN,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,YAAS;wBAER,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OAAO,SAAS,EAChB,OAAO,QAAQ,IAAI,mCACnB,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;wBAE9B,OAAO;4BAAE,OAAO,OAAO,KAAK;wBAAC;wBAC7B,SAAS,IAAM,WAAW;kCAE1B,cAAA,6LAAC;4BAAI,WAAU;;gCACZ,OAAO,KAAK;gCACZ,eAAe;;;;;;;uBAZb,OAAO,GAAG;;;;;;;;;;;;;;;IAoBzB,UAAU;IACV,MAAM,aAAa,CAAC,QAAsB,QAAW;QACnD,MAAM,QAAQ,OAAO,SAAS,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,GAAG;QAE5D,IAAI,OAAO,MAAM,EAAE;YACjB,OAAO,OAAO,MAAM,CAAC,OAAO,QAAQ;QACtC;QAEA,SAAS;QACT,IAAI,UAAU,QAAQ,UAAU,WAAW;YACzC,qBAAO,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;QACzC;QAEA,IAAI,OAAO,UAAU,WAAW;YAC9B,qBACE,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAS,QAAQ,YAAY;0BACjC,QAAQ,MAAM;;;;;;QAGrB;QAEA,IAAI,OAAO,UAAU,UAAU;YAC7B,OAAO,MAAM,cAAc;QAC7B;QAEA,OAAO,MAAM,QAAQ;IACvB;IAEA,SAAS;IACT,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,qBACE,6LAAC,oIAAA,CAAA,YAAS;0BACR,cAAA,6LAAC,sIAAA,CAAA,kBAAe;oBAAC,SAAS,QAAQ,MAAM;oBAAE,MAAM;;;;;;;;;;;QAGtD;QAEA,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,qBACE,6LAAC,oIAAA,CAAA,YAAS;0BACR,cAAA,6LAAC,oIAAA,CAAA,WAAQ;8BACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;wBACR,SAAS,QAAQ,MAAM;wBACvB,WAAU;kCAET;;;;;;;;;;;;;;;;QAKX;QAEA,qBACE,6LAAC,oIAAA,CAAA,YAAS;sBACP,KAAK,GAAG,CAAC,CAAC,QAAQ;gBACjB,MAAM,WAAW,CAAA,kBAAA,4BAAA,MAAQ,QAAQ,WAAU,CAAC;gBAE5C,qBACE,6LAAC,oIAAA,CAAA,WAAQ;oBAEP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,SAAS,SAAS,EAClB,SAAS,OAAO,IAAI,mCACpB,QAAQ,MAAM,KAAK;oBAErB,SAAS,SAAS,OAAO;oBACzB,eAAe,SAAS,aAAa;8BAEpC,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,YAAS;4BAER,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OAAO,KAAK,KAAK,YAAY,eAC7B,OAAO,KAAK,KAAK,WAAW;sCAG7B,WAAW,QAAQ,QAAQ;2BANvB,OAAO,GAAG;;;;;mBAXd,UAAU,QAAQ;;;;;YAsB7B;;;;;;IAGN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAE9B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;;;;;;;;;;;;0CAKd,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;;kDAC7B,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAMvC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;;8CAC7B,6LAAC,mNAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAOjD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;wBACH;wBACA;;;;;;;;;;;;YAKJ,4BACC,6LAAC,yIAAA,CAAA,aAAU;gBACT,aAAa,WAAW,OAAO;gBAC/B,YAAY,KAAK,IAAI,CAAC,WAAW,KAAK,GAAG,WAAW,QAAQ;gBAC5D,YAAY,WAAW,KAAK;gBAC5B,UAAU,WAAW,QAAQ;gBAC7B,cAAc,CAAC,OAAS,WAAW,QAAQ,CAAC,MAAM,WAAW,QAAQ;gBACrE,kBAAkB,CAAC,WAAa,WAAW,QAAQ,CAAC,GAAG;gBACvD,sBAAsB,WAAW,eAAe;gBAChD,iBAAiB,WAAW,eAAe;;;;;;;;;;;;AAKrD;GApPgB;;QAgBG,oIAAA,CAAA,cAAW;;;KAhBd", "debugId": null}}, {"offset": {"line": 2870, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,KAEgC;QAFhC,EACrB,GAAG,OACkD,GAFhC;IAGrB,qBAAO,6LAAC,qKAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,KAEgC;QAFhC,EACpB,GAAG,OACiD,GAFhC;IAGpB,qBAAO,6LAAC,qKAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAGgC;QAHhC,EACrB,SAAS,EACT,GAAG,OACkD,GAHhC;IAIrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC;QAAa,aAAU;;0BACtB,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,6LAAC,qKAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,QAAK;;;;;0CACN,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;MAhCS;AAkCT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 3078, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/confirm-dialog.tsx"], "sourcesContent": ["/**\n * 确认对话框组件\n * \n * 功能说明：\n * 1. 提供通用的确认对话框\n * 2. 支持不同类型的确认操作\n * 3. 可配置的标题、内容和按钮\n * 4. 支持异步操作和加载状态\n */\n\n'use client';\n\nimport React, { useState } from 'react';\nimport { AlertTriangle, Trash2, Edit, Eye, Plus } from 'lucide-react';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog';\nimport { Button } from '@/components/ui/button';\nimport { LoadingButton } from '@/components/ui/loading';\nimport { Badge } from '@/components/ui/badge';\n\n// ============================================================================\n// 确认对话框类型定义\n// ============================================================================\n\nexport type ConfirmType = 'delete' | 'edit' | 'create' | 'view' | 'warning' | 'info';\n\nexport interface ConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  type?: ConfirmType;\n  title?: string;\n  description?: string;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n  loading?: boolean;\n  children?: React.ReactNode;\n  data?: any; // 可以传递额外的数据用于显示\n}\n\n// ============================================================================\n// 确认对话框组件实现\n// ============================================================================\n\nexport const ConfirmDialog: React.FC<ConfirmDialogProps> = ({\n  open,\n  onOpenChange,\n  type = 'warning',\n  title,\n  description,\n  confirmText,\n  cancelText = '取消',\n  onConfirm,\n  onCancel,\n  loading = false,\n  children,\n  data\n}) => {\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  // 根据类型获取默认配置\n  const getTypeConfig = () => {\n    switch (type) {\n      case 'delete':\n        return {\n          icon: Trash2,\n          iconColor: 'text-red-500',\n          title: title || '确认删除',\n          description: description || '此操作不可撤销，确定要删除吗？',\n          confirmText: confirmText || '删除',\n          confirmVariant: 'destructive' as const\n        };\n      case 'edit':\n        return {\n          icon: Edit,\n          iconColor: 'text-blue-500',\n          title: title || '确认编辑',\n          description: description || '确定要编辑此项吗？',\n          confirmText: confirmText || '编辑',\n          confirmVariant: 'default' as const\n        };\n      case 'create':\n        return {\n          icon: Plus,\n          iconColor: 'text-green-500',\n          title: title || '确认创建',\n          description: description || '确定要创建此项吗？',\n          confirmText: confirmText || '创建',\n          confirmVariant: 'default' as const\n        };\n      case 'view':\n        return {\n          icon: Eye,\n          iconColor: 'text-gray-500',\n          title: title || '查看详情',\n          description: description || '',\n          confirmText: confirmText || '确定',\n          confirmVariant: 'default' as const\n        };\n      case 'warning':\n        return {\n          icon: AlertTriangle,\n          iconColor: 'text-yellow-500',\n          title: title || '警告',\n          description: description || '请确认您的操作',\n          confirmText: confirmText || '确认',\n          confirmVariant: 'default' as const\n        };\n      case 'info':\n      default:\n        return {\n          icon: AlertTriangle,\n          iconColor: 'text-blue-500',\n          title: title || '提示',\n          description: description || '请确认您的操作',\n          confirmText: confirmText || '确认',\n          confirmVariant: 'default' as const\n        };\n    }\n  };\n\n  const config = getTypeConfig();\n  const Icon = config.icon;\n\n  // 处理确认操作\n  const handleConfirm = async () => {\n    if (!onConfirm) return;\n\n    try {\n      setIsProcessing(true);\n      await onConfirm();\n      onOpenChange(false);\n    } catch (error) {\n      console.error('确认操作失败:', error);\n      // 这里可以添加错误提示\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  // 处理取消操作\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel();\n    }\n    onOpenChange(false);\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-md\">\n        <DialogHeader>\n          <div className=\"flex items-center gap-3\">\n            <div className={`p-2 rounded-full bg-gray-100 ${config.iconColor}`}>\n              <Icon className=\"h-5 w-5\" />\n            </div>\n            <div>\n              <DialogTitle className=\"text-lg font-semibold\">\n                {config.title}\n              </DialogTitle>\n            </div>\n          </div>\n        </DialogHeader>\n\n        <div className=\"py-4\">\n          {config.description && (\n            <DialogDescription className=\"text-gray-600 mb-4\">\n              {config.description}\n            </DialogDescription>\n          )}\n\n          {/* 自定义内容 */}\n          {children}\n\n          {/* 数据展示 */}\n          {data && (\n            <div className=\"mt-4 p-4 bg-gray-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium mb-2\">相关信息：</h4>\n              <div className=\"space-y-2\">\n                {Object.entries(data).map(([key, value]) => (\n                  <div key={key} className=\"flex justify-between items-center text-sm\">\n                    <span className=\"text-gray-600\">{key}:</span>\n                    <span className=\"font-medium\">\n                      {typeof value === 'boolean' ? (\n                        <Badge variant={value ? 'default' : 'secondary'}>\n                          {value ? '是' : '否'}\n                        </Badge>\n                      ) : (\n                        value?.toString() || '-'\n                      )}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter className=\"gap-2\">\n          <Button\n            variant=\"outline\"\n            onClick={handleCancel}\n            disabled={isProcessing || loading}\n          >\n            {cancelText}\n          </Button>\n          \n          {onConfirm && (\n            <LoadingButton\n              variant={config.confirmVariant}\n              onClick={handleConfirm}\n              loading={isProcessing || loading}\n              loadingText=\"处理中...\"\n            >\n              {config.confirmText}\n            </LoadingButton>\n          )}\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n};\n\n// ============================================================================\n// 删除确认对话框（特化组件）\n// ============================================================================\n\nexport interface DeleteConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  title?: string;\n  itemName?: string;\n  itemType?: string;\n  onConfirm: () => void | Promise<void>;\n  loading?: boolean;\n  additionalInfo?: Record<string, any>;\n}\n\nexport const DeleteConfirmDialog: React.FC<DeleteConfirmDialogProps> = ({\n  open,\n  onOpenChange,\n  title,\n  itemName,\n  itemType = '项目',\n  onConfirm,\n  loading = false,\n  additionalInfo\n}) => {\n  return (\n    <ConfirmDialog\n      open={open}\n      onOpenChange={onOpenChange}\n      type=\"delete\"\n      title={title || `删除${itemType}`}\n      description={\n        itemName \n          ? `确定要删除${itemType}\"${itemName}\"吗？此操作不可撤销。`\n          : `确定要删除此${itemType}吗？此操作不可撤销。`\n      }\n      confirmText=\"删除\"\n      onConfirm={onConfirm}\n      loading={loading}\n      data={additionalInfo}\n    />\n  );\n};\n\n// ============================================================================\n// 批量操作确认对话框\n// ============================================================================\n\nexport interface BatchConfirmDialogProps {\n  open: boolean;\n  onOpenChange: (open: boolean) => void;\n  action: string;\n  itemCount: number;\n  itemType?: string;\n  onConfirm: () => void | Promise<void>;\n  loading?: boolean;\n}\n\nexport const BatchConfirmDialog: React.FC<BatchConfirmDialogProps> = ({\n  open,\n  onOpenChange,\n  action,\n  itemCount,\n  itemType = '项目',\n  onConfirm,\n  loading = false\n}) => {\n  return (\n    <ConfirmDialog\n      open={open}\n      onOpenChange={onOpenChange}\n      type={action === '删除' ? 'delete' : 'warning'}\n      title={`批量${action}`}\n      description={`确定要${action}选中的 ${itemCount} 个${itemType}吗？`}\n      confirmText={action}\n      onConfirm={onConfirm}\n      loading={loading}\n    />\n  );\n};\n\n// ============================================================================\n// 导出所有组件\n// ============================================================================\n\nexport {\n  ConfirmDialog as default,\n  DeleteConfirmDialog,\n  BatchConfirmDialog\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAQA;AACA;AACA;;;AAdA;;;;;;;AAyCO,MAAM,gBAA8C;QAAC,EAC1D,IAAI,EACJ,YAAY,EACZ,OAAO,SAAS,EAChB,KAAK,EACL,WAAW,EACX,WAAW,EACX,aAAa,IAAI,EACjB,SAAS,EACT,QAAQ,EACR,UAAU,KAAK,EACf,QAAQ,EACR,IAAI,EACL;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,aAAa;IACb,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,MAAM,6MAAA,CAAA,SAAM;oBACZ,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,8MAAA,CAAA,OAAI;oBACV,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,qMAAA,CAAA,OAAI;oBACV,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,mMAAA,CAAA,MAAG;oBACT,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;gBACH,OAAO;oBACL,MAAM,2NAAA,CAAA,gBAAa;oBACnB,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;YACF,KAAK;YACL;gBACE,OAAO;oBACL,MAAM,2NAAA,CAAA,gBAAa;oBACnB,WAAW;oBACX,OAAO,SAAS;oBAChB,aAAa,eAAe;oBAC5B,aAAa,eAAe;oBAC5B,gBAAgB;gBAClB;QACJ;IACF;IAEA,MAAM,SAAS;IACf,MAAM,OAAO,OAAO,IAAI;IAExB,SAAS;IACT,MAAM,gBAAgB;QACpB,IAAI,CAAC,WAAW;QAEhB,IAAI;YACF,gBAAgB;YAChB,MAAM;YACN,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,WAAW;QACzB,aAAa;QACf,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,SAAS;IACT,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ;QACF;QACA,aAAa;IACf;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAW,AAAC,gCAAgD,OAAjB,OAAO,SAAS;0CAC9D,cAAA,6LAAC;oCAAK,WAAU;;;;;;;;;;;0CAElB,6LAAC;0CACC,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;8CACpB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;8BAMrB,6LAAC;oBAAI,WAAU;;wBACZ,OAAO,WAAW,kBACjB,6LAAC,qIAAA,CAAA,oBAAiB;4BAAC,WAAU;sCAC1B,OAAO,WAAW;;;;;;wBAKtB;wBAGA,sBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,6LAAC;oCAAI,WAAU;8CACZ,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC;4CAAC,CAAC,KAAK,MAAM;6DACrC,6LAAC;4CAAc,WAAU;;8DACvB,6LAAC;oDAAK,WAAU;;wDAAiB;wDAAI;;;;;;;8DACrC,6LAAC;oDAAK,WAAU;8DACb,OAAO,UAAU,0BAChB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAS,QAAQ,YAAY;kEACjC,QAAQ,MAAM;;;;;mGAGjB,CAAA,kBAAA,4BAAA,MAAO,QAAQ,OAAM;;;;;;;2CARjB;;;;;;;;;;;;;;;;;;;;;;;8BAkBpB,6LAAC,qIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU,gBAAgB;sCAEzB;;;;;;wBAGF,2BACC,6LAAC,sIAAA,CAAA,gBAAa;4BACZ,SAAS,OAAO,cAAc;4BAC9B,SAAS;4BACT,SAAS,gBAAgB;4BACzB,aAAY;sCAEX,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAOjC;GAjLa;KAAA;AAkMN,MAAM,sBAA0D;QAAC,EACtE,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,WAAW,IAAI,EACf,SAAS,EACT,UAAU,KAAK,EACf,cAAc,EACf;IACC,qBACE,6LAAC;QACC,MAAM;QACN,cAAc;QACd,MAAK;QACL,OAAO,SAAS,AAAC,KAAa,OAAT;QACrB,aACE,WACI,AAAC,QAAmB,OAAZ,UAAS,KAAY,OAAT,UAAS,iBAC7B,AAAC,SAAiB,OAAT,UAAS;QAExB,aAAY;QACZ,WAAW;QACX,SAAS;QACT,MAAM;;;;;;AAGZ;MA3Ba;AA2CN,MAAM,qBAAwD;QAAC,EACpE,IAAI,EACJ,YAAY,EACZ,MAAM,EACN,SAAS,EACT,WAAW,IAAI,EACf,SAAS,EACT,UAAU,KAAK,EAChB;IACC,qBACE,6LAAC;QACC,MAAM;QACN,cAAc;QACd,MAAM,WAAW,OAAO,WAAW;QACnC,OAAO,AAAC,KAAW,OAAP;QACZ,aAAa,AAAC,MAAkB,OAAb,QAAO,QAAoB,OAAd,WAAU,MAAa,OAAT,UAAS;QACvD,aAAa;QACb,WAAW;QACX,SAAS;;;;;;AAGf;MArBa", "debugId": null}}, {"offset": {"line": 3423, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3458, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-card text-card-foreground\",\n        destructive:\n          \"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Alert({\n  className,\n  variant,\n  ...props\n}: React.ComponentProps<\"div\"> & VariantProps<typeof alertVariants>) {\n  return (\n    <div\n      data-slot=\"alert\"\n      role=\"alert\"\n      className={cn(alertVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nfunction AlertTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-title\"\n      className={cn(\n        \"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AlertDescription({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"alert-description\"\n      className={cn(\n        \"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAIoD;QAJpD,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D,GAJpD;IAKb,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 3536, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/components/forms/promotion-form.tsx"], "sourcesContent": ["/**\n * 促销活动表单组件\n * \n * 功能说明：\n * 1. 支持创建、编辑、查看三种模式\n * 2. 表单验证和错误处理\n * 3. 响应式设计\n * 4. 关联竞品和票种选择\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Label } from '@/components/ui/label';\nimport { Textarea } from '@/components/ui/textarea';\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\nimport { DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Loader2, AlertCircle, CheckCircle2, Calendar, DollarSign } from 'lucide-react';\nimport { Promotion, CreatePromotionInput, UpdatePromotionInput, Competitor, TicketType } from '@/types';\n\n// ============================================================================\n// 表单验证模式\n// ============================================================================\n\nconst promotionSchema = z.object({\n  competitor_id: z.number().min(1, '请选择竞品'),\n  ticket_type_id: z.number().min(1, '请选择票种'),\n  activity_name: z.string()\n    .min(1, '活动名称不能为空')\n    .max(200, '活动名称不能超过200个字符'),\n  rack_rate: z.number()\n    .min(0, '原价不能为负数')\n    .optional()\n    .nullable(),\n  promo_price: z.number()\n    .min(0, '促销价不能为负数')\n    .optional()\n    .nullable(),\n  sale_start_date: z.string().optional().nullable(),\n  sale_end_date: z.string().optional().nullable(),\n  use_start_date: z.string().optional().nullable(),\n  use_end_date: z.string().optional().nullable(),\n  sales_channel: z.string()\n    .max(100, '销售渠道不能超过100个字符')\n    .optional()\n    .nullable(),\n  usage_rules: z.string()\n    .max(500, '使用规则不能超过500个字符')\n    .optional()\n    .nullable(),\n  data_source_url: z.string()\n    .refine((val) => !val || /^https?:\\/\\/.+/.test(val), '请输入有效的URL')\n    .refine((val) => !val || val.length <= 500, 'URL不能超过500个字符')\n    .optional()\n    .or(z.literal('')),\n  remarks: z.string()\n    .max(1000, '备注不能超过1000个字符')\n    .optional()\n    .nullable()\n});\n\ntype PromotionFormData = z.infer<typeof promotionSchema>;\n\n// ============================================================================\n// 组件属性接口\n// ============================================================================\n\ninterface PromotionFormProps {\n  mode: 'create' | 'edit' | 'view';\n  initialData?: Promotion;\n  onSubmit: (data: CreatePromotionInput | UpdatePromotionInput) => Promise<void>;\n  onCancel: () => void;\n  loading?: boolean;\n  className?: string;\n}\n\n// ============================================================================\n// 促销活动表单组件实现\n// ============================================================================\n\nexport const PromotionForm: React.FC<PromotionFormProps> = ({\n  mode,\n  initialData,\n  onSubmit,\n  onCancel,\n  loading = false,\n  className\n}) => {\n  const [submitError, setSubmitError] = useState<string>('');\n  const [submitSuccess, setSubmitSuccess] = useState<string>('');\n  const [competitors, setCompetitors] = useState<Competitor[]>([]);\n  const [ticketTypes, setTicketTypes] = useState<TicketType[]>([]);\n  const [loadingOptions, setLoadingOptions] = useState(true);\n\n  const isReadonly = mode === 'view';\n  const isEdit = mode === 'edit';\n  const isCreate = mode === 'create';\n\n  // 表单配置\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n    setValue,\n    watch\n  } = useForm<PromotionFormData>({\n    resolver: zodResolver(promotionSchema),\n    defaultValues: {\n      competitor_id: initialData?.competitor_id || 0,\n      ticket_type_id: initialData?.ticket_type_id || 0,\n      activity_name: initialData?.activity_name || '',\n      rack_rate: initialData?.rack_rate || null,\n      promo_price: initialData?.promo_price || null,\n      sale_start_date: initialData?.sale_start_date || '',\n      sale_end_date: initialData?.sale_end_date || '',\n      use_start_date: initialData?.use_start_date || '',\n      use_end_date: initialData?.use_end_date || '',\n      sales_channel: initialData?.sales_channel || '',\n      usage_rules: initialData?.usage_rules || '',\n      data_source_url: initialData?.data_source_url || '',\n      remarks: initialData?.remarks || ''\n    }\n  });\n\n  // 监听价格变化计算折扣\n  const rackRate = watch('rack_rate');\n  const promoPrice = watch('promo_price');\n\n  // 加载竞品和票种选项\n  useEffect(() => {\n    const loadOptions = async () => {\n      try {\n        setLoadingOptions(true);\n        \n        // 并行加载竞品和票种\n        const [competitorsRes, ticketTypesRes] = await Promise.all([\n          fetch('/api/competitors?pageSize=1000'),\n          fetch('/api/ticket-types?pageSize=1000')\n        ]);\n\n        const [competitorsResult, ticketTypesResult] = await Promise.all([\n          competitorsRes.json(),\n          ticketTypesRes.json()\n        ]);\n\n        if (competitorsResult.success) {\n          setCompetitors(competitorsResult.data.data || []);\n        }\n\n        if (ticketTypesResult.success) {\n          setTicketTypes(ticketTypesResult.data.data || []);\n        }\n      } catch (error) {\n        console.error('加载选项失败:', error);\n      } finally {\n        setLoadingOptions(false);\n      }\n    };\n\n    loadOptions();\n  }, []);\n\n  // 重置表单数据\n  useEffect(() => {\n    if (initialData) {\n      reset({\n        competitor_id: initialData.competitor_id,\n        ticket_type_id: initialData.ticket_type_id,\n        activity_name: initialData.activity_name,\n        rack_rate: initialData.rack_rate,\n        promo_price: initialData.promo_price,\n        sale_start_date: initialData.sale_start_date || '',\n        sale_end_date: initialData.sale_end_date || '',\n        use_start_date: initialData.use_start_date || '',\n        use_end_date: initialData.use_end_date || '',\n        sales_channel: initialData.sales_channel || '',\n        usage_rules: initialData.usage_rules || '',\n        data_source_url: initialData.data_source_url || '',\n        remarks: initialData.remarks || ''\n      });\n    }\n  }, [initialData, reset]);\n\n  // 表单提交处理\n  const handleFormSubmit = async (data: PromotionFormData) => {\n    try {\n      setSubmitError('');\n      setSubmitSuccess('');\n\n      // 清理和转换数据\n      const cleanedData = {\n        ...data,\n        rack_rate: data.rack_rate || null,\n        promo_price: data.promo_price || null,\n        sale_start_date: data.sale_start_date || null,\n        sale_end_date: data.sale_end_date || null,\n        use_start_date: data.use_start_date || null,\n        use_end_date: data.use_end_date || null,\n        sales_channel: data.sales_channel?.trim() || null,\n        usage_rules: data.usage_rules?.trim() || null,\n        data_source_url: data.data_source_url?.trim() || null,\n        remarks: data.remarks?.trim() || null\n      };\n\n      await onSubmit(cleanedData);\n\n      if (isCreate) {\n        setSubmitSuccess('促销活动创建成功！');\n        reset(); // 重置表单\n      } else if (isEdit) {\n        setSubmitSuccess('促销活动更新成功！');\n      }\n    } catch (error) {\n      setSubmitError(error instanceof Error ? error.message : '操作失败，请重试');\n    }\n  };\n\n  // 计算折扣率\n  const calculateDiscount = () => {\n    if (rackRate && promoPrice && rackRate > 0) {\n      const discount = ((rackRate - promoPrice) / rackRate * 100);\n      return discount.toFixed(1);\n    }\n    return null;\n  };\n\n  // 获取表单标题\n  const getTitle = () => {\n    switch (mode) {\n      case 'create':\n        return '创建促销活动';\n      case 'edit':\n        return '编辑促销活动';\n      case 'view':\n        return '查看促销活动';\n      default:\n        return '促销活动信息';\n    }\n  };\n\n  // 获取表单描述\n  const getDescription = () => {\n    switch (mode) {\n      case 'create':\n        return '填写促销活动详细信息，创建新的促销记录';\n      case 'edit':\n        return '修改促销活动信息，更新现有记录';\n      case 'view':\n        return '查看促销活动的详细信息';\n      default:\n        return '';\n    }\n  };\n\n  return (\n    <div className={className}>\n      <DialogHeader>\n        <DialogTitle>{getTitle()}</DialogTitle>\n        <DialogDescription>{getDescription()}</DialogDescription>\n      </DialogHeader>\n\n      <form onSubmit={handleSubmit(handleFormSubmit)} className=\"space-y-6\">\n        {/* 错误提示 */}\n        {submitError && (\n          <Alert variant=\"destructive\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{submitError}</AlertDescription>\n          </Alert>\n        )}\n\n        {/* 成功提示 */}\n        {submitSuccess && (\n          <Alert className=\"border-green-200 bg-green-50 text-green-800\">\n            <CheckCircle2 className=\"h-4 w-4\" />\n            <AlertDescription>{submitSuccess}</AlertDescription>\n          </Alert>\n        )}\n\n        <div className=\"grid gap-6\">\n          {/* 基本信息 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {/* 竞品选择 */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"competitor_id\" className=\"text-sm font-medium\">\n                竞品 <span className=\"text-red-500\">*</span>\n              </Label>\n              <Select\n                disabled={isReadonly || loading || loadingOptions}\n                onValueChange={(value) => setValue('competitor_id', parseInt(value))}\n                defaultValue={initialData?.competitor_id?.toString()}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"请选择竞品\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {competitors.map((competitor) => (\n                    <SelectItem key={competitor.competitor_id} value={competitor.competitor_id.toString()}>\n                      {competitor.competitor_name}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {errors.competitor_id && (\n                <p className=\"text-sm text-red-500\">{errors.competitor_id.message}</p>\n              )}\n            </div>\n\n            {/* 票种选择 */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"ticket_type_id\" className=\"text-sm font-medium\">\n                票种 <span className=\"text-red-500\">*</span>\n              </Label>\n              <Select\n                disabled={isReadonly || loading || loadingOptions}\n                onValueChange={(value) => setValue('ticket_type_id', parseInt(value))}\n                defaultValue={initialData?.ticket_type_id?.toString()}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"请选择票种\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {ticketTypes.map((ticketType) => (\n                    <SelectItem key={ticketType.ticket_type_id} value={ticketType.ticket_type_id.toString()}>\n                      {ticketType.ticket_type_name}\n                      {ticketType.category && ` (${ticketType.category})`}\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              {errors.ticket_type_id && (\n                <p className=\"text-sm text-red-500\">{errors.ticket_type_id.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* 活动名称 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"activity_name\" className=\"text-sm font-medium\">\n              活动名称 <span className=\"text-red-500\">*</span>\n            </Label>\n            <Input\n              id=\"activity_name\"\n              {...register('activity_name')}\n              placeholder=\"请输入活动名称\"\n              disabled={isReadonly || loading}\n              className={errors.activity_name ? 'border-red-500' : ''}\n            />\n            {errors.activity_name && (\n              <p className=\"text-sm text-red-500\">{errors.activity_name.message}</p>\n            )}\n          </div>\n\n          {/* 价格信息 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"rack_rate\" className=\"text-sm font-medium flex items-center gap-2\">\n                <DollarSign className=\"h-4 w-4\" />\n                原价 (¥)\n              </Label>\n              <Input\n                id=\"rack_rate\"\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                {...register('rack_rate', { valueAsNumber: true })}\n                placeholder=\"0.00\"\n                disabled={isReadonly || loading}\n                className={errors.rack_rate ? 'border-red-500' : ''}\n              />\n              {errors.rack_rate && (\n                <p className=\"text-sm text-red-500\">{errors.rack_rate.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"promo_price\" className=\"text-sm font-medium flex items-center gap-2\">\n                <DollarSign className=\"h-4 w-4\" />\n                促销价 (¥)\n              </Label>\n              <Input\n                id=\"promo_price\"\n                type=\"number\"\n                step=\"0.01\"\n                min=\"0\"\n                {...register('promo_price', { valueAsNumber: true })}\n                placeholder=\"0.00\"\n                disabled={isReadonly || loading}\n                className={errors.promo_price ? 'border-red-500' : ''}\n              />\n              {errors.promo_price && (\n                <p className=\"text-sm text-red-500\">{errors.promo_price.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label className=\"text-sm font-medium\">折扣率</Label>\n              <div className=\"h-10 px-3 py-2 border rounded-md bg-gray-50 flex items-center\">\n                <span className=\"text-sm text-gray-600\">\n                  {calculateDiscount() ? `${calculateDiscount()}%` : '-'}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* 销售时间 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"sale_start_date\" className=\"text-sm font-medium flex items-center gap-2\">\n                <Calendar className=\"h-4 w-4\" />\n                销售开始日期\n              </Label>\n              <Input\n                id=\"sale_start_date\"\n                type=\"date\"\n                {...register('sale_start_date')}\n                disabled={isReadonly || loading}\n                className={errors.sale_start_date ? 'border-red-500' : ''}\n              />\n              {errors.sale_start_date && (\n                <p className=\"text-sm text-red-500\">{errors.sale_start_date.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"sale_end_date\" className=\"text-sm font-medium flex items-center gap-2\">\n                <Calendar className=\"h-4 w-4\" />\n                销售结束日期\n              </Label>\n              <Input\n                id=\"sale_end_date\"\n                type=\"date\"\n                {...register('sale_end_date')}\n                disabled={isReadonly || loading}\n                className={errors.sale_end_date ? 'border-red-500' : ''}\n              />\n              {errors.sale_end_date && (\n                <p className=\"text-sm text-red-500\">{errors.sale_end_date.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* 使用时间 */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"use_start_date\" className=\"text-sm font-medium flex items-center gap-2\">\n                <Calendar className=\"h-4 w-4\" />\n                使用开始日期\n              </Label>\n              <Input\n                id=\"use_start_date\"\n                type=\"date\"\n                {...register('use_start_date')}\n                disabled={isReadonly || loading}\n                className={errors.use_start_date ? 'border-red-500' : ''}\n              />\n              {errors.use_start_date && (\n                <p className=\"text-sm text-red-500\">{errors.use_start_date.message}</p>\n              )}\n            </div>\n\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"use_end_date\" className=\"text-sm font-medium flex items-center gap-2\">\n                <Calendar className=\"h-4 w-4\" />\n                使用结束日期\n              </Label>\n              <Input\n                id=\"use_end_date\"\n                type=\"date\"\n                {...register('use_end_date')}\n                disabled={isReadonly || loading}\n                className={errors.use_end_date ? 'border-red-500' : ''}\n              />\n              {errors.use_end_date && (\n                <p className=\"text-sm text-red-500\">{errors.use_end_date.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* 销售渠道 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"sales_channel\" className=\"text-sm font-medium\">\n              销售渠道\n            </Label>\n            <Input\n              id=\"sales_channel\"\n              {...register('sales_channel')}\n              placeholder=\"例如：官网、APP、第三方平台等\"\n              disabled={isReadonly || loading}\n              className={errors.sales_channel ? 'border-red-500' : ''}\n            />\n            {errors.sales_channel && (\n              <p className=\"text-sm text-red-500\">{errors.sales_channel.message}</p>\n            )}\n          </div>\n\n          {/* 使用规则 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"usage_rules\" className=\"text-sm font-medium\">\n              使用规则\n            </Label>\n            <Textarea\n              id=\"usage_rules\"\n              {...register('usage_rules')}\n              placeholder=\"请输入使用规则和限制条件\"\n              disabled={isReadonly || loading}\n              className={errors.usage_rules ? 'border-red-500' : ''}\n              rows={3}\n            />\n            {errors.usage_rules && (\n              <p className=\"text-sm text-red-500\">{errors.usage_rules.message}</p>\n            )}\n          </div>\n\n          {/* 数据来源URL */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"data_source_url\" className=\"text-sm font-medium\">\n              数据来源URL\n            </Label>\n            <Input\n              id=\"data_source_url\"\n              type=\"url\"\n              {...register('data_source_url')}\n              placeholder=\"https://example.com/promotion\"\n              disabled={isReadonly || loading}\n              className={errors.data_source_url ? 'border-red-500' : ''}\n            />\n            {errors.data_source_url && (\n              <p className=\"text-sm text-red-500\">{errors.data_source_url.message}</p>\n            )}\n          </div>\n\n          {/* 备注 */}\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"remarks\" className=\"text-sm font-medium\">\n              备注\n            </Label>\n            <Textarea\n              id=\"remarks\"\n              {...register('remarks')}\n              placeholder=\"请输入备注信息\"\n              disabled={isReadonly || loading}\n              className={errors.remarks ? 'border-red-500' : ''}\n              rows={3}\n            />\n            {errors.remarks && (\n              <p className=\"text-sm text-red-500\">{errors.remarks.message}</p>\n            )}\n          </div>\n\n          {/* 只读模式显示额外信息 */}\n          {isReadonly && initialData && (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t\">\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-gray-600\">活动ID</Label>\n                <div className=\"text-sm text-gray-900\">{initialData.promotion_id}</div>\n              </div>\n              <div className=\"space-y-2\">\n                <Label className=\"text-sm font-medium text-gray-600\">录入时间</Label>\n                <div className=\"text-sm text-gray-900\">\n                  {initialData.entry_date ? new Date(initialData.entry_date).toLocaleString() : '-'}\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        <DialogFooter>\n          {!isReadonly && (\n            <Button\n              type=\"submit\"\n              disabled={loading || isSubmitting || loadingOptions}\n              className=\"min-w-[100px]\"\n            >\n              {(loading || isSubmitting) && (\n                <Loader2 className=\"mr-2 h-4 w-4 animate-spin\" />\n              )}\n              {isCreate ? '创建活动' : '保存更改'}\n            </Button>\n          )}\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            onClick={onCancel}\n            disabled={loading || isSubmitting}\n          >\n            {isReadonly ? '关闭' : '取消'}\n          </Button>\n        </DialogFooter>\n      </form>\n    </div>\n  );\n};\n\n// ============================================================================\n// 导出组件\n// ============================================================================\n\nexport default PromotionForm;\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;;AAID;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAbA;;;;;;;;;;;;;AAgBA,+EAA+E;AAC/E,SAAS;AACT,+EAA+E;AAE/E,MAAM,kBAAkB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/B,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACjC,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAClC,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,GACpB,GAAG,CAAC,GAAG,YACP,GAAG,CAAC,KAAK;IACZ,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAChB,GAAG,CAAC,GAAG,WACP,QAAQ,GACR,QAAQ;IACX,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,GAAG,YACP,QAAQ,GACR,QAAQ;IACX,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7C,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,GACpB,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,QAAQ;IACX,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAClB,GAAG,CAAC,KAAK,kBACT,QAAQ,GACR,QAAQ;IACX,iBAAiB,gLAAA,CAAA,IAAC,CAAC,MAAM,GACtB,MAAM,CAAC,CAAC,MAAQ,CAAC,OAAO,iBAAiB,IAAI,CAAC,MAAM,aACpD,MAAM,CAAC,CAAC,MAAQ,CAAC,OAAO,IAAI,MAAM,IAAI,KAAK,iBAC3C,QAAQ,GACR,EAAE,CAAC,gLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IAChB,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GACd,GAAG,CAAC,MAAM,iBACV,QAAQ,GACR,QAAQ;AACb;AAqBO,MAAM,gBAA8C;QAAC,EAC1D,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,QAAQ,EACR,UAAU,KAAK,EACf,SAAS,EACV;QA2M6B,4BA0BA;;IApO5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,aAAa,SAAS;IAC5B,MAAM,SAAS,SAAS;IACxB,MAAM,WAAW,SAAS;IAE1B,OAAO;IACP,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACL,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAqB;QAC7B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,eAAe,CAAA,wBAAA,kCAAA,YAAa,aAAa,KAAI;YAC7C,gBAAgB,CAAA,wBAAA,kCAAA,YAAa,cAAc,KAAI;YAC/C,eAAe,CAAA,wBAAA,kCAAA,YAAa,aAAa,KAAI;YAC7C,WAAW,CAAA,wBAAA,kCAAA,YAAa,SAAS,KAAI;YACrC,aAAa,CAAA,wBAAA,kCAAA,YAAa,WAAW,KAAI;YACzC,iBAAiB,CAAA,wBAAA,kCAAA,YAAa,eAAe,KAAI;YACjD,eAAe,CAAA,wBAAA,kCAAA,YAAa,aAAa,KAAI;YAC7C,gBAAgB,CAAA,wBAAA,kCAAA,YAAa,cAAc,KAAI;YAC/C,cAAc,CAAA,wBAAA,kCAAA,YAAa,YAAY,KAAI;YAC3C,eAAe,CAAA,wBAAA,kCAAA,YAAa,aAAa,KAAI;YAC7C,aAAa,CAAA,wBAAA,kCAAA,YAAa,WAAW,KAAI;YACzC,iBAAiB,CAAA,wBAAA,kCAAA,YAAa,eAAe,KAAI;YACjD,SAAS,CAAA,wBAAA,kCAAA,YAAa,OAAO,KAAI;QACnC;IACF;IAEA,aAAa;IACb,MAAM,WAAW,MAAM;IACvB,MAAM,aAAa,MAAM;IAEzB,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;uDAAc;oBAClB,IAAI;wBACF,kBAAkB;wBAElB,YAAY;wBACZ,MAAM,CAAC,gBAAgB,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;4BACzD,MAAM;4BACN,MAAM;yBACP;wBAED,MAAM,CAAC,mBAAmB,kBAAkB,GAAG,MAAM,QAAQ,GAAG,CAAC;4BAC/D,eAAe,IAAI;4BACnB,eAAe,IAAI;yBACpB;wBAED,IAAI,kBAAkB,OAAO,EAAE;4BAC7B,eAAe,kBAAkB,IAAI,CAAC,IAAI,IAAI,EAAE;wBAClD;wBAEA,IAAI,kBAAkB,OAAO,EAAE;4BAC7B,eAAe,kBAAkB,IAAI,CAAC,IAAI,IAAI,EAAE;wBAClD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,WAAW;oBAC3B,SAAU;wBACR,kBAAkB;oBACpB;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,SAAS;IACT,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,aAAa;gBACf,MAAM;oBACJ,eAAe,YAAY,aAAa;oBACxC,gBAAgB,YAAY,cAAc;oBAC1C,eAAe,YAAY,aAAa;oBACxC,WAAW,YAAY,SAAS;oBAChC,aAAa,YAAY,WAAW;oBACpC,iBAAiB,YAAY,eAAe,IAAI;oBAChD,eAAe,YAAY,aAAa,IAAI;oBAC5C,gBAAgB,YAAY,cAAc,IAAI;oBAC9C,cAAc,YAAY,YAAY,IAAI;oBAC1C,eAAe,YAAY,aAAa,IAAI;oBAC5C,aAAa,YAAY,WAAW,IAAI;oBACxC,iBAAiB,YAAY,eAAe,IAAI;oBAChD,SAAS,YAAY,OAAO,IAAI;gBAClC;YACF;QACF;kCAAG;QAAC;QAAa;KAAM;IAEvB,SAAS;IACT,MAAM,mBAAmB,OAAO;QAC9B,IAAI;gBAae,qBACF,mBACI,uBACR;YAfX,eAAe;YACf,iBAAiB;YAEjB,UAAU;YACV,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP,WAAW,KAAK,SAAS,IAAI;gBAC7B,aAAa,KAAK,WAAW,IAAI;gBACjC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,eAAe,KAAK,aAAa,IAAI;gBACrC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,cAAc,KAAK,YAAY,IAAI;gBACnC,eAAe,EAAA,sBAAA,KAAK,aAAa,cAAlB,0CAAA,oBAAoB,IAAI,OAAM;gBAC7C,aAAa,EAAA,oBAAA,KAAK,WAAW,cAAhB,wCAAA,kBAAkB,IAAI,OAAM;gBACzC,iBAAiB,EAAA,wBAAA,KAAK,eAAe,cAApB,4CAAA,sBAAsB,IAAI,OAAM;gBACjD,SAAS,EAAA,gBAAA,KAAK,OAAO,cAAZ,oCAAA,cAAc,IAAI,OAAM;YACnC;YAEA,MAAM,SAAS;YAEf,IAAI,UAAU;gBACZ,iBAAiB;gBACjB,SAAS,OAAO;YAClB,OAAO,IAAI,QAAQ;gBACjB,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC1D;IACF;IAEA,QAAQ;IACR,MAAM,oBAAoB;QACxB,IAAI,YAAY,cAAc,WAAW,GAAG;YAC1C,MAAM,WAAY,CAAC,WAAW,UAAU,IAAI,WAAW;YACvD,OAAO,SAAS,OAAO,CAAC;QAC1B;QACA,OAAO;IACT;IAEA,SAAS;IACT,MAAM,WAAW;QACf,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,SAAS;IACT,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW;;0BACd,6LAAC,qIAAA,CAAA,eAAY;;kCACX,6LAAC,qIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,6LAAC,qIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAGtB,6LAAC;gBAAK,UAAU,aAAa;gBAAmB,WAAU;;oBAEvD,6BACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC,oIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;oBAKtB,+BACC,6LAAC,oIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,6LAAC,wNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,6LAAC,oIAAA,CAAA,mBAAgB;0CAAE;;;;;;;;;;;;kCAIvB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAgB,WAAU;;oDAAsB;kEAC1D,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEpC,6LAAC,qIAAA,CAAA,SAAM;gDACL,UAAU,cAAc,WAAW;gDACnC,eAAe,CAAC,QAAU,SAAS,iBAAiB,SAAS;gDAC7D,YAAY,EAAE,wBAAA,mCAAA,6BAAA,YAAa,aAAa,cAA1B,iDAAA,2BAA4B,QAAQ;;kEAElD,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;kEACX,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC,qIAAA,CAAA,aAAU;gEAAgC,OAAO,WAAW,aAAa,CAAC,QAAQ;0EAChF,WAAW,eAAe;+DADZ,WAAW,aAAa;;;;;;;;;;;;;;;;4CAM9C,OAAO,aAAa,kBACnB,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;kDAKrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAiB,WAAU;;oDAAsB;kEAC3D,6LAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;0DAEpC,6LAAC,qIAAA,CAAA,SAAM;gDACL,UAAU,cAAc,WAAW;gDACnC,eAAe,CAAC,QAAU,SAAS,kBAAkB,SAAS;gDAC9D,YAAY,EAAE,wBAAA,mCAAA,8BAAA,YAAa,cAAc,cAA3B,kDAAA,4BAA6B,QAAQ;;kEAEnD,6LAAC,qIAAA,CAAA,gBAAa;kEACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,6LAAC,qIAAA,CAAA,gBAAa;kEACX,YAAY,GAAG,CAAC,CAAC,2BAChB,6LAAC,qIAAA,CAAA,aAAU;gEAAiC,OAAO,WAAW,cAAc,CAAC,QAAQ;;oEAClF,WAAW,gBAAgB;oEAC3B,WAAW,QAAQ,IAAI,AAAC,KAAwB,OAApB,WAAW,QAAQ,EAAC;;+DAFlC,WAAW,cAAc;;;;;;;;;;;;;;;;4CAO/C,OAAO,cAAc,kBACpB,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAMxE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAgB,WAAU;;4CAAsB;0DACxD,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEtC,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACF,GAAG,SAAS,gBAAgB;wCAC7B,aAAY;wCACZ,UAAU,cAAc;wCACxB,WAAW,OAAO,aAAa,GAAG,mBAAmB;;;;;;oCAEtD,OAAO,aAAa,kBACnB,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;0CAKrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;;kEACnC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGpC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACH,GAAG,SAAS,aAAa;oDAAE,eAAe;gDAAK,EAAE;gDAClD,aAAY;gDACZ,UAAU,cAAc;gDACxB,WAAW,OAAO,SAAS,GAAG,mBAAmB;;;;;;4CAElD,OAAO,SAAS,kBACf,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kDAIjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAc,WAAU;;kEACrC,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGpC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,MAAK;gDACL,KAAI;gDACH,GAAG,SAAS,eAAe;oDAAE,eAAe;gDAAK,EAAE;gDACpD,aAAY;gDACZ,UAAU,cAAc;gDACxB,WAAW,OAAO,WAAW,GAAG,mBAAmB;;;;;;4CAEpD,OAAO,WAAW,kBACjB,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;kDAInE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAsB;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,sBAAsB,AAAC,GAAsB,OAApB,qBAAoB,OAAK;;;;;;;;;;;;;;;;;;;;;;;0CAO3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAkB,WAAU;;kEACzC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACJ,GAAG,SAAS,kBAAkB;gDAC/B,UAAU,cAAc;gDACxB,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;4CAExD,OAAO,eAAe,kBACrB,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;kDAIvE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAgB,WAAU;;kEACvC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACJ,GAAG,SAAS,gBAAgB;gDAC7B,UAAU,cAAc;gDACxB,WAAW,OAAO,aAAa,GAAG,mBAAmB;;;;;;4CAEtD,OAAO,aAAa,kBACnB,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAMvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAiB,WAAU;;kEACxC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACJ,GAAG,SAAS,iBAAiB;gDAC9B,UAAU,cAAc;gDACxB,WAAW,OAAO,cAAc,GAAG,mBAAmB;;;;;;4CAEvD,OAAO,cAAc,kBACpB,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;kDAItE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;;kEACtC,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACJ,GAAG,SAAS,eAAe;gDAC5B,UAAU,cAAc;gDACxB,WAAW,OAAO,YAAY,GAAG,mBAAmB;;;;;;4CAErD,OAAO,YAAY,kBAClB,6LAAC;gDAAE,WAAU;0DAAwB,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAMtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAgB,WAAU;kDAAsB;;;;;;kDAG/D,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACF,GAAG,SAAS,gBAAgB;wCAC7B,aAAY;wCACZ,UAAU,cAAc;wCACxB,WAAW,OAAO,aAAa,GAAG,mBAAmB;;;;;;oCAEtD,OAAO,aAAa,kBACnB,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,aAAa,CAAC,OAAO;;;;;;;;;;;;0CAKrE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAc,WAAU;kDAAsB;;;;;;kDAG7D,6LAAC;wCACC,IAAG;wCACF,GAAG,SAAS,cAAc;wCAC3B,aAAY;wCACZ,UAAU,cAAc;wCACxB,WAAW,OAAO,WAAW,GAAG,mBAAmB;wCACnD,MAAM;;;;;;oCAEP,OAAO,WAAW,kBACjB,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;0CAKnE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAkB,WAAU;kDAAsB;;;;;;kDAGjE,6LAAC,oIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,MAAK;wCACJ,GAAG,SAAS,kBAAkB;wCAC/B,aAAY;wCACZ,UAAU,cAAc;wCACxB,WAAW,OAAO,eAAe,GAAG,mBAAmB;;;;;;oCAExD,OAAO,eAAe,kBACrB,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;0CAKvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAsB;;;;;;kDAGzD,6LAAC;wCACC,IAAG;wCACF,GAAG,SAAS,UAAU;wCACvB,aAAY;wCACZ,UAAU,cAAc;wCACxB,WAAW,OAAO,OAAO,GAAG,mBAAmB;wCAC/C,MAAM;;;;;;oCAEP,OAAO,OAAO,kBACb,6LAAC;wCAAE,WAAU;kDAAwB,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;4BAK9D,cAAc,6BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DAAyB,YAAY,YAAY;;;;;;;;;;;;kDAElE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAoC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DACZ,YAAY,UAAU,GAAG,IAAI,KAAK,YAAY,UAAU,EAAE,cAAc,KAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOxF,6LAAC,qIAAA,CAAA,eAAY;;4BACV,CAAC,4BACA,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,WAAW,gBAAgB;gCACrC,WAAU;;oCAET,CAAC,WAAW,YAAY,mBACvB,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAEpB,WAAW,SAAS;;;;;;;0CAGzB,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU,WAAW;0CAEpB,aAAa,OAAO;;;;;;;;;;;;;;;;;;;;;;;;AAMjC;GAhgBa;;QA0BP,iKAAA,CAAA,UAAO;;;KA1BA;uCAsgBE", "debugId": null}}, {"offset": {"line": 4673, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/competitor_analysis/competitor-analysis-dashboard/src/app/promotions/page.tsx"], "sourcesContent": ["/**\n * 促销活动管理页面\n * \n * 功能说明：\n * 1. 展示促销活动列表，支持分页、搜索、筛选\n * 2. 提供促销活动的增删改查功能\n * 3. 响应式设计，适配移动端\n * 4. 复杂的筛选条件支持\n */\n\n'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Plus, Search, Filter, Edit, Trash2, Eye, Calendar, DollarSign } from 'lucide-react';\nimport { MainLayout, PageContainer } from '@/components/layout/main-layout';\nimport { DataTable, ColumnDef } from '@/components/ui/data-table';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Badge } from '@/components/ui/badge';\nimport { LoadingPage } from '@/components/ui/loading';\nimport { DeleteConfirmDialog } from '@/components/ui/confirm-dialog';\nimport { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';\nimport { PromotionForm } from '@/components/forms/promotion-form';\nimport { Promotion, PaginatedResult, CreatePromotionInput, UpdatePromotionInput } from '@/types';\n\n// ============================================================================\n// 页面状态接口定义\n// ============================================================================\n\ninterface PromotionsPageState {\n  data: PaginatedResult<Promotion> | null;\n  loading: boolean;\n  error: string | null;\n  searchText: string;\n  currentPage: number;\n  pageSize: number;\n}\n\ninterface DialogState {\n  type: 'create' | 'edit' | 'view' | null;\n  open: boolean;\n  data?: Promotion;\n}\n\ninterface DeleteState {\n  open: boolean;\n  promotion: Promotion | null;\n}\n\n// ============================================================================\n// 促销活动管理页面组件\n// ============================================================================\n\nexport default function PromotionsPage() {\n  // 页面状态\n  const [state, setState] = useState<PromotionsPageState>({\n    data: null,\n    loading: true,\n    error: null,\n    searchText: '',\n    currentPage: 1,\n    pageSize: 10\n  });\n\n  // 对话框状态\n  const [dialogState, setDialogState] = useState<DialogState>({\n    type: null,\n    open: false\n  });\n\n  // 删除确认状态\n  const [deleteState, setDeleteState] = useState<DeleteState>({\n    open: false,\n    promotion: null\n  });\n\n  // 表单加载状态\n  const [formLoading, setFormLoading] = useState(false);\n\n  // 获取促销活动列表\n  const fetchPromotions = async (page = state.currentPage, pageSize = state.pageSize, search = state.searchText) => {\n    try {\n      setState(prev => ({ ...prev, loading: true, error: null }));\n\n      const params = new URLSearchParams({\n        page: page.toString(),\n        pageSize: pageSize.toString()\n      });\n\n      if (search.trim()) {\n        params.append('activity_name', search.trim());\n      }\n\n      const response = await fetch(`/api/promotions?${params}`);\n      const result = await response.json();\n\n      if (result.success) {\n        setState(prev => ({\n          ...prev,\n          data: result.data,\n          loading: false,\n          currentPage: page,\n          pageSize: pageSize,\n          searchText: search\n        }));\n      } else {\n        throw new Error(result.error?.message || '获取促销活动列表失败');\n      }\n    } catch (error) {\n      setState(prev => ({\n        ...prev,\n        loading: false,\n        error: error instanceof Error ? error.message : '未知错误'\n      }));\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchPromotions();\n  }, []);\n\n  // 搜索处理\n  const handleSearch = () => {\n    fetchPromotions(1, state.pageSize, state.searchText);\n  };\n\n  // 分页处理\n  const handlePageChange = (page: number, pageSize?: number) => {\n    fetchPromotions(page, pageSize || state.pageSize, state.searchText);\n  };\n\n  // 打开对话框\n  const openDialog = (type: 'create' | 'edit' | 'view', data?: Promotion) => {\n    setDialogState({\n      type,\n      open: true,\n      data\n    });\n  };\n\n  // 关闭对话框\n  const closeDialog = () => {\n    setDialogState({\n      type: null,\n      open: false,\n      data: undefined\n    });\n  };\n\n  // 表单提交处理\n  const handleFormSubmit = async (data: CreatePromotionInput | UpdatePromotionInput) => {\n    try {\n      setFormLoading(true);\n\n      const isEdit = dialogState.type === 'edit';\n      const url = isEdit ? `/api/promotions/${dialogState.data?.promotion_id}` : '/api/promotions';\n      const method = isEdit ? 'PUT' : 'POST';\n\n      const response = await fetch(url, {\n        method,\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(data)\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        closeDialog();\n        fetchPromotions(); // 刷新列表\n      } else {\n        throw new Error(result.error?.message || '操作失败');\n      }\n    } catch (error) {\n      throw error; // 让表单组件处理错误显示\n    } finally {\n      setFormLoading(false);\n    }\n  };\n\n  // 删除促销活动\n  const handleDelete = async (promotion: Promotion) => {\n    try {\n      const response = await fetch(`/api/promotions/${promotion.promotion_id}`, {\n        method: 'DELETE'\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        setDeleteState({ open: false, promotion: null });\n        fetchPromotions(); // 刷新列表\n      } else {\n        throw new Error(result.error?.message || '删除失败');\n      }\n    } catch (error) {\n      console.error('删除促销活动失败:', error);\n      // 这里可以添加错误提示\n    }\n  };\n\n  // 格式化价格\n  const formatPrice = (price: number | null) => {\n    if (price === null || price === undefined) return '-';\n    return `¥${price.toFixed(2)}`;\n  };\n\n  // 格式化日期\n  const formatDate = (date: string | null) => {\n    if (!date) return '-';\n    return new Date(date).toLocaleDateString('zh-CN');\n  };\n\n  // 计算折扣率\n  const calculateDiscount = (rackRate: number | null, promoPrice: number | null) => {\n    if (!rackRate || !promoPrice || rackRate <= 0) return '-';\n    const discount = ((rackRate - promoPrice) / rackRate * 100);\n    return `${discount.toFixed(1)}%`;\n  };\n\n  // 获取活动状态\n  const getActivityStatus = (promotion: Promotion) => {\n    const now = new Date();\n    const saleStart = promotion.sale_start_date ? new Date(promotion.sale_start_date) : null;\n    const saleEnd = promotion.sale_end_date ? new Date(promotion.sale_end_date) : null;\n\n    if (saleEnd && saleEnd < now) {\n      return <Badge variant=\"secondary\">已结束</Badge>;\n    }\n    if (saleStart && saleStart > now) {\n      return <Badge variant=\"outline\">未开始</Badge>;\n    }\n    return <Badge variant=\"default\">进行中</Badge>;\n  };\n\n  // 表格列定义\n  const columns: ColumnDef<Promotion>[] = [\n    {\n      key: 'activity_name',\n      title: '活动名称',\n      dataIndex: 'activity_name',\n      sorter: true,\n      render: (value: string) => (\n        <span className=\"font-medium text-gray-900\">{value}</span>\n      )\n    },\n    {\n      key: 'competitor',\n      title: '竞品',\n      render: (_, record: Promotion) => (\n        <span className=\"text-sm text-gray-600\">\n          {record.competitor?.competitor_name || '-'}\n        </span>\n      )\n    },\n    {\n      key: 'ticket_type',\n      title: '票种',\n      render: (_, record: Promotion) => (\n        <span className=\"text-sm text-gray-600\">\n          {record.ticket_type?.ticket_type_name || '-'}\n        </span>\n      )\n    },\n    {\n      key: 'price',\n      title: '价格',\n      render: (_, record: Promotion) => (\n        <div className=\"text-sm\">\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-gray-500\">原价:</span>\n            <span>{formatPrice(record.rack_rate)}</span>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-gray-500\">促销:</span>\n            <span className=\"font-medium text-red-600\">{formatPrice(record.promo_price)}</span>\n          </div>\n        </div>\n      )\n    },\n    {\n      key: 'discount',\n      title: '折扣',\n      render: (_, record: Promotion) => (\n        <Badge variant=\"outline\">\n          {calculateDiscount(record.rack_rate, record.promo_price)}\n        </Badge>\n      )\n    },\n    {\n      key: 'sale_period',\n      title: '销售期',\n      render: (_, record: Promotion) => (\n        <div className=\"text-xs text-gray-600\">\n          <div>{formatDate(record.sale_start_date)}</div>\n          <div>至</div>\n          <div>{formatDate(record.sale_end_date)}</div>\n        </div>\n      )\n    },\n    {\n      key: 'status',\n      title: '状态',\n      render: (_, record: Promotion) => getActivityStatus(record)\n    },\n    {\n      key: 'actions',\n      title: '操作',\n      width: 120,\n      render: (_, record: Promotion) => (\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => openDialog('view', record)}\n          >\n            <Eye className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => openDialog('edit', record)}\n          >\n            <Edit className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setDeleteState({ open: true, promotion: record })}\n          >\n            <Trash2 className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      )\n    }\n  ];\n\n  // 加载状态\n  if (state.loading && !state.data) {\n    return (\n      <MainLayout currentPath=\"/promotions\">\n        <LoadingPage text=\"加载促销活动数据...\" />\n      </MainLayout>\n    );\n  }\n\n  // 错误状态\n  if (state.error && !state.data) {\n    return (\n      <MainLayout currentPath=\"/promotions\">\n        <PageContainer>\n          <div className=\"text-center py-12\">\n            <div className=\"text-red-500 text-lg font-medium mb-4\">\n              {state.error}\n            </div>\n            <Button onClick={() => fetchPromotions()}>\n              重新加载\n            </Button>\n          </div>\n        </PageContainer>\n      </MainLayout>\n    );\n  }\n\n  return (\n    <MainLayout currentPath=\"/promotions\">\n      <PageContainer\n        title=\"促销活动管理\"\n        description=\"管理促销活动信息，包括活动的价格、时间和规则\"\n        actions={\n          <Button onClick={() => openDialog('create')}>\n            <Plus className=\"h-4 w-4 mr-2\" />\n            添加促销活动\n          </Button>\n        }\n      >\n        {/* 搜索和筛选 */}\n        <div className=\"flex gap-4 mb-6\">\n          <div className=\"flex-1 max-w-md\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n              <Input\n                placeholder=\"搜索活动名称...\"\n                value={state.searchText}\n                onChange={(e) => setState(prev => ({ ...prev, searchText: e.target.value }))}\n                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}\n                className=\"pl-9\"\n              />\n            </div>\n          </div>\n          <Button onClick={handleSearch} variant=\"outline\">\n            搜索\n          </Button>\n        </div>\n\n        {/* 数据表格 */}\n        <DataTable\n          columns={columns}\n          data={state.data?.data || []}\n          loading={state.loading}\n          pagination={state.data ? {\n            current: state.currentPage,\n            pageSize: state.pageSize,\n            total: state.data.total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            onChange: handlePageChange\n          } : undefined}\n          emptyText=\"暂无促销活动数据\"\n        />\n\n        {/* 表单对话框 */}\n        <Dialog open={dialogState.open} onOpenChange={closeDialog}>\n          <DialogContent className=\"max-w-4xl\">\n            <DialogTitle className=\"sr-only\">\n              {dialogState.type === 'create' && '添加促销活动'}\n              {dialogState.type === 'edit' && '编辑促销活动'}\n              {dialogState.type === 'view' && '查看促销活动'}\n            </DialogTitle>\n            <DialogDescription className=\"sr-only\">\n              {dialogState.type === 'create' && '创建新的促销活动信息'}\n              {dialogState.type === 'edit' && '编辑现有促销活动信息'}\n              {dialogState.type === 'view' && '查看促销活动详细信息'}\n            </DialogDescription>\n            {dialogState.type && (\n              <PromotionForm\n                mode={dialogState.type}\n                initialData={dialogState.data}\n                onSubmit={handleFormSubmit}\n                onCancel={closeDialog}\n                loading={formLoading}\n              />\n            )}\n          </DialogContent>\n        </Dialog>\n\n        {/* 删除确认对话框 */}\n        <DeleteConfirmDialog\n          open={deleteState.open}\n          onOpenChange={(open) => setDeleteState(prev => ({ ...prev, open }))}\n          itemName={deleteState.promotion?.activity_name}\n          itemType=\"促销活动\"\n          onConfirm={() => deleteState.promotion && handleDelete(deleteState.promotion)}\n        />\n      </PageContainer>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AA2Ce,SAAS;QAyVR,aA0CI;;IAlYlB,OAAO;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,MAAM;QACN,SAAS;QACT,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;IACZ;IAEA,QAAQ;IACR,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM;QACN,MAAM;IACR;IAEA,SAAS;IACT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM;QACN,WAAW;IACb;IAEA,SAAS;IACT,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,WAAW;IACX,MAAM,kBAAkB;YAAO,wEAAO,MAAM,WAAW,EAAE,4EAAW,MAAM,QAAQ,EAAE,0EAAS,MAAM,UAAU;QAC3G,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,UAAU,SAAS,QAAQ;YAC7B;YAEA,IAAI,OAAO,IAAI,IAAI;gBACjB,OAAO,MAAM,CAAC,iBAAiB,OAAO,IAAI;YAC5C;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAyB,OAAP;YAChD,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,MAAM,OAAO,IAAI;wBACjB,SAAS;wBACT,aAAa;wBACb,UAAU;wBACV,YAAY;oBACd,CAAC;YACH,OAAO;oBACW;gBAAhB,MAAM,IAAI,MAAM,EAAA,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,OAAO,KAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,SAAS;oBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF;IAEA,QAAQ;IACR,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,OAAO;IACP,MAAM,eAAe;QACnB,gBAAgB,GAAG,MAAM,QAAQ,EAAE,MAAM,UAAU;IACrD;IAEA,OAAO;IACP,MAAM,mBAAmB,CAAC,MAAc;QACtC,gBAAgB,MAAM,YAAY,MAAM,QAAQ,EAAE,MAAM,UAAU;IACpE;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC,MAAkC;QACpD,eAAe;YACb;YACA,MAAM;YACN;QACF;IACF;IAEA,QAAQ;IACR,MAAM,cAAc;QAClB,eAAe;YACb,MAAM;YACN,MAAM;YACN,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,mBAAmB,OAAO;QAC9B,IAAI;gBAIsC;YAHxC,eAAe;YAEf,MAAM,SAAS,YAAY,IAAI,KAAK;YACpC,MAAM,MAAM,SAAS,AAAC,mBAAiD,QAA/B,oBAAA,YAAY,IAAI,cAAhB,wCAAA,kBAAkB,YAAY,IAAK;YAC3E,MAAM,SAAS,SAAS,QAAQ;YAEhC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB;gBACA,mBAAmB,OAAO;YAC5B,OAAO;oBACW;gBAAhB,MAAM,IAAI,MAAM,EAAA,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,OAAO,KAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,MAAM,OAAO,cAAc;QAC7B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,SAAS;IACT,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,mBAAyC,OAAvB,UAAU,YAAY,GAAI;gBACxE,QAAQ;YACV;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,eAAe;oBAAE,MAAM;oBAAO,WAAW;gBAAK;gBAC9C,mBAAmB,OAAO;YAC5B,OAAO;oBACW;gBAAhB,MAAM,IAAI,MAAM,EAAA,gBAAA,OAAO,KAAK,cAAZ,oCAAA,cAAc,OAAO,KAAI;YAC3C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,aAAa;QACf;IACF;IAEA,QAAQ;IACR,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;QAClD,OAAO,AAAC,IAAoB,OAAjB,MAAM,OAAO,CAAC;IAC3B;IAEA,QAAQ;IACR,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,IAAI,KAAK,MAAM,kBAAkB,CAAC;IAC3C;IAEA,QAAQ;IACR,MAAM,oBAAoB,CAAC,UAAyB;QAClD,IAAI,CAAC,YAAY,CAAC,cAAc,YAAY,GAAG,OAAO;QACtD,MAAM,WAAY,CAAC,WAAW,UAAU,IAAI,WAAW;QACvD,OAAO,AAAC,GAAsB,OAApB,SAAS,OAAO,CAAC,IAAG;IAChC;IAEA,SAAS;IACT,MAAM,oBAAoB,CAAC;QACzB,MAAM,MAAM,IAAI;QAChB,MAAM,YAAY,UAAU,eAAe,GAAG,IAAI,KAAK,UAAU,eAAe,IAAI;QACpF,MAAM,UAAU,UAAU,aAAa,GAAG,IAAI,KAAK,UAAU,aAAa,IAAI;QAE9E,IAAI,WAAW,UAAU,KAAK;YAC5B,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAY;;;;;;QACpC;QACA,IAAI,aAAa,YAAY,KAAK;YAChC,qBAAO,6LAAC,oIAAA,CAAA,QAAK;gBAAC,SAAQ;0BAAU;;;;;;QAClC;QACA,qBAAO,6LAAC,oIAAA,CAAA,QAAK;YAAC,SAAQ;sBAAU;;;;;;IAClC;IAEA,QAAQ;IACR,MAAM,UAAkC;QACtC;YACE,KAAK;YACL,OAAO;YACP,WAAW;YACX,QAAQ;YACR,QAAQ,CAAC,sBACP,6LAAC;oBAAK,WAAU;8BAA6B;;;;;;QAEjD;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;oBAEP;qCADH,6LAAC;oBAAK,WAAU;8BACb,EAAA,qBAAA,OAAO,UAAU,cAAjB,yCAAA,mBAAmB,eAAe,KAAI;;;;;;;QAG7C;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG;oBAEP;qCADH,6LAAC;oBAAK,WAAU;8BACb,EAAA,sBAAA,OAAO,WAAW,cAAlB,0CAAA,oBAAoB,gBAAgB,KAAI;;;;;;;QAG/C;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;8CAAM,YAAY,OAAO,SAAS;;;;;;;;;;;;sCAErC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAA4B,YAAY,OAAO,WAAW;;;;;;;;;;;;;;;;;;QAIlF;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BACZ,kBAAkB,OAAO,SAAS,EAAE,OAAO,WAAW;;;;;;QAG7D;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAK,WAAW,OAAO,eAAe;;;;;;sCACvC,6LAAC;sCAAI;;;;;;sCACL,6LAAC;sCAAK,WAAW,OAAO,aAAa;;;;;;;;;;;;QAG3C;QACA;YACE,KAAK;YACL,OAAO;YACP,QAAQ,CAAC,GAAG,SAAsB,kBAAkB;QACtD;QACA;YACE,KAAK;YACL,OAAO;YACP,OAAO;YACP,QAAQ,CAAC,GAAG,uBACV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,WAAW,QAAQ;sCAElC,cAAA,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,WAAW,QAAQ;sCAElC,cAAA,6LAAC,8MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAElB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,eAAe;oCAAE,MAAM;oCAAM,WAAW;gCAAO;sCAE9D,cAAA,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;QAI1B;KACD;IAED,OAAO;IACP,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,IAAI,EAAE;QAChC,qBACE,6LAAC,iJAAA,CAAA,aAAU;YAAC,aAAY;sBACtB,cAAA,6LAAC,sIAAA,CAAA,cAAW;gBAAC,MAAK;;;;;;;;;;;IAGxB;IAEA,OAAO;IACP,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,IAAI,EAAE;QAC9B,qBACE,6LAAC,iJAAA,CAAA,aAAU;YAAC,aAAY;sBACtB,cAAA,6LAAC,iJAAA,CAAA,gBAAa;0BACZ,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK;;;;;;sCAEd,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM;sCAAmB;;;;;;;;;;;;;;;;;;;;;;IAOpD;IAEA,qBACE,6LAAC,iJAAA,CAAA,aAAU;QAAC,aAAY;kBACtB,cAAA,6LAAC,iJAAA,CAAA,gBAAa;YACZ,OAAM;YACN,aAAY;YACZ,uBACE,6LAAC,qIAAA,CAAA,SAAM;gBAAC,SAAS,IAAM,WAAW;;kCAChC,6LAAC,qMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;8BAMrC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC,oIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO,MAAM,UAAU;wCACvB,UAAU,CAAC,IAAM,SAAS,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAW,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;wCACvC,WAAU;;;;;;;;;;;;;;;;;sCAIhB,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAc,SAAQ;sCAAU;;;;;;;;;;;;8BAMnD,6LAAC,4IAAA,CAAA,YAAS;oBACR,SAAS;oBACT,MAAM,EAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,IAAI,KAAI,EAAE;oBAC5B,SAAS,MAAM,OAAO;oBACtB,YAAY,MAAM,IAAI,GAAG;wBACvB,SAAS,MAAM,WAAW;wBAC1B,UAAU,MAAM,QAAQ;wBACxB,OAAO,MAAM,IAAI,CAAC,KAAK;wBACvB,iBAAiB;wBACjB,iBAAiB;wBACjB,UAAU;oBACZ,IAAI;oBACJ,WAAU;;;;;;8BAIZ,6LAAC,qIAAA,CAAA,SAAM;oBAAC,MAAM,YAAY,IAAI;oBAAE,cAAc;8BAC5C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,YAAY,IAAI,KAAK,YAAY;oCACjC,YAAY,IAAI,KAAK,UAAU;oCAC/B,YAAY,IAAI,KAAK,UAAU;;;;;;;0CAElC,6LAAC,qIAAA,CAAA,oBAAiB;gCAAC,WAAU;;oCAC1B,YAAY,IAAI,KAAK,YAAY;oCACjC,YAAY,IAAI,KAAK,UAAU;oCAC/B,YAAY,IAAI,KAAK,UAAU;;;;;;;4BAEjC,YAAY,IAAI,kBACf,6LAAC,mJAAA,CAAA,gBAAa;gCACZ,MAAM,YAAY,IAAI;gCACtB,aAAa,YAAY,IAAI;gCAC7B,UAAU;gCACV,UAAU;gCACV,SAAS;;;;;;;;;;;;;;;;;8BAOjB,6LAAC,gJAAA,CAAA,sBAAmB;oBAClB,MAAM,YAAY,IAAI;oBACtB,cAAc,CAAC,OAAS,eAAe,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE;4BAAK,CAAC;oBACjE,QAAQ,GAAE,yBAAA,YAAY,SAAS,cAArB,6CAAA,uBAAuB,aAAa;oBAC9C,UAAS;oBACT,WAAW,IAAM,YAAY,SAAS,IAAI,aAAa,YAAY,SAAS;;;;;;;;;;;;;;;;;AAKtF;GA1YwB;KAAA", "debugId": null}}]}