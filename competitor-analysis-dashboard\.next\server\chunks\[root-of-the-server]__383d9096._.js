module.exports = {

"[project]/.next-internal/server/app/api/promotions/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/process [external] (process, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("process", () => require("process"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/timers [external] (timers, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("timers", () => require("timers"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 数据库连接配置和连接池管理
 * 
 * 功能说明：
 * 1. 创建MySQL连接池，支持高并发访问
 * 2. 提供数据库连接的获取和释放机制
 * 3. 实现连接错误处理和重连机制
 * 4. 提供数据库健康检查功能
 */ __turbopack_context__.s({
    "checkDatabaseHealth": ()=>checkDatabaseHealth,
    "closeDatabasePool": ()=>closeDatabasePool,
    "createConnectionPool": ()=>createConnectionPool,
    "default": ()=>__TURBOPACK__default__export__,
    "executeQuery": ()=>executeQuery,
    "executeTransaction": ()=>executeTransaction,
    "getConnectionPool": ()=>getConnectionPool
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mysql2$2f$promise$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mysql2/promise.js [app-route] (ecmascript)");
;
// 从环境变量获取数据库配置
const getDatabaseConfig = ()=>{
    const requiredEnvVars = [
        'DB_HOST',
        'DB_USER',
        'DB_PASSWORD',
        'DB_NAME'
    ];
    // 检查必需的环境变量
    for (const envVar of requiredEnvVars){
        if (!process.env[envVar]) {
            throw new Error(`缺少必需的环境变量: ${envVar}`);
        }
    }
    return {
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT || '3306'),
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10'),
        queueLimit: parseInt(process.env.DB_QUEUE_LIMIT || '0'),
        timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
        acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000')
    };
};
// 全局连接池变量
let connectionPool = null;
const createConnectionPool = ()=>{
    if (connectionPool) {
        return connectionPool;
    }
    try {
        const config = getDatabaseConfig();
        console.log('🔄 正在创建数据库连接池...');
        console.log(`📍 数据库地址: ${config.host}:${config.port}`);
        console.log(`🗄️  数据库名称: ${config.database}`);
        console.log(`👤 用户名: ${config.user}`);
        console.log(`🔗 连接池大小: ${config.connectionLimit}`);
        connectionPool = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mysql2$2f$promise$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].createPool({
            host: config.host,
            port: config.port,
            user: config.user,
            password: config.password,
            database: config.database,
            waitForConnections: true,
            connectionLimit: config.connectionLimit,
            queueLimit: config.queueLimit,
            timeout: config.timeout,
            acquireTimeout: config.acquireTimeout,
            // 启用多语句查询
            multipleStatements: false,
            // 字符集设置
            charset: 'utf8mb4',
            // 时区设置
            timezone: '+08:00',
            // 连接保活设置
            keepAliveInitialDelay: 0,
            enableKeepAlive: true
        });
        console.log('✅ 数据库连接池创建成功');
        return connectionPool;
    } catch (error) {
        console.error('❌ 创建数据库连接池失败:', error);
        throw new Error(`数据库连接池创建失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
};
const getConnectionPool = ()=>{
    if (!connectionPool) {
        return createConnectionPool();
    }
    return connectionPool;
};
const executeQuery = async (query, params = [])=>{
    const pool = getConnectionPool();
    try {
        console.log('🔍 执行SQL查询:', query);
        console.log('📝 查询参数:', params);
        const [rows] = await pool.execute(query, params);
        console.log('✅ 查询执行成功');
        return rows;
    } catch (error) {
        console.error('❌ 数据库查询失败:', error);
        console.error('🔍 失败的SQL:', query);
        console.error('📝 查询参数:', params);
        throw new Error(`数据库查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
};
const executeTransaction = async (callback)=>{
    const pool = getConnectionPool();
    const connection = await pool.getConnection();
    try {
        console.log('🔄 开始数据库事务');
        await connection.beginTransaction();
        const result = await callback(connection);
        await connection.commit();
        console.log('✅ 事务提交成功');
        return result;
    } catch (error) {
        console.error('❌ 事务执行失败，正在回滚:', error);
        await connection.rollback();
        throw error;
    } finally{
        connection.release();
        console.log('🔄 数据库连接已释放');
    }
};
const checkDatabaseHealth = async ()=>{
    try {
        console.log('🏥 检查数据库连接健康状态...');
        const result = await executeQuery('SELECT 1 as health_check');
        if (result && result.length > 0) {
            console.log('✅ 数据库连接健康');
            return true;
        } else {
            console.log('⚠️ 数据库连接异常');
            return false;
        }
    } catch (error) {
        console.error('❌ 数据库健康检查失败:', error);
        return false;
    }
};
const closeDatabasePool = async ()=>{
    if (connectionPool) {
        try {
            console.log('🔄 正在关闭数据库连接池...');
            await connectionPool.end();
            connectionPool = null;
            console.log('✅ 数据库连接池已关闭');
        } catch (error) {
            console.error('❌ 关闭数据库连接池失败:', error);
            throw error;
        }
    }
};
const __TURBOPACK__default__export__ = getConnectionPool;
}),
"[project]/src/lib/dao/base-dao.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 基础数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 提供通用的CRUD操作方法
 * 2. 实现分页查询和排序功能
 * 3. 提供事务支持和错误处理
 * 4. 作为其他DAO类的基类
 */ __turbopack_context__.s({
    "BaseDAO": ()=>BaseDAO
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
class BaseDAO {
    tableName;
    primaryKey;
    constructor(tableName, primaryKey = 'id'){
        this.tableName = tableName;
        this.primaryKey = primaryKey;
    }
    /**
   * 根据ID查询单条记录
   * @param id 记录ID
   * @returns 查询结果
   */ async findById(id) {
        try {
            console.log(`🔍 查询${this.tableName}表中ID为${id}的记录`);
            const query = `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                id
            ]);
            if (results.length === 0) {
                console.log(`⚠️ 未找到ID为${id}的记录`);
                return null;
            }
            console.log(`✅ 成功查询到记录`);
            return results[0];
        } catch (error) {
            console.error(`❌ 查询${this.tableName}记录失败:`, error);
            throw new Error(`查询记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 查询所有记录
   * @param orderBy 排序字段
   * @param orderDirection 排序方向
   * @returns 查询结果列表
   */ async findAll(orderBy = this.primaryKey, orderDirection = 'ASC') {
        try {
            console.log(`🔍 查询${this.tableName}表的所有记录`);
            const query = `SELECT * FROM ${this.tableName} ORDER BY ${orderBy} ${orderDirection}`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
            console.log(`✅ 成功查询到${results.length}条记录`);
            return results;
        } catch (error) {
            console.error(`❌ 查询${this.tableName}所有记录失败:`, error);
            throw new Error(`查询记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 分页查询记录
   * @param params 分页参数
   * @param whereClause WHERE子句
   * @param whereParams WHERE参数
   * @returns 分页查询结果
   */ async findWithPagination(params, whereClause = '', whereParams = []) {
        try {
            const { page, pageSize, sortBy = this.primaryKey, sortOrder = 'ASC' } = params;
            const offset = (page - 1) * pageSize;
            console.log(`🔍 分页查询${this.tableName}表 - 第${page}页，每页${pageSize}条`);
            // 构建WHERE子句
            const whereSQL = whereClause ? `WHERE ${whereClause}` : '';
            // 查询总数
            const countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereSQL}`;
            const countResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(countQuery, whereParams);
            const total = countResults[0].total;
            // 查询数据 - 使用字符串拼接避免参数类型问题
            const dataQuery = `
        SELECT * FROM ${this.tableName}
        ${whereSQL}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ${Number(pageSize)} OFFSET ${Number(offset)}
      `;
            const dataResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(dataQuery, whereParams);
            const totalPages = Math.ceil(total / pageSize);
            console.log(`✅ 分页查询成功 - 总计${total}条记录，当前页${dataResults.length}条`);
            return {
                data: dataResults,
                total,
                page,
                pageSize,
                totalPages
            };
        } catch (error) {
            console.error(`❌ 分页查询${this.tableName}失败:`, error);
            throw new Error(`分页查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 创建新记录
   * @param data 创建数据
   * @returns 创建的记录ID
   */ async create(data) {
        try {
            console.log(`➕ 创建${this.tableName}新记录`);
            console.log('📝 创建数据:', data);
            const fields = Object.keys(data);
            const values = Object.values(data);
            const placeholders = fields.map(()=>'?').join(', ');
            const query = `
        INSERT INTO ${this.tableName} (${fields.join(', ')}) 
        VALUES (${placeholders})
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, values);
            const insertId = results.insertId;
            console.log(`✅ 成功创建记录，ID: ${insertId}`);
            return insertId;
        } catch (error) {
            console.error(`❌ 创建${this.tableName}记录失败:`, error);
            throw new Error(`创建记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 更新记录
   * @param id 记录ID
   * @param data 更新数据
   * @returns 是否更新成功
   */ async update(id, data) {
        try {
            console.log(`✏️ 更新${this.tableName}记录，ID: ${id}`);
            console.log('📝 更新数据:', data);
            const fields = Object.keys(data);
            const values = Object.values(data);
            if (fields.length === 0) {
                console.log('⚠️ 没有需要更新的字段');
                return false;
            }
            const setClause = fields.map((field)=>`${field} = ?`).join(', ');
            const query = `UPDATE ${this.tableName} SET ${setClause} WHERE ${this.primaryKey} = ?`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                ...values,
                id
            ]);
            const affectedRows = results.affectedRows;
            if (affectedRows === 0) {
                console.log(`⚠️ 未找到ID为${id}的记录或数据未发生变化`);
                return false;
            }
            console.log(`✅ 成功更新记录，影响行数: ${affectedRows}`);
            return true;
        } catch (error) {
            console.error(`❌ 更新${this.tableName}记录失败:`, error);
            throw new Error(`更新记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 删除记录
   * @param id 记录ID
   * @returns 是否删除成功
   */ async delete(id) {
        try {
            console.log(`🗑️ 删除${this.tableName}记录，ID: ${id}`);
            const query = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                id
            ]);
            const affectedRows = results.affectedRows;
            if (affectedRows === 0) {
                console.log(`⚠️ 未找到ID为${id}的记录`);
                return false;
            }
            console.log(`✅ 成功删除记录，影响行数: ${affectedRows}`);
            return true;
        } catch (error) {
            console.error(`❌ 删除${this.tableName}记录失败:`, error);
            throw new Error(`删除记录失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 批量删除记录
   * @param ids 记录ID列表
   * @returns 删除的记录数量
   */ async batchDelete(ids) {
        try {
            if (ids.length === 0) {
                console.log('⚠️ 没有需要删除的记录');
                return 0;
            }
            console.log(`🗑️ 批量删除${this.tableName}记录，数量: ${ids.length}`);
            const placeholders = ids.map(()=>'?').join(', ');
            const query = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} IN (${placeholders})`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, ids);
            const affectedRows = results.affectedRows;
            console.log(`✅ 成功删除${affectedRows}条记录`);
            return affectedRows;
        } catch (error) {
            console.error(`❌ 批量删除${this.tableName}记录失败:`, error);
            throw new Error(`批量删除失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 检查记录是否存在
   * @param id 记录ID
   * @returns 是否存在
   */ async exists(id) {
        try {
            const query = `SELECT 1 FROM ${this.tableName} WHERE ${this.primaryKey} = ? LIMIT 1`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                id
            ]);
            return results.length > 0;
        } catch (error) {
            console.error(`❌ 检查${this.tableName}记录存在性失败:`, error);
            throw new Error(`检查记录存在性失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取记录总数
   * @param whereClause WHERE子句
   * @param whereParams WHERE参数
   * @returns 记录总数
   */ async count(whereClause = '', whereParams = []) {
        try {
            const whereSQL = whereClause ? `WHERE ${whereClause}` : '';
            const query = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereSQL}`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, whereParams);
            return results[0].total;
        } catch (error) {
            console.error(`❌ 统计${this.tableName}记录数量失败:`, error);
            throw new Error(`统计记录数量失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 执行事务操作
   * @param callback 事务回调函数
   * @returns 事务执行结果
   */ async executeInTransaction(callback) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeTransaction"])(callback);
    }
}
}),
"[project]/src/lib/dao/competitor-dao.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 竞品数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 实现竞品表的CRUD操作
 * 2. 提供竞品特有的查询方法
 * 3. 支持按城市、园区类型等条件筛选
 * 4. 提供竞品活跃状态管理
 */ __turbopack_context__.s({
    "CompetitorDAO": ()=>CompetitorDAO,
    "competitorDAO": ()=>competitorDAO
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/base-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
class CompetitorDAO extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseDAO"] {
    constructor(){
        super('Competitors', 'competitor_id');
    }
    /**
   * 根据筛选条件查询竞品列表（分页）
   * @param params 分页参数
   * @param filters 筛选条件
   * @returns 分页查询结果
   */ async findWithFilters(params, filters = {}) {
        try {
            console.log('🔍 根据筛选条件查询竞品列表');
            console.log('📝 筛选条件:', filters);
            const whereConditions = [];
            const whereParams = [];
            // 按竞品名称搜索
            if (filters.competitor_name) {
                whereConditions.push('competitor_name LIKE ?');
                whereParams.push(`%${filters.competitor_name}%`);
            }
            // 按城市筛选
            if (filters.city) {
                whereConditions.push('city = ?');
                whereParams.push(filters.city);
            }
            // 按园区类型筛选
            if (filters.park_type) {
                whereConditions.push('park_type = ?');
                whereParams.push(filters.park_type);
            }
            // 按活跃状态筛选
            if (filters.is_active !== undefined) {
                whereConditions.push('is_active = ?');
                whereParams.push(filters.is_active);
            }
            const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';
            return await this.findWithPagination(params, whereClause, whereParams);
        } catch (error) {
            console.error('❌ 根据筛选条件查询竞品失败:', error);
            throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据竞品名称查询（精确匹配）
   * @param competitorName 竞品名称
   * @returns 竞品信息
   */ async findByName(competitorName) {
        try {
            console.log(`🔍 查询竞品名称为"${competitorName}"的记录`);
            const query = 'SELECT * FROM Competitors WHERE competitor_name = ?';
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                competitorName
            ]);
            if (results.length === 0) {
                console.log(`⚠️ 未找到名称为"${competitorName}"的竞品`);
                return null;
            }
            console.log('✅ 成功查询到竞品信息');
            return results[0];
        } catch (error) {
            console.error('❌ 根据名称查询竞品失败:', error);
            throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取所有活跃的竞品
   * @returns 活跃竞品列表
   */ async findActiveCompetitors() {
        try {
            console.log('🔍 查询所有活跃的竞品');
            const query = `
        SELECT * FROM Competitors 
        WHERE is_active = true 
        ORDER BY competitor_name ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
            console.log(`✅ 成功查询到${results.length}个活跃竞品`);
            return results;
        } catch (error) {
            console.error('❌ 查询活跃竞品失败:', error);
            throw new Error(`查询活跃竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据城市获取竞品列表
   * @param city 城市名称
   * @returns 竞品列表
   */ async findByCity(city) {
        try {
            console.log(`🔍 查询城市"${city}"的竞品列表`);
            const query = `
        SELECT * FROM Competitors 
        WHERE city = ? 
        ORDER BY competitor_name ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                city
            ]);
            console.log(`✅ 成功查询到${results.length}个竞品`);
            return results;
        } catch (error) {
            console.error('❌ 根据城市查询竞品失败:', error);
            throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据园区类型获取竞品列表
   * @param parkType 园区类型
   * @returns 竞品列表
   */ async findByParkType(parkType) {
        try {
            console.log(`🔍 查询园区类型"${parkType}"的竞品列表`);
            const query = `
        SELECT * FROM Competitors 
        WHERE park_type = ? 
        ORDER BY competitor_name ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                parkType
            ]);
            console.log(`✅ 成功查询到${results.length}个竞品`);
            return results;
        } catch (error) {
            console.error('❌ 根据园区类型查询竞品失败:', error);
            throw new Error(`查询竞品失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取所有城市列表
   * @returns 城市列表
   */ async getAllCities() {
        try {
            console.log('🔍 获取所有城市列表');
            const query = `
        SELECT DISTINCT city 
        FROM Competitors 
        WHERE city IS NOT NULL AND city != '' 
        ORDER BY city ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
            const cities = results.map((row)=>row.city);
            console.log(`✅ 成功获取${cities.length}个城市`);
            return cities;
        } catch (error) {
            console.error('❌ 获取城市列表失败:', error);
            throw new Error(`获取城市列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取所有园区类型列表
   * @returns 园区类型列表
   */ async getAllParkTypes() {
        try {
            console.log('🔍 获取所有园区类型列表');
            const query = `
        SELECT DISTINCT park_type 
        FROM Competitors 
        WHERE park_type IS NOT NULL AND park_type != '' 
        ORDER BY park_type ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
            const parkTypes = results.map((row)=>row.park_type);
            console.log(`✅ 成功获取${parkTypes.length}个园区类型`);
            return parkTypes;
        } catch (error) {
            console.error('❌ 获取园区类型列表失败:', error);
            throw new Error(`获取园区类型列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 更新竞品活跃状态
   * @param competitorId 竞品ID
   * @param isActive 是否活跃
   * @returns 是否更新成功
   */ async updateActiveStatus(competitorId, isActive) {
        try {
            console.log(`🔄 更新竞品${competitorId}的活跃状态为${isActive ? '活跃' : '非活跃'}`);
            const query = 'UPDATE Competitors SET is_active = ? WHERE competitor_id = ?';
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                isActive,
                competitorId
            ]);
            const affectedRows = results.affectedRows;
            if (affectedRows === 0) {
                console.log(`⚠️ 未找到ID为${competitorId}的竞品`);
                return false;
            }
            console.log('✅ 成功更新竞品活跃状态');
            return true;
        } catch (error) {
            console.error('❌ 更新竞品活跃状态失败:', error);
            throw new Error(`更新活跃状态失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 批量更新竞品活跃状态
   * @param competitorIds 竞品ID列表
   * @param isActive 是否活跃
   * @returns 更新的记录数量
   */ async batchUpdateActiveStatus(competitorIds, isActive) {
        try {
            if (competitorIds.length === 0) {
                console.log('⚠️ 没有需要更新的竞品');
                return 0;
            }
            console.log(`🔄 批量更新${competitorIds.length}个竞品的活跃状态`);
            const placeholders = competitorIds.map(()=>'?').join(', ');
            const query = `
        UPDATE Competitors 
        SET is_active = ? 
        WHERE competitor_id IN (${placeholders})
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                isActive,
                ...competitorIds
            ]);
            const affectedRows = results.affectedRows;
            console.log(`✅ 成功更新${affectedRows}个竞品的活跃状态`);
            return affectedRows;
        } catch (error) {
            console.error('❌ 批量更新竞品活跃状态失败:', error);
            throw new Error(`批量更新失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 检查竞品名称是否已存在
   * @param competitorName 竞品名称
   * @param excludeId 排除的竞品ID（用于更新时检查）
   * @returns 是否已存在
   */ async isNameExists(competitorName, excludeId) {
        try {
            let query = 'SELECT 1 FROM Competitors WHERE competitor_name = ?';
            const params = [
                competitorName
            ];
            if (excludeId) {
                query += ' AND competitor_id != ?';
                params.push(excludeId);
            }
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, params);
            return results.length > 0;
        } catch (error) {
            console.error('❌ 检查竞品名称是否存在失败:', error);
            throw new Error(`检查名称失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取竞品统计信息
   * @returns 统计信息
   */ async getStatistics() {
        try {
            console.log('📊 获取竞品统计信息');
            const queries = [
                'SELECT COUNT(*) as total FROM Competitors',
                'SELECT COUNT(*) as active FROM Competitors WHERE is_active = true',
                'SELECT COUNT(*) as inactive FROM Competitors WHERE is_active = false',
                'SELECT COUNT(DISTINCT city) as cityCount FROM Competitors WHERE city IS NOT NULL AND city != ""',
                'SELECT COUNT(DISTINCT park_type) as parkTypeCount FROM Competitors WHERE park_type IS NOT NULL AND park_type != ""'
            ];
            const [totalResult, activeResult, inactiveResult, cityResult, parkTypeResult] = await Promise.all(queries.map((query)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query)));
            const statistics = {
                total: totalResult[0].total,
                active: activeResult[0].active,
                inactive: inactiveResult[0].inactive,
                cityCount: cityResult[0].cityCount,
                parkTypeCount: parkTypeResult[0].parkTypeCount
            };
            console.log('✅ 成功获取竞品统计信息:', statistics);
            return statistics;
        } catch (error) {
            console.error('❌ 获取竞品统计信息失败:', error);
            throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
}
const competitorDAO = new CompetitorDAO();
}),
"[project]/src/lib/dao/ticket-type-dao.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 票种数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 实现票种表的CRUD操作
 * 2. 提供票种特有的查询方法
 * 3. 支持按分类筛选票种
 * 4. 提供票种统计和分析功能
 */ __turbopack_context__.s({
    "TicketTypeDAO": ()=>TicketTypeDAO,
    "ticketTypeDAO": ()=>ticketTypeDAO
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/base-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
class TicketTypeDAO extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseDAO"] {
    constructor(){
        super('TicketTypes', 'ticket_type_id');
    }
    /**
   * 根据筛选条件查询票种列表（分页）
   * @param params 分页参数
   * @param filters 筛选条件
   * @returns 分页查询结果
   */ async findWithFilters(params, filters = {}) {
        try {
            console.log('🔍 根据筛选条件查询票种列表');
            console.log('📝 筛选条件:', filters);
            const whereConditions = [];
            const whereParams = [];
            // 按票种名称搜索
            if (filters.ticket_type_name) {
                whereConditions.push('ticket_type_name LIKE ?');
                whereParams.push(`%${filters.ticket_type_name}%`);
            }
            // 按分类筛选
            if (filters.category) {
                whereConditions.push('category = ?');
                whereParams.push(filters.category);
            }
            const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';
            return await this.findWithPagination(params, whereClause, whereParams);
        } catch (error) {
            console.error('❌ 根据筛选条件查询票种失败:', error);
            throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据票种名称查询（精确匹配）
   * @param ticketTypeName 票种名称
   * @returns 票种信息
   */ async findByName(ticketTypeName) {
        try {
            console.log(`🔍 查询票种名称为"${ticketTypeName}"的记录`);
            const query = 'SELECT * FROM TicketTypes WHERE ticket_type_name = ?';
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                ticketTypeName
            ]);
            if (results.length === 0) {
                console.log(`⚠️ 未找到名称为"${ticketTypeName}"的票种`);
                return null;
            }
            console.log('✅ 成功查询到票种信息');
            return results[0];
        } catch (error) {
            console.error('❌ 根据名称查询票种失败:', error);
            throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据分类获取票种列表
   * @param category 票种分类
   * @returns 票种列表
   */ async findByCategory(category) {
        try {
            console.log(`🔍 查询分类"${category}"的票种列表`);
            const query = `
        SELECT * FROM TicketTypes 
        WHERE category = ? 
        ORDER BY ticket_type_name ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                category
            ]);
            console.log(`✅ 成功查询到${results.length}个票种`);
            return results;
        } catch (error) {
            console.error('❌ 根据分类查询票种失败:', error);
            throw new Error(`查询票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取所有票种分类列表
   * @returns 分类列表
   */ async getAllCategories() {
        try {
            console.log('🔍 获取所有票种分类列表');
            const query = `
        SELECT DISTINCT category 
        FROM TicketTypes 
        WHERE category IS NOT NULL AND category != '' 
        ORDER BY category ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
            const categories = results.map((row)=>row.category);
            console.log(`✅ 成功获取${categories.length}个分类`);
            return categories;
        } catch (error) {
            console.error('❌ 获取分类列表失败:', error);
            throw new Error(`获取分类列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 搜索票种（模糊匹配票种名称）
   * @param keyword 搜索关键词
   * @param limit 返回结果数量限制
   * @returns 票种列表
   */ async searchByKeyword(keyword, limit = 10) {
        try {
            console.log(`🔍 搜索票种，关键词: "${keyword}"`);
            const query = `
        SELECT * FROM TicketTypes 
        WHERE ticket_type_name LIKE ? OR category LIKE ?
        ORDER BY 
          CASE 
            WHEN ticket_type_name = ? THEN 1
            WHEN ticket_type_name LIKE ? THEN 2
            ELSE 3
          END,
          ticket_type_name ASC
        LIMIT ?
      `;
            const searchPattern = `%${keyword}%`;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                searchPattern,
                searchPattern,
                keyword,
                `${keyword}%`,
                limit
            ]);
            console.log(`✅ 搜索到${results.length}个票种`);
            return results;
        } catch (error) {
            console.error('❌ 搜索票种失败:', error);
            throw new Error(`搜索票种失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 检查票种名称是否已存在
   * @param ticketTypeName 票种名称
   * @param excludeId 排除的票种ID（用于更新时检查）
   * @returns 是否已存在
   */ async isNameExists(ticketTypeName, excludeId) {
        try {
            let query = 'SELECT 1 FROM TicketTypes WHERE ticket_type_name = ?';
            const params = [
                ticketTypeName
            ];
            if (excludeId) {
                query += ' AND ticket_type_id != ?';
                params.push(excludeId);
            }
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, params);
            return results.length > 0;
        } catch (error) {
            console.error('❌ 检查票种名称是否存在失败:', error);
            throw new Error(`检查名称失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取票种使用统计（关联促销活动表）
   * @param ticketTypeId 票种ID
   * @returns 使用统计信息
   */ async getUsageStatistics(ticketTypeId) {
        try {
            console.log(`📊 获取票种${ticketTypeId}的使用统计`);
            const query = `
        SELECT 
          COUNT(*) as promotionCount,
          AVG(promo_price) as avgPrice,
          MIN(promo_price) as minPrice,
          MAX(promo_price) as maxPrice,
          MAX(entry_date) as latestPromotion
        FROM Promotions 
        WHERE ticket_type_id = ? AND promo_price IS NOT NULL
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                ticketTypeId
            ]);
            const statistics = results[0] || {
                promotionCount: 0,
                avgPrice: 0,
                minPrice: 0,
                maxPrice: 0,
                latestPromotion: undefined
            };
            console.log('✅ 成功获取票种使用统计:', statistics);
            return statistics;
        } catch (error) {
            console.error('❌ 获取票种使用统计失败:', error);
            throw new Error(`获取使用统计失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取热门票种排行榜
   * @param limit 返回数量限制
   * @returns 热门票种列表
   */ async getPopularTicketTypes(limit = 10) {
        try {
            console.log(`📊 获取热门票种排行榜，前${limit}名`);
            const query = `
        SELECT 
          tt.ticket_type_id,
          tt.ticket_type_name,
          tt.category,
          COUNT(p.promotion_id) as promotion_count,
          AVG(p.promo_price) as avg_price
        FROM TicketTypes tt
        LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id
        GROUP BY tt.ticket_type_id, tt.ticket_type_name, tt.category
        ORDER BY promotion_count DESC, avg_price DESC
        LIMIT ?
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                limit
            ]);
            console.log(`✅ 成功获取${results.length}个热门票种`);
            return results;
        } catch (error) {
            console.error('❌ 获取热门票种排行榜失败:', error);
            throw new Error(`获取排行榜失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取票种统计信息
   * @returns 统计信息
   */ async getStatistics() {
        try {
            console.log('📊 获取票种统计信息');
            const queries = [
                'SELECT COUNT(*) as total FROM TicketTypes',
                'SELECT COUNT(DISTINCT category) as categoryCount FROM TicketTypes WHERE category IS NOT NULL AND category != ""',
                'SELECT COUNT(DISTINCT tt.ticket_type_id) as withPromotions FROM TicketTypes tt INNER JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id',
                'SELECT COUNT(*) as withoutPromotions FROM TicketTypes tt LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id WHERE p.ticket_type_id IS NULL'
            ];
            const [totalResult, categoryResult, withPromotionsResult, withoutPromotionsResult] = await Promise.all(queries.map((query)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query)));
            const statistics = {
                total: totalResult[0].total,
                categoryCount: categoryResult[0].categoryCount,
                withPromotions: withPromotionsResult[0].withPromotions,
                withoutPromotions: withoutPromotionsResult[0].withoutPromotions
            };
            console.log('✅ 成功获取票种统计信息:', statistics);
            return statistics;
        } catch (error) {
            console.error('❌ 获取票种统计信息失败:', error);
            throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取分类统计信息
   * @returns 分类统计列表
   */ async getCategoryStatistics() {
        try {
            console.log('📊 获取分类统计信息');
            const query = `
        SELECT 
          COALESCE(tt.category, '未分类') as category,
          COUNT(DISTINCT tt.ticket_type_id) as ticket_count,
          COUNT(p.promotion_id) as promotion_count,
          AVG(p.promo_price) as avg_price
        FROM TicketTypes tt
        LEFT JOIN Promotions p ON tt.ticket_type_id = p.ticket_type_id
        GROUP BY tt.category
        ORDER BY ticket_count DESC, promotion_count DESC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
            console.log(`✅ 成功获取${results.length}个分类的统计信息`);
            return results;
        } catch (error) {
            console.error('❌ 获取分类统计信息失败:', error);
            throw new Error(`获取分类统计失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
}
const ticketTypeDAO = new TicketTypeDAO();
}),
"[project]/src/lib/dao/promotion-dao.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 促销活动数据访问对象(DAO)类
 * 
 * 功能说明：
 * 1. 实现促销活动表的CRUD操作
 * 2. 提供复杂的关联查询（竞品、票种）
 * 3. 支持多维度筛选和搜索
 * 4. 提供促销活动统计和分析功能
 */ __turbopack_context__.s({
    "PromotionDAO": ()=>PromotionDAO,
    "promotionDAO": ()=>promotionDAO
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/base-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
class PromotionDAO extends __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["BaseDAO"] {
    constructor(){
        super('Promotions', 'promotion_id');
    }
    /**
   * 根据筛选条件查询促销活动列表（分页，包含关联数据）
   * @param params 分页参数
   * @param filters 筛选条件
   * @returns 分页查询结果
   */ async findWithFilters(params, filters = {}) {
        try {
            console.log('🔍 根据筛选条件查询促销活动列表');
            console.log('📝 筛选条件:', filters);
            const whereConditions = [];
            const whereParams = [];
            // 按竞品筛选
            if (filters.competitor_id) {
                whereConditions.push('p.competitor_id = ?');
                whereParams.push(filters.competitor_id);
            }
            // 按票种筛选
            if (filters.ticket_type_id) {
                whereConditions.push('p.ticket_type_id = ?');
                whereParams.push(filters.ticket_type_id);
            }
            // 按活动名称搜索
            if (filters.activity_name) {
                whereConditions.push('p.activity_name LIKE ?');
                whereParams.push(`%${filters.activity_name}%`);
            }
            // 按销售渠道筛选
            if (filters.sales_channel) {
                whereConditions.push('p.sales_channel LIKE ?');
                whereParams.push(`%${filters.sales_channel}%`);
            }
            // 按销售日期范围筛选
            if (filters.sale_date_start) {
                whereConditions.push('p.sale_start_date >= ?');
                whereParams.push(filters.sale_date_start);
            }
            if (filters.sale_date_end) {
                whereConditions.push('p.sale_end_date <= ?');
                whereParams.push(filters.sale_date_end);
            }
            // 按使用日期范围筛选
            if (filters.use_date_start) {
                whereConditions.push('p.use_start_date >= ?');
                whereParams.push(filters.use_date_start);
            }
            if (filters.use_date_end) {
                whereConditions.push('p.use_end_date <= ?');
                whereParams.push(filters.use_date_end);
            }
            // 按价格范围筛选
            if (filters.min_price) {
                whereConditions.push('p.promo_price >= ?');
                whereParams.push(filters.min_price);
            }
            if (filters.max_price) {
                whereConditions.push('p.promo_price <= ?');
                whereParams.push(filters.max_price);
            }
            const whereClause = whereConditions.length > 0 ? whereConditions.join(' AND ') : '';
            return await this.findWithPaginationAndJoins(params, whereClause, whereParams);
        } catch (error) {
            console.error('❌ 根据筛选条件查询促销活动失败:', error);
            throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 分页查询促销活动（包含关联的竞品和票种信息）
   * @param params 分页参数
   * @param whereClause WHERE子句
   * @param whereParams WHERE参数
   * @returns 分页查询结果
   */ async findWithPaginationAndJoins(params, whereClause = '', whereParams = []) {
        try {
            const { page, pageSize, sortBy = 'p.promotion_id', sortOrder = 'DESC' } = params;
            const offset = (page - 1) * pageSize;
            // 构建WHERE子句
            const whereSQL = whereClause ? `WHERE ${whereClause}` : '';
            // 查询总数
            const countQuery = `
        SELECT COUNT(*) as total 
        FROM Promotions p
        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id
        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id
        ${whereSQL}
      `;
            const countResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(countQuery, whereParams);
            const total = countResults[0].total;
            // 查询数据（包含关联信息）
            const dataQuery = `
        SELECT
          p.*,
          c.competitor_name,
          c.city,
          c.park_type,
          c.is_active as competitor_is_active,
          tt.ticket_type_name,
          tt.category as ticket_category,
          CASE
            WHEN p.rack_rate > 0 AND p.promo_price > 0
            THEN ROUND((p.rack_rate - p.promo_price) / p.rack_rate, 4)
            ELSE NULL
          END as discount_rate
        FROM Promotions p
        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id
        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id
        ${whereSQL}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ${pageSize} OFFSET ${offset}
      `;
            const dataResults = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(dataQuery, whereParams);
            // 转换数据格式
            const promotions = dataResults.map((row)=>({
                    promotion_id: row.promotion_id,
                    competitor_id: row.competitor_id,
                    ticket_type_id: row.ticket_type_id,
                    activity_name: row.activity_name,
                    rack_rate: row.rack_rate,
                    promo_price: row.promo_price,
                    sale_start_date: row.sale_start_date,
                    sale_end_date: row.sale_end_date,
                    use_start_date: row.use_start_date,
                    use_end_date: row.use_end_date,
                    sales_channel: row.sales_channel,
                    usage_rules: row.usage_rules,
                    data_source_url: row.data_source_url,
                    entry_date: row.entry_date,
                    remarks: row.remarks,
                    competitor: row.competitor_name ? {
                        competitor_id: row.competitor_id,
                        competitor_name: row.competitor_name,
                        city: row.city,
                        park_type: row.park_type,
                        is_active: row.competitor_is_active
                    } : undefined,
                    ticket_type: row.ticket_type_name ? {
                        ticket_type_id: row.ticket_type_id,
                        ticket_type_name: row.ticket_type_name,
                        category: row.ticket_category
                    } : undefined,
                    discount_rate: row.discount_rate
                }));
            const totalPages = Math.ceil(total / pageSize);
            console.log(`✅ 分页查询成功 - 总计${total}条记录，当前页${promotions.length}条`);
            return {
                data: promotions,
                total,
                page,
                pageSize,
                totalPages
            };
        } catch (error) {
            console.error('❌ 分页查询促销活动失败:', error);
            throw new Error(`分页查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据ID查询促销活动详情（包含关联数据）
   * @param promotionId 促销活动ID
   * @returns 促销活动详情
   */ async findDetailById(promotionId) {
        try {
            console.log(`🔍 查询促销活动详情，ID: ${promotionId}`);
            const query = `
        SELECT 
          p.*,
          c.competitor_name,
          c.city,
          c.park_type,
          c.is_active as competitor_is_active,
          tt.ticket_type_name,
          tt.category as ticket_category,
          CASE 
            WHEN p.rack_rate > 0 AND p.promo_price > 0 
            THEN ROUND((p.rack_rate - p.promo_price) / p.rack_rate, 4)
            ELSE NULL 
          END as discount_rate
        FROM Promotions p
        LEFT JOIN Competitors c ON p.competitor_id = c.competitor_id
        LEFT JOIN TicketTypes tt ON p.ticket_type_id = tt.ticket_type_id
        WHERE p.promotion_id = ?
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                promotionId
            ]);
            if (results.length === 0) {
                console.log(`⚠️ 未找到ID为${promotionId}的促销活动`);
                return null;
            }
            const row = results[0];
            const promotion = {
                promotion_id: row.promotion_id,
                competitor_id: row.competitor_id,
                ticket_type_id: row.ticket_type_id,
                activity_name: row.activity_name,
                rack_rate: row.rack_rate,
                promo_price: row.promo_price,
                sale_start_date: row.sale_start_date,
                sale_end_date: row.sale_end_date,
                use_start_date: row.use_start_date,
                use_end_date: row.use_end_date,
                sales_channel: row.sales_channel,
                usage_rules: row.usage_rules,
                data_source_url: row.data_source_url,
                entry_date: row.entry_date,
                remarks: row.remarks,
                competitor: row.competitor_name ? {
                    competitor_id: row.competitor_id,
                    competitor_name: row.competitor_name,
                    city: row.city,
                    park_type: row.park_type,
                    is_active: row.competitor_is_active
                } : undefined,
                ticket_type: row.ticket_type_name ? {
                    ticket_type_id: row.ticket_type_id,
                    ticket_type_name: row.ticket_type_name,
                    category: row.ticket_category
                } : undefined,
                discount_rate: row.discount_rate
            };
            console.log('✅ 成功查询到促销活动详情');
            return promotion;
        } catch (error) {
            console.error('❌ 查询促销活动详情失败:', error);
            throw new Error(`查询详情失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据竞品ID查询促销活动列表
   * @param competitorId 竞品ID
   * @param limit 返回数量限制
   * @returns 促销活动列表
   */ async findByCompetitorId(competitorId, limit = 50) {
        try {
            console.log(`🔍 查询竞品${competitorId}的促销活动列表`);
            const params = {
                page: 1,
                pageSize: limit,
                sortBy: 'p.entry_date',
                sortOrder: 'DESC'
            };
            const result = await this.findWithPaginationAndJoins(params, 'p.competitor_id = ?', [
                competitorId
            ]);
            console.log(`✅ 成功查询到${result.data.length}个促销活动`);
            return result.data;
        } catch (error) {
            console.error('❌ 根据竞品ID查询促销活动失败:', error);
            throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 根据票种ID查询促销活动列表
   * @param ticketTypeId 票种ID
   * @param limit 返回数量限制
   * @returns 促销活动列表
   */ async findByTicketTypeId(ticketTypeId, limit = 50) {
        try {
            console.log(`🔍 查询票种${ticketTypeId}的促销活动列表`);
            const params = {
                page: 1,
                pageSize: limit,
                sortBy: 'p.entry_date',
                sortOrder: 'DESC'
            };
            const result = await this.findWithPaginationAndJoins(params, 'p.ticket_type_id = ?', [
                ticketTypeId
            ]);
            console.log(`✅ 成功查询到${result.data.length}个促销活动`);
            return result.data;
        } catch (error) {
            console.error('❌ 根据票种ID查询促销活动失败:', error);
            throw new Error(`查询促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取当前有效的促销活动（销售期内）
   * @param limit 返回数量限制
   * @returns 有效促销活动列表
   */ async findCurrentActivePromotions(limit = 50) {
        try {
            console.log('🔍 查询当前有效的促销活动');
            const currentDate = new Date().toISOString().split('T')[0];
            const params = {
                page: 1,
                pageSize: limit,
                sortBy: 'p.entry_date',
                sortOrder: 'DESC'
            };
            const whereClause = `
        (p.sale_start_date IS NULL OR p.sale_start_date <= ?) AND
        (p.sale_end_date IS NULL OR p.sale_end_date >= ?)
      `;
            const result = await this.findWithPaginationAndJoins(params, whereClause, [
                currentDate,
                currentDate
            ]);
            console.log(`✅ 成功查询到${result.data.length}个当前有效的促销活动`);
            return result.data;
        } catch (error) {
            console.error('❌ 查询当前有效促销活动失败:', error);
            throw new Error(`查询有效促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取即将到期的促销活动
   * @param days 提前天数
   * @param limit 返回数量限制
   * @returns 即将到期的促销活动列表
   */ async findExpiringPromotions(days = 7, limit = 50) {
        try {
            console.log(`🔍 查询${days}天内即将到期的促销活动`);
            const currentDate = new Date();
            const futureDate = new Date(currentDate.getTime() + days * 24 * 60 * 60 * 1000);
            const currentDateStr = currentDate.toISOString().split('T')[0];
            const futureDateStr = futureDate.toISOString().split('T')[0];
            const params = {
                page: 1,
                pageSize: limit,
                sortBy: 'p.sale_end_date',
                sortOrder: 'ASC'
            };
            const whereClause = `
        p.sale_end_date IS NOT NULL AND
        p.sale_end_date >= ? AND
        p.sale_end_date <= ?
      `;
            const result = await this.findWithPaginationAndJoins(params, whereClause, [
                currentDateStr,
                futureDateStr
            ]);
            console.log(`✅ 成功查询到${result.data.length}个即将到期的促销活动`);
            return result.data;
        } catch (error) {
            console.error('❌ 查询即将到期促销活动失败:', error);
            throw new Error(`查询即将到期促销活动失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取所有销售渠道列表
   * @returns 销售渠道列表
   */ async getAllSalesChannels() {
        try {
            console.log('🔍 获取所有销售渠道列表');
            const query = `
        SELECT DISTINCT sales_channel
        FROM Promotions
        WHERE sales_channel IS NOT NULL AND sales_channel != ''
        ORDER BY sales_channel ASC
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query);
            const channels = results.map((row)=>row.sales_channel);
            console.log(`✅ 成功获取${channels.length}个销售渠道`);
            return channels;
        } catch (error) {
            console.error('❌ 获取销售渠道列表失败:', error);
            throw new Error(`获取销售渠道列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取促销活动统计信息
   * @returns 统计信息
   */ async getStatistics() {
        try {
            console.log('📊 获取促销活动统计信息');
            const currentDate = new Date().toISOString().split('T')[0];
            const queries = [
                'SELECT COUNT(*) as total FROM Promotions',
                `SELECT COUNT(*) as activePromotions FROM Promotions WHERE (sale_start_date IS NULL OR sale_start_date <= '${currentDate}') AND (sale_end_date IS NULL OR sale_end_date >= '${currentDate}')`,
                `SELECT COUNT(*) as expiredPromotions FROM Promotions WHERE sale_end_date IS NOT NULL AND sale_end_date < '${currentDate}'`,
                'SELECT AVG(CASE WHEN rack_rate > 0 AND promo_price > 0 THEN (rack_rate - promo_price) / rack_rate ELSE NULL END) as avgDiscountRate FROM Promotions',
                'SELECT AVG(promo_price) as avgPrice FROM Promotions WHERE promo_price IS NOT NULL',
                'SELECT COUNT(DISTINCT competitor_id) as totalCompetitors FROM Promotions',
                'SELECT COUNT(DISTINCT ticket_type_id) as totalTicketTypes FROM Promotions'
            ];
            const [totalResult, activeResult, expiredResult, discountResult, priceResult, competitorResult, ticketTypeResult] = await Promise.all(queries.map((query)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query)));
            const statistics = {
                total: totalResult[0].total,
                activePromotions: activeResult[0].activePromotions,
                expiredPromotions: expiredResult[0].expiredPromotions,
                avgDiscountRate: discountResult[0].avgDiscountRate || 0,
                avgPrice: priceResult[0].avgPrice || 0,
                totalCompetitors: competitorResult[0].totalCompetitors,
                totalTicketTypes: ticketTypeResult[0].totalTicketTypes
            };
            console.log('✅ 成功获取促销活动统计信息:', statistics);
            return statistics;
        } catch (error) {
            console.error('❌ 获取促销活动统计信息失败:', error);
            throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 获取热门竞品排行榜（按促销活动数量）
   * @param limit 返回数量限制
   * @returns 热门竞品列表
   */ async getTopCompetitorsByPromotions(limit = 10) {
        try {
            console.log(`📊 获取热门竞品排行榜，前${limit}名`);
            const query = `
        SELECT
          c.competitor_id,
          c.competitor_name,
          COUNT(p.promotion_id) as promotion_count,
          AVG(p.promo_price) as avg_price,
          AVG(CASE WHEN p.rack_rate > 0 AND p.promo_price > 0 THEN (p.rack_rate - p.promo_price) / p.rack_rate ELSE NULL END) as avg_discount_rate
        FROM Competitors c
        LEFT JOIN Promotions p ON c.competitor_id = p.competitor_id
        GROUP BY c.competitor_id, c.competitor_name
        ORDER BY promotion_count DESC, avg_price DESC
        LIMIT ?
      `;
            const results = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["executeQuery"])(query, [
                limit
            ]);
            console.log(`✅ 成功获取${results.length}个热门竞品`);
            return results;
        } catch (error) {
            console.error('❌ 获取热门竞品排行榜失败:', error);
            throw new Error(`获取排行榜失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
}
const promotionDAO = new PromotionDAO();
}),
"[project]/src/lib/dao/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * 数据访问对象(DAO)统一导出文件
 * 
 * 功能说明：
 * 1. 统一导出所有DAO类和实例
 * 2. 提供DAO的中央管理
 * 3. 方便其他模块引用和使用
 */ // ============================================================================
// 导出基础DAO类
// ============================================================================
__turbopack_context__.s({
    "DAOManager": ()=>DAOManager,
    "createCompetitor": ()=>createCompetitor,
    "createPromotion": ()=>createPromotion,
    "createTicketType": ()=>createTicketType,
    "daoManager": ()=>daoManager,
    "default": ()=>__TURBOPACK__default__export__,
    "getCompetitors": ()=>getCompetitors,
    "getPromotions": ()=>getPromotions,
    "getTicketTypes": ()=>getTicketTypes
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/base-dao.ts [app-route] (ecmascript)");
// ============================================================================
// 导出具体DAO类和实例
// ============================================================================
// 竞品DAO
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$competitor$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/competitor-dao.ts [app-route] (ecmascript)");
// 票种DAO
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$ticket$2d$type$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/ticket-type-dao.ts [app-route] (ecmascript)");
// 促销活动DAO
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/promotion-dao.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
class DAOManager {
    // DAO实例 - 使用getter延迟初始化
    get competitor() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$competitor$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["competitorDAO"];
    }
    get ticketType() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$ticket$2d$type$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ticketTypeDAO"];
    }
    get promotion() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promotionDAO"];
    }
    /**
   * 获取所有DAO实例
   * @returns DAO实例对象
   */ getAllDAOs() {
        return {
            competitor: this.competitor,
            ticketType: this.ticketType,
            promotion: this.promotion
        };
    }
    /**
   * 检查所有DAO的数据库连接
   * @returns 连接状态
   */ async checkConnections() {
        try {
            console.log('🔍 检查所有DAO的数据库连接状态');
            const [competitorCheck, ticketTypeCheck, promotionCheck] = await Promise.all([
                this.competitor.exists(1).then(()=>true).catch(()=>false),
                this.ticketType.exists(1).then(()=>true).catch(()=>false),
                this.promotion.exists(1).then(()=>true).catch(()=>false)
            ]);
            const result = {
                competitor: competitorCheck,
                ticketType: ticketTypeCheck,
                promotion: promotionCheck,
                allConnected: competitorCheck && ticketTypeCheck && promotionCheck
            };
            console.log('✅ DAO连接检查完成:', result);
            return result;
        } catch (error) {
            console.error('❌ DAO连接检查失败:', error);
            return {
                competitor: false,
                ticketType: false,
                promotion: false,
                allConnected: false
            };
        }
    }
    /**
   * 获取系统整体统计信息
   * @returns 统计信息
   */ async getSystemStatistics() {
        try {
            console.log('📊 获取系统整体统计信息');
            const [competitorStats, ticketTypeStats, promotionStats] = await Promise.all([
                this.competitor.getStatistics(),
                this.ticketType.getStatistics(),
                this.promotion.getStatistics()
            ]);
            const totalRecords = competitorStats.total + ticketTypeStats.total + promotionStats.total;
            const result = {
                competitors: competitorStats,
                ticketTypes: ticketTypeStats,
                promotions: promotionStats,
                summary: {
                    totalRecords,
                    lastUpdated: new Date().toISOString()
                }
            };
            console.log('✅ 成功获取系统统计信息');
            return result;
        } catch (error) {
            console.error('❌ 获取系统统计信息失败:', error);
            throw new Error(`获取统计信息失败: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }
    /**
   * 清理和优化所有表
   * @returns 清理结果
   */ async optimizeTables() {
        try {
            console.log('🔧 开始优化数据库表');
            const details = [];
            // 这里可以添加具体的优化操作
            // 例如：清理无效数据、重建索引等
            details.push('数据库表优化功能待实现');
            console.log('✅ 数据库表优化完成');
            return {
                success: true,
                message: '数据库表优化完成',
                details
            };
        } catch (error) {
            console.error('❌ 数据库表优化失败:', error);
            return {
                success: false,
                message: `优化失败: ${error instanceof Error ? error.message : '未知错误'}`,
                details: []
            };
        }
    }
}
const daoManager = new DAOManager();
const getCompetitors = (filters, pagination)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$competitor$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["competitorDAO"].findWithFilters(pagination || {
        page: 1,
        pageSize: 10
    }, filters);
};
const getTicketTypes = (filters, pagination)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$ticket$2d$type$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ticketTypeDAO"].findWithFilters(pagination || {
        page: 1,
        pageSize: 10
    }, filters);
};
const getPromotions = (filters, pagination)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promotionDAO"].findWithFilters(pagination || {
        page: 1,
        pageSize: 10
    }, filters);
};
const createCompetitor = (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$competitor$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["competitorDAO"].create(data);
};
const createTicketType = (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$ticket$2d$type$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ticketTypeDAO"].create(data);
};
const createPromotion = (data)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promotionDAO"].create(data);
};
const __TURBOPACK__default__export__ = daoManager;
}),
"[project]/src/lib/dao/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$base$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/base-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$competitor$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/competitor-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$ticket$2d$type$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/ticket-type-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/promotion-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/dao/index.ts [app-route] (ecmascript) <locals>");
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/types/forms.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 表单相关的TypeScript类型定义
 * 
 * 功能说明：
 * 1. 定义表单字段的验证规则
 * 2. 提供表单状态管理类型
 * 3. 支持表单错误处理和用户反馈
 */ // ============================================================================
// 表单验证相关类型定义
// ============================================================================
/**
 * 表单字段验证错误接口
 */ __turbopack_context__.s({
    "BatchAction": ()=>BatchAction,
    "FormAction": ()=>FormAction,
    "FormStatus": ()=>FormStatus
});
var FormStatus = /*#__PURE__*/ function(FormStatus) {
    FormStatus["IDLE"] = "idle";
    FormStatus["LOADING"] = "loading";
    FormStatus["SUBMITTING"] = "submitting";
    FormStatus["SUCCESS"] = "success";
    FormStatus["ERROR"] = "error"; // 错误
    return FormStatus;
}({});
var FormAction = /*#__PURE__*/ function(FormAction) {
    FormAction["CREATE"] = "create";
    FormAction["UPDATE"] = "update";
    FormAction["DELETE"] = "delete";
    FormAction["VIEW"] = "view"; // 查看
    return FormAction;
}({});
var BatchAction = /*#__PURE__*/ function(BatchAction) {
    BatchAction["DELETE"] = "delete";
    BatchAction["UPDATE_STATUS"] = "update_status";
    BatchAction["EXPORT"] = "export";
    BatchAction["IMPORT"] = "import"; // 批量导入
    return BatchAction;
}({});
}),
"[project]/src/types/api.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * API相关的TypeScript类型定义
 * 
 * 功能说明：
 * 1. 定义API请求和响应的数据结构
 * 2. 提供统一的错误处理类型
 * 3. 支持API状态管理和加载状态
 */ __turbopack_context__.s({
    "ApiErrorType": ()=>ApiErrorType,
    "ApiStatus": ()=>ApiStatus,
    "HttpMethod": ()=>HttpMethod
});
var HttpMethod = /*#__PURE__*/ function(HttpMethod) {
    HttpMethod["GET"] = "GET";
    HttpMethod["POST"] = "POST";
    HttpMethod["PUT"] = "PUT";
    HttpMethod["PATCH"] = "PATCH";
    HttpMethod["DELETE"] = "DELETE";
    return HttpMethod;
}({});
var ApiErrorType = /*#__PURE__*/ function(ApiErrorType) {
    ApiErrorType["NETWORK_ERROR"] = "NETWORK_ERROR";
    ApiErrorType["VALIDATION_ERROR"] = "VALIDATION_ERROR";
    ApiErrorType["AUTHENTICATION_ERROR"] = "AUTHENTICATION_ERROR";
    ApiErrorType["AUTHORIZATION_ERROR"] = "AUTHORIZATION_ERROR";
    ApiErrorType["NOT_FOUND_ERROR"] = "NOT_FOUND_ERROR";
    ApiErrorType["SERVER_ERROR"] = "SERVER_ERROR";
    ApiErrorType["DATABASE_ERROR"] = "DATABASE_ERROR";
    ApiErrorType["UNKNOWN_ERROR"] = "UNKNOWN_ERROR"; // 未知错误
    return ApiErrorType;
}({});
var ApiStatus = /*#__PURE__*/ function(ApiStatus) {
    ApiStatus["IDLE"] = "idle";
    ApiStatus["LOADING"] = "loading";
    ApiStatus["SUCCESS"] = "success";
    ApiStatus["ERROR"] = "error"; // 错误
    return ApiStatus;
}({});
}),
"[project]/src/types/index.ts [app-route] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * TypeScript类型定义统一导出文件
 * 
 * 功能说明：
 * 1. 统一导出所有类型定义，方便其他模块引用
 * 2. 提供类型定义的中央管理
 * 3. 避免循环依赖和重复导入
 */ // ============================================================================
// 数据库相关类型导出
// ============================================================================
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$forms$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/forms.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/api.ts [app-route] (ecmascript)");
;
;
}),
"[project]/src/types/index.ts [app-route] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$forms$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/forms.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/api.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript) <locals>");
}),
"[project]/src/lib/api-utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * API工具函数
 * 
 * 功能说明：
 * 1. 提供统一的API响应格式
 * 2. 实现请求验证和错误处理
 * 3. 提供通用的API辅助函数
 * 4. 支持分页参数解析和验证
 */ __turbopack_context__.s({
    "createErrorResponse": ()=>createErrorResponse,
    "createMethodNotAllowedResponse": ()=>createMethodNotAllowedResponse,
    "createNotFoundResponse": ()=>createNotFoundResponse,
    "createSuccessResponse": ()=>createSuccessResponse,
    "createValidationErrorResponse": ()=>createValidationErrorResponse,
    "parsePaginationParams": ()=>parsePaginationParams,
    "parseQueryParams": ()=>parseQueryParams,
    "parseRequestBody": ()=>parseRequestBody,
    "validateDateFormat": ()=>validateDateFormat,
    "validateHttpMethod": ()=>validateHttpMethod,
    "validateNumberRange": ()=>validateNumberRange,
    "validateRequiredFields": ()=>validateRequiredFields,
    "validateStringLength": ()=>validateStringLength,
    "validateUrlFormat": ()=>validateUrlFormat,
    "withErrorHandling": ()=>withErrorHandling
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/types/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/api.ts [app-route] (ecmascript)");
;
;
function createSuccessResponse(data, message) {
    const response = {
        success: true,
        data,
        message,
        timestamp: new Date().toISOString()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response);
}
function createErrorResponse(error, status = 500) {
    const apiError = typeof error === 'string' ? {
        type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiErrorType"].UNKNOWN_ERROR,
        message: error,
        timestamp: new Date().toISOString()
    } : error;
    const response = {
        success: false,
        error: apiError,
        timestamp: new Date().toISOString()
    };
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(response, {
        status
    });
}
function createValidationErrorResponse(message, details) {
    const error = {
        type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiErrorType"].VALIDATION_ERROR,
        message,
        details,
        timestamp: new Date().toISOString()
    };
    return createErrorResponse(error, 400);
}
function createNotFoundResponse(resource, id) {
    const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
    const error = {
        type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiErrorType"].NOT_FOUND_ERROR,
        message,
        timestamp: new Date().toISOString()
    };
    return createErrorResponse(error, 404);
}
function parsePaginationParams(request) {
    const { searchParams } = new URL(request.url);
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'));
    const pageSize = Math.min(100, Math.max(1, parseInt(searchParams.get('pageSize') || '10')));
    const sortBy = searchParams.get('sortBy') || undefined;
    const sortOrder = searchParams.get('sortOrder')?.toUpperCase() || 'ASC';
    return {
        page,
        pageSize,
        sortBy,
        sortOrder
    };
}
function parseQueryParams(request, excludeKeys = [
    'page',
    'pageSize',
    'sortBy',
    'sortOrder'
]) {
    const { searchParams } = new URL(request.url);
    const params = {};
    for (const [key, value] of searchParams.entries()){
        if (!excludeKeys.includes(key) && value) {
            // 尝试解析数字
            if (/^\d+$/.test(value)) {
                params[key] = parseInt(value);
            } else if (value === 'true' || value === 'false') {
                params[key] = value === 'true';
            } else {
                params[key] = value;
            }
        }
    }
    return params;
}
async function parseRequestBody(request) {
    try {
        const body = await request.json();
        return body;
    } catch (error) {
        throw new Error('Invalid JSON in request body');
    }
}
function validateRequiredFields(data, requiredFields) {
    const missingFields = [];
    for (const field of requiredFields){
        if (data[field] === undefined || data[field] === null || data[field] === '') {
            missingFields.push(field);
        }
    }
    return {
        isValid: missingFields.length === 0,
        missingFields
    };
}
function validateNumberRange(value, min, max) {
    if (typeof value !== 'number' || isNaN(value)) {
        return false;
    }
    if (min !== undefined && value < min) {
        return false;
    }
    if (max !== undefined && value > max) {
        return false;
    }
    return true;
}
function validateStringLength(value, minLength, maxLength) {
    if (typeof value !== 'string') {
        return false;
    }
    if (minLength !== undefined && value.length < minLength) {
        return false;
    }
    if (maxLength !== undefined && value.length > maxLength) {
        return false;
    }
    return true;
}
function validateDateFormat(dateString) {
    if (!dateString) return false;
    const date = new Date(dateString);
    return !isNaN(date.getTime());
}
function validateUrlFormat(url) {
    if (!url) return true; // 空URL被认为是有效的（可选字段）
    try {
        new URL(url);
        return true;
    } catch  {
        return false;
    }
}
function withErrorHandling(handler) {
    return async (request, context)=>{
        try {
            return await handler(request, context);
        } catch (error) {
            console.error('API处理函数发生错误:', error);
            // 数据库错误
            if (error instanceof Error && error.message.includes('数据库')) {
                const apiError = {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiErrorType"].DATABASE_ERROR,
                    message: '数据库操作失败',
                    details: error.message,
                    timestamp: new Date().toISOString()
                };
                return createErrorResponse(apiError, 500);
            }
            // 验证错误
            if (error instanceof Error && error.message.includes('验证')) {
                const apiError = {
                    type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiErrorType"].VALIDATION_ERROR,
                    message: error.message,
                    timestamp: new Date().toISOString()
                };
                return createErrorResponse(apiError, 400);
            }
            // 通用错误
            const apiError = {
                type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiErrorType"].SERVER_ERROR,
                message: error instanceof Error ? error.message : '服务器内部错误',
                timestamp: new Date().toISOString()
            };
            return createErrorResponse(apiError, 500);
        }
    };
}
function validateHttpMethod(request, allowedMethods) {
    return allowedMethods.includes(request.method);
}
function createMethodNotAllowedResponse(allowedMethods) {
    const error = {
        type: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$api$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ApiErrorType"].VALIDATION_ERROR,
        message: `Method not allowed. Allowed methods: ${allowedMethods.join(', ')}`,
        timestamp: new Date().toISOString()
    };
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
        success: false,
        error,
        timestamp: new Date().toISOString()
    }, {
        status: 405
    });
    response.headers.set('Allow', allowedMethods.join(', '));
    return response;
}
}),
"[project]/src/app/api/promotions/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * 促销活动API路由
 * 
 * 功能说明：
 * 1. 处理促销活动的CRUD操作
 * 2. 支持复杂的分页查询和筛选
 * 3. 提供数据验证和错误处理
 * 4. 实现RESTful API规范
 */ __turbopack_context__.s({
    "DELETE": ()=>DELETE,
    "GET": ()=>GET,
    "PATCH": ()=>PATCH,
    "POST": ()=>POST,
    "PUT": ()=>PUT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$index$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/dao/index.ts [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/promotion-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$competitor$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/competitor-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$ticket$2d$type$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/dao/ticket-type-dao.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-utils.ts [app-route] (ecmascript)");
;
;
const GET = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withErrorHandling"])(async (request)=>{
    console.log('📥 GET /api/promotions - 获取促销活动列表');
    // 解析分页参数
    const paginationParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parsePaginationParams"])(request);
    console.log('📄 分页参数:', paginationParams);
    // 解析筛选参数
    const queryParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseQueryParams"])(request);
    const filters = {
        competitor_id: queryParams.competitor_id,
        ticket_type_id: queryParams.ticket_type_id,
        activity_name: queryParams.activity_name,
        sales_channel: queryParams.sales_channel,
        sale_date_start: queryParams.sale_date_start,
        sale_date_end: queryParams.sale_date_end,
        use_date_start: queryParams.use_date_start,
        use_date_end: queryParams.use_date_end,
        min_price: queryParams.min_price,
        max_price: queryParams.max_price
    };
    console.log('🔍 筛选条件:', filters);
    // 查询数据
    const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promotionDAO"].findWithFilters(paginationParams, filters);
    console.log(`✅ 成功获取促销活动列表，共${result.total}条记录`);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSuccessResponse"])(result, '获取促销活动列表成功');
});
const POST = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withErrorHandling"])(async (request)=>{
    console.log('📥 POST /api/promotions - 创建新促销活动');
    // 解析请求体
    const body = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["parseRequestBody"])(request);
    console.log('📝 创建数据:', body);
    // 验证必需字段
    const { isValid, missingFields } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateRequiredFields"])(body, [
        'competitor_id',
        'ticket_type_id',
        'activity_name'
    ]);
    if (!isValid) {
        console.log('❌ 缺少必需字段:', missingFields);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])(`缺少必需字段: ${missingFields.join(', ')}`, {
            missingFields
        });
    }
    // 验证外键关联
    const competitor = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$competitor$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["competitorDAO"].findById(body.competitor_id);
    if (!competitor) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('指定的竞品不存在');
    }
    const ticketType = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$ticket$2d$type$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ticketTypeDAO"].findById(body.ticket_type_id);
    if (!ticketType) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('指定的票种不存在');
    }
    // 验证字段格式和长度
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateStringLength"])(body.activity_name, 1, 255)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('活动名称长度必须在1-255个字符之间');
    }
    if (body.rack_rate !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateNumberRange"])(body.rack_rate, 0, 999999.99)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('门市价必须在0-999999.99之间');
    }
    if (body.promo_price !== undefined && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateNumberRange"])(body.promo_price, 0, 999999.99)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('促销价必须在0-999999.99之间');
    }
    // 验证日期格式
    if (body.sale_start_date && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateDateFormat"])(body.sale_start_date)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('销售开始日期格式无效');
    }
    if (body.sale_end_date && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateDateFormat"])(body.sale_end_date)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('销售结束日期格式无效');
    }
    if (body.use_start_date && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateDateFormat"])(body.use_start_date)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('使用开始日期格式无效');
    }
    if (body.use_end_date && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateDateFormat"])(body.use_end_date)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('使用结束日期格式无效');
    }
    // 验证日期逻辑
    if (body.sale_start_date && body.sale_end_date) {
        if (new Date(body.sale_start_date) > new Date(body.sale_end_date)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('销售开始日期不能晚于结束日期');
        }
    }
    if (body.use_start_date && body.use_end_date) {
        if (new Date(body.use_start_date) > new Date(body.use_end_date)) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('使用开始日期不能晚于结束日期');
        }
    }
    // 验证其他字段
    if (body.sales_channel && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateStringLength"])(body.sales_channel, 0, 255)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('销售渠道长度不能超过255个字符');
    }
    if (body.data_source_url && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateUrlFormat"])(body.data_source_url)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('数据来源URL格式无效');
    }
    if (body.data_source_url && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateStringLength"])(body.data_source_url, 0, 512)) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createValidationErrorResponse"])('数据来源URL长度不能超过512个字符');
    }
    // 创建促销活动
    const promotionId = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promotionDAO"].create(body);
    // 获取创建的促销活动详情
    const newPromotion = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$dao$2f$promotion$2d$dao$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["promotionDAO"].findDetailById(promotionId);
    console.log(`✅ 成功创建促销活动，ID: ${promotionId}`);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createSuccessResponse"])(newPromotion, '创建促销活动成功');
});
async function PUT(request) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMethodNotAllowedResponse"])([
        'GET',
        'POST'
    ]);
}
async function DELETE(request) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMethodNotAllowedResponse"])([
        'GET',
        'POST'
    ]);
}
async function PATCH(request) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createMethodNotAllowedResponse"])([
        'GET',
        'POST'
    ]);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__383d9096._.js.map